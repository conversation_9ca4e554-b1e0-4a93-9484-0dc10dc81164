#!/usr/bin/env python3
"""
配置文件
"""
import os

# 环境变量检查
def check_environment():
    """检查必要的环境变量"""
    required_vars = ['OPENAI_API_KEY']
    missing_vars = []
    
    for var in required_vars:
        if not os.getenv(var):
            missing_vars.append(var)
    
    if missing_vars:
        raise ValueError(f"缺少环境变量: {', '.join(missing_vars)}")
    
    # 检查代理设置（可选）
    proxy_url = os.getenv('OPENAI_PROXY')
    if proxy_url:
        print(f"[配置] 检测到代理设置: {proxy_url}")
    else:
        print("[配置] 未设置代理，将使用默认值: http://127.0.0.1:7890")
        print("[配置] 如需自定义代理，请设置环境变量: export OPENAI_PROXY=http://your-proxy:port")
    
    # 检查自定义API基础URL（可选）
    base_url = os.getenv('OPENAI_BASE_URL')
    if base_url:
        print(f"[配置] 检测到自定义API基础URL: {base_url}")
    
    return True

# 语言配置
LANGUAGE_CONFIG = {
    'chinese_to_english': {
        'source_lang': 'zh',
        'target_lang': 'en',
        'tts_voice': 'en-US-JennyNeural'
    },
    'english_to_chinese': {
        'source_lang': 'en', 
        'target_lang': 'zh',
        'tts_voice': 'zh-CN-XiaoxiaoNeural'
    },
    'chinese_to_japanese': {
        'source_lang': 'zh',
        'target_lang': 'ja', 
        'tts_voice': 'ja-JP-NanamiNeural'
    }
}

# 音频配置
AUDIO_CONFIG = {
    'sample_rate': 48000,  # OBS标准采样率
    'channels': 2,
    'blocksize': 1024,
    'whisper_sample_rate': 16000,  # Whisper需要16kHz
    'vad_frame_duration': 30,  # VAD帧长度(ms)
    'buffer_seconds': 2.0,     # 音频缓冲区长度
}

# VAD配置
VAD_CONFIG = {
    'aggressiveness': 2,       # 0-3, 越高越激进
    'min_speech_frames': 10,   # 最少连续语音帧
    'max_silence_frames': 15,  # 最大连续静音帧
    'speech_threshold': 0.8,   # 语音判定阈值
}

# API配置
API_CONFIG = {
    'openai_model': 'gpt-3.5-turbo',
    'openai_temperature': 0.3,
    'openai_max_tokens': 200,
    'translation_timeout': 5.0,
}

# OBS配置
OBS_CONFIG = {
    'host': 'localhost',
    'port': 4455,
    'subtitle_source': '字幕',  # OBS中文字源名称
    'connection_timeout': 5.0,
}

# 性能优化配置
PERFORMANCE_CONFIG = {
    'max_concurrent_translations': 3,
    'queue_maxsize': 10,
    'enable_preprocessing': True,
    'enable_postprocessing': True,
    'cache_translations': True,
}