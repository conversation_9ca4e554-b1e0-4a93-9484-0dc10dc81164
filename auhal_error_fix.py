#!/usr/bin/env python3
"""
PaMacCore AUHAL Error -10863 修复方案
完整的音频设备冲突解决方案
"""

import sounddevice as sd
import numpy as np
import threading
import time
import queue
from contextlib import contextmanager
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='[%(asctime)s] %(levelname)s: %(message)s')
logger = logging.getLogger(__name__)

class AudioDeviceManager:
    """音频设备管理器 - 防止设备冲突"""
    
    def __init__(self):
        self.active_streams = {}
        self.device_locks = {}
        self.stream_lock = threading.RLock()
        self._update_device_info()
    
    def _update_device_info(self):
        """更新设备信息"""
        try:
            self.devices = sd.query_devices()
            self.default_input = sd.default.device[0]
            self.default_output = sd.default.device[1]
            logger.info(f"检测到 {len(self.devices)} 个音频设备")
        except Exception as e:
            logger.error(f"获取设备信息失败: {e}")
            self.devices = []
    
    def get_safe_output_device(self, exclude_device_ids=None):
        """获取安全的输出设备，避免冲突"""
        if exclude_device_ids is None:
            exclude_device_ids = []
        
        # 优先级顺序
        priority_keywords = [
            ['airpods', 'bluetooth'],  # 蓝牙设备优先
            ['headphones', 'headset'],  # 耳机
            ['speaker', '扬声器'],  # 扬声器
            ['display', 'monitor'],  # 显示器音频
            ['usb', 'external'],  # 外接设备
        ]
        
        safe_devices = []
        
        for i, device in enumerate(self.devices):
            # 必须有输出通道
            if device['max_output_channels'] == 0:
                continue
            
            # 排除指定设备
            if i in exclude_device_ids:
                logger.info(f"排除设备 {i}: {device['name']}")
                continue
            
            # 排除正在使用的设备
            if i in self.active_streams:
                logger.info(f"设备 {i} 正在使用中: {device['name']}")
                continue
            
            # 排除明显的输入设备
            device_name_lower = device['name'].lower()
            input_keywords = ['mic', 'microphone', '麦克风', 'input']
            if any(keyword in device_name_lower for keyword in input_keywords):
                logger.info(f"排除输入设备 {i}: {device['name']}")
                continue
            
            safe_devices.append((i, device))
        
        if not safe_devices:
            logger.warning("未找到安全的输出设备")
            return None
        
        # 按优先级选择
        for priority_group in priority_keywords:
            for device_id, device in safe_devices:
                device_name_lower = device['name'].lower()
                for keyword in priority_group:
                    if keyword in device_name_lower:
                        logger.info(f"选择优先设备 {device_id}: {device['name']}")
                        return device_id
        
        # 使用第一个安全设备
        device_id, device = safe_devices[0]
        logger.info(f"使用第一个安全设备 {device_id}: {device['name']}")
        return device_id
    
    @contextmanager
    def reserve_device(self, device_id, stream_type):
        """预留设备使用权"""
        with self.stream_lock:
            if device_id in self.active_streams:
                raise RuntimeError(f"设备 {device_id} 已被占用: {self.active_streams[device_id]}")
            
            self.active_streams[device_id] = stream_type
            logger.info(f"预留设备 {device_id} 用于 {stream_type}")
        
        try:
            yield device_id
        finally:
            with self.stream_lock:
                if device_id in self.active_streams:
                    del self.active_streams[device_id]
                    logger.info(f"释放设备 {device_id}")

class SafeAudioPlayer:
    """安全的音频播放器"""
    
    def __init__(self, device_manager):
        self.device_manager = device_manager
        self.current_stream = None
        self.playback_queue = queue.Queue()
        self.is_playing = False
        self.playback_thread = None
        
    def play_audio_safe(self, audio_data, sample_rate=48000, exclude_devices=None):
        """安全播放音频，避免设备冲突"""
        if exclude_devices is None:
            exclude_devices = []
        
        # 获取安全的输出设备
        output_device = self.device_manager.get_safe_output_device(exclude_devices)
        if output_device is None:
            logger.warning("无可用的输出设备，跳过播放")
            return False
        
        try:
            with self.device_manager.reserve_device(output_device, "音频播放"):
                # 使用非阻塞播放避免线程阻塞
                sd.play(audio_data, sample_rate, device=output_device, blocking=False)
                
                # 等待播放完成
                duration = len(audio_data) / sample_rate
                time.sleep(min(duration + 0.5, 10.0))  # 最多等待10秒
                
                # 确保停止播放
                sd.stop()
                
                logger.info(f"音频播放完成，设备: {output_device}")
                return True
                
        except Exception as e:
            logger.error(f"音频播放失败 (设备 {output_device}): {e}")
            
            # 如果是AUHAL错误，记录详细信息
            if "-10863" in str(e):
                logger.error("检测到PaMacCore AUHAL Error -10863")
                logger.error("可能原因：音频设备冲突或上下文错误")
                
                # 尝试重置音频系统
                self._reset_audio_system()
            
            return False
    
    def _reset_audio_system(self):
        """重置音频系统状态"""
        try:
            logger.info("尝试重置音频系统...")
            sd.stop()  # 停止所有播放
            time.sleep(0.5)
            
            # 重新查询设备
            self.device_manager._update_device_info()
            
            logger.info("音频系统重置完成")
        except Exception as e:
            logger.error(f"音频系统重置失败: {e}")

class AUHALErrorFixer:
    """AUHAL错误修复器"""
    
    def __init__(self):
        self.device_manager = AudioDeviceManager()
        self.audio_player = SafeAudioPlayer(self.device_manager)
        self.input_device_id = None
    
    def set_input_device(self, device_id):
        """设置输入设备ID（麦克风等）"""
        self.input_device_id = device_id
        logger.info(f"设置输入设备: {device_id}")
    
    def play_tts_audio(self, audio_data, sample_rate=48000):
        """播放TTS音频，避免与输入设备冲突"""
        exclude_devices = []
        if self.input_device_id is not None:
            exclude_devices.append(self.input_device_id)
        
        logger.info(f"播放TTS音频，排除设备: {exclude_devices}")
        return self.audio_player.play_audio_safe(audio_data, sample_rate, exclude_devices)
    
    def test_all_devices(self):
        """测试所有音频设备"""
        logger.info("=== 测试所有音频设备 ===")
        
        # 生成测试音频
        duration = 0.5
        sample_rate = 48000
        t = np.linspace(0, duration, int(sample_rate * duration), False)
        test_audio = 0.1 * np.sin(2 * np.pi * 440 * t)
        
        working_devices = []
        failed_devices = []
        
        for i, device in enumerate(self.device_manager.devices):
            if device['max_output_channels'] > 0:
                logger.info(f"测试设备 {i}: {device['name']}")
                
                try:
                    with self.device_manager.reserve_device(i, "设备测试"):
                        sd.play(test_audio, sample_rate, device=i, blocking=True)
                        working_devices.append((i, device['name']))
                        logger.info(f"✅ 设备 {i} 工作正常")
                        
                except Exception as e:
                    failed_devices.append((i, device['name'], str(e)))
                    logger.error(f"❌ 设备 {i} 测试失败: {e}")
                    
                    if "-10863" in str(e):
                        logger.error("🔍 检测到AUHAL Error -10863")
        
        logger.info(f"测试完成: {len(working_devices)} 个设备正常, {len(failed_devices)} 个设备异常")
        return working_devices, failed_devices

def apply_auhal_fixes():
    """应用AUHAL错误修复"""
    logger.info("=== 应用PaMacCore AUHAL Error -10863修复 ===")
    
    fixer = AUHALErrorFixer()
    
    # 1. 测试所有设备
    working_devices, failed_devices = fixer.test_all_devices()
    
    # 2. 生成修复报告
    logger.info("\n=== 设备状态报告 ===")
    logger.info(f"可用设备 ({len(working_devices)}):")
    for device_id, name in working_devices:
        logger.info(f"  ✅ [{device_id}] {name}")
    
    if failed_devices:
        logger.warning(f"问题设备 ({len(failed_devices)}):")
        for device_id, name, error in failed_devices:
            logger.warning(f"  ❌ [{device_id}] {name}: {error}")
    
    # 3. 提供修复建议
    logger.info("\n=== 修复建议 ===")
    
    if failed_devices:
        auhal_errors = [d for d in failed_devices if "-10863" in d[2]]
        if auhal_errors:
            logger.info("检测到AUHAL Error -10863，建议:")
            logger.info("1. 确保没有其他应用占用音频设备")
            logger.info("2. 重启Core Audio服务: sudo pkill coreaudiod")
            logger.info("3. 使用蓝牙耳机或外接音响避免内建设备冲突")
            logger.info("4. 在代码中使用排除列表避免设备冲突")
    
    if working_devices:
        recommended_device = working_devices[0]
        logger.info(f"推荐使用设备: [{recommended_device[0]}] {recommended_device[1]}")
    
    return fixer

if __name__ == "__main__":
    # 运行修复程序
    fixer = apply_auhal_fixes()
    
    # 演示安全播放
    logger.info("\n=== 演示安全音频播放 ===")
    
    # 生成测试音频
    duration = 1.0
    sample_rate = 48000
    t = np.linspace(0, duration, int(sample_rate * duration), False)
    test_audio = 0.1 * np.sin(2 * np.pi * 880 * t)
    
    # 模拟麦克风设备
    fixer.set_input_device(2)  # MacBook Pro麦克风通常是设备2
    
    # 安全播放
    success = fixer.play_tts_audio(test_audio)
    
    if success:
        logger.info("✅ 安全播放测试成功")
    else:
        logger.error("❌ 安全播放测试失败")