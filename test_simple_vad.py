#!/usr/bin/env python3
"""
简单VAD测试 - 验证修复后的响应
"""

import sys
import time
import numpy as np
from obs_ai_translator_mic import MicrophoneTranslatorSystem

def test_simple_speech():
    """简单语音测试"""
    print("=== 简单语音测试 ===\n")
    
    system = MicrophoneTranslatorSystem(
        enable_tts=True,  # 启用TTS看完整流程
        source_lang='zh',
        target_lang='en'
    )
    
    try:
        print(f"VAD阈值: {system.translator.speech_threshold}")
        print("1. 发送清晰的语音信号...")
        
        # 生成清楚的测试语音
        sample_rate = 48000
        duration = 2.0
        t = np.linspace(0, duration, int(sample_rate * duration), False)
        
        # 使用足够大的音量
        audio = 0.03 * np.sin(2 * np.pi * 300 * t).astype(np.float32)
        actual_volume = np.sqrt(np.mean(audio**2))
        print(f"音频RMS音量: {actual_volume:.4f}")
        
        # 实时发送
        chunk_size = 4800  # 0.1秒
        start_time = time.time()
        
        print("开始发送音频块...")
        for i in range(0, len(audio), chunk_size):
            chunk = audio[i:i+chunk_size]
            system.translator.process_audio(chunk)
            time.sleep(0.02)  # 稍微慢一点模拟真实情况
        
        print("音频发送完成，现在发送静音...")
        
        # 发送静音
        silence = np.zeros(4800, dtype=np.float32)
        for i in range(12):  # 1.2秒静音
            system.translator.process_audio(silence)
            time.sleep(0.1)
        
        print("等待处理完成...")
        time.sleep(8)  # 等待完整的识别+翻译+TTS流程
        
        end_time = time.time()
        print(f"总耗时: {end_time - start_time:.1f}秒")
        
    except Exception as e:
        print(f"测试异常: {e}")
    finally:
        system.translator.shutdown()

if __name__ == "__main__":
    try:
        test_simple_speech()
    except KeyboardInterrupt:
        print("\n测试被中断")
    except Exception as e:
        print(f"异常: {e}")
        import traceback
        traceback.print_exc()