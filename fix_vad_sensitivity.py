#!/usr/bin/env python3
"""
修复VAD敏感度问题 - 调整语音检测参数
"""
import re

def update_vad_parameters():
    """更新VAD参数，降低误触发"""
    
    # 读取当前文件
    with open('realtime_translator.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 备份原文件
    with open('realtime_translator.py.backup', 'w', encoding='utf-8') as f:
        f.write(content)
    print("✅ 已备份原文件到 realtime_translator.py.backup")
    
    # 更新参数
    updates = [
        # 提高语音阈值，减少误触发
        (r'self\.speech_threshold = 0\.008', 'self.speech_threshold = 0.015'),
        
        # 增加去抖动时间，确保是真正的语音
        (r'self\.speech_debounce_time = 0\.3', 'self.speech_debounce_time = 0.5'),
        
        # 增加最小语音长度
        (r'self\.min_speech_duration = 0\.5', 'self.min_speech_duration = 0.8'),
        
        # 在主程序中也提高音量阈值
        (r'if volume > 0\.001:', 'if volume > 0.005:')
    ]
    
    modified = False
    for pattern, replacement in updates:
        if re.search(pattern, content):
            content = re.sub(pattern, replacement, content)
            modified = True
            print(f"✅ 更新: {pattern} -> {replacement}")
        else:
            print(f"⚠️  未找到: {pattern}")
    
    if modified:
        # 写入更新后的文件
        with open('realtime_translator.py', 'w', encoding='utf-8') as f:
            f.write(content)
        print("\n✅ VAD参数已更新")
        
        print("\n📋 更新内容:")
        print("1. 语音阈值: 0.008 -> 0.015 (降低敏感度)")
        print("2. 去抖动时间: 0.3s -> 0.5s (需要更长时间确认语音)")
        print("3. 最小语音长度: 0.5s -> 0.8s (过滤短暂噪声)")
        print("4. 音量处理阈值: 0.001 -> 0.005 (减少处理频率)")
        
        print("\n💡 这些更改将:")
        print("- 减少环境噪声的误触发")
        print("- 需要更明确的语音输入才会开始处理")
        print("- 过滤掉短暂的噪声干扰")
        
    else:
        print("❌ 没有找到需要更新的参数")

def update_main_program():
    """同时更新主程序的音量阈值"""
    try:
        with open('obs_ai_translator_main.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 备份
        with open('obs_ai_translator_main.py.backup', 'w', encoding='utf-8') as f:
            f.write(content)
        
        # 更新主程序中的音量阈值
        if 'if volume > 0.001:' in content:
            content = content.replace('if volume > 0.001:', 'if volume > 0.005:')
            
            with open('obs_ai_translator_main.py', 'w', encoding='utf-8') as f:
                f.write(content)
            print("✅ 已更新主程序的音量阈值")
        else:
            print("⚠️  主程序中未找到音量阈值设置")
            
    except Exception as e:
        print(f"❌ 更新主程序失败: {e}")

def main():
    print("=== VAD敏感度修复工具 ===")
    print("此工具将调整语音检测参数，减少误触发")
    
    print("\n当前问题:")
    print("- 没有说话时仍然产生翻译结果")
    print("- 可能是语音阈值过低，环境噪声触发了检测")
    
    print("\n即将进行的修改:")
    print("1. 提高语音检测阈值")
    print("2. 增加去抖动时间")
    print("3. 增加最小语音长度要求")
    
    confirm = input("\n是否继续? (y/N): ").strip().lower()
    if confirm != 'y':
        print("取消操作")
        return
    
    # 更新VAD参数
    update_vad_parameters()
    
    # 更新主程序
    update_main_program()
    
    print("\n🎉 修复完成!")
    print("\n📝 测试建议:")
    print("1. 重新启动翻译程序")
    print("2. 在安静环境中测试是否还有误触发")
    print("3. 如果仍有问题，运行 python3 diagnose_audio_input.py 进行详细诊断")
    print("4. 如果需要恢复原设置，使用备份文件")

if __name__ == "__main__":
    main()
