#!/usr/bin/env python3
"""
快速音频流测试 - 验证修复
"""
import sounddevice as sd
import numpy as np
import time
import threading

class QuickAudioTest:
    def __init__(self):
        self.is_running = False
        self.last_audio_time = time.time()
        
        # 查找BlackHole设备
        devices = sd.query_devices()
        self.blackhole_in = None
        
        for i, device in enumerate(devices):
            if 'BlackHole' in device['name'] and device['max_input_channels'] > 0:
                self.blackhole_in = i
                break
        
        if self.blackhole_in is None:
            print("❌ 未找到BlackHole输入设备")
            return
        
        print(f"✅ 找到BlackHole设备: {devices[self.blackhole_in]['name']}")
    
    def audio_callback(self, indata, frames, time_info, status):
        """音频回调函数"""
        if status:
            print(f"[音频] 状态: {status}")
        
        # 更新最后音频时间
        self.last_audio_time = time.time()
        
        # 计算音量
        volume = np.sqrt(np.mean(indata**2))
        
        # 显示音频活动
        if volume > 0.001:  # 有音频输入
            bars = int(volume * 50)
            print(f"\r[音频] {'█' * bars:<20} {volume:.4f}", end='', flush=True)
    
    def start_test(self):
        """开始测试"""
        if self.blackhole_in is None:
            return False
        
        self.is_running = True
        retry_count = 0
        max_retries = 3
        
        print("\n=== 音频流自动恢复测试 ===")
        print("按 Ctrl+C 停止测试")
        print()
        
        while self.is_running and retry_count < max_retries:
            try:
                print(f"[测试] 启动音频流 (尝试 {retry_count + 1}/{max_retries})")
                
                with sd.InputStream(
                    device=self.blackhole_in,
                    channels=2,
                    samplerate=44100,
                    blocksize=1024,
                    dtype='float32',
                    callback=self.audio_callback
                ) as stream:
                    print(f"[测试] ✅ 音频流已启动")
                    retry_count = 0  # 重置重试计数
                    
                    # 监控音频流
                    while self.is_running:
                        time.sleep(1)
                        
                        # 检查音频流是否中断
                        if time.time() - self.last_audio_time > 10:
                            print(f"\n[测试] ⚠️ 检测到10秒无音频，可能流中断")
                            break
                        
                        # 检查流状态
                        if not stream.active:
                            print(f"\n[测试] ❌ 音频流已停止")
                            break
                
                if self.is_running:
                    print(f"\n[测试] 准备重启音频流...")
                    retry_count += 1
                    time.sleep(2)
                
            except KeyboardInterrupt:
                print(f"\n[测试] 用户中断")
                self.is_running = False
                break
            except Exception as e:
                print(f"\n[测试] 错误: {e}")
                retry_count += 1
                if retry_count < max_retries:
                    print(f"[测试] 等待3秒后重试...")
                    time.sleep(3)
        
        if retry_count >= max_retries:
            print(f"[测试] ❌ 达到最大重试次数")
            return False
        
        print(f"[测试] ✅ 测试完成")
        return True

def main():
    """主函数"""
    print("快速音频流测试")
    print("此测试验证音频流自动恢复功能")
    print()
    
    tester = QuickAudioTest()
    success = tester.start_test()
    
    if success:
        print("\n✅ 音频流修复验证成功")
        print("现在可以运行完整的翻译系统了")
    else:
        print("\n❌ 音频流测试失败")
        print("请检查BlackHole设备是否正常工作")

if __name__ == "__main__":
    main()
