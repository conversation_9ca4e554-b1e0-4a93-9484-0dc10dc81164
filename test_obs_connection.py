#!/usr/bin/env python3
"""
测试OBS WebSocket连接
"""
import obsws_python as obs
import json

def test_obs_connection():
    """测试OBS连接"""
    # 连接参数
    host = "************"
    port = 4455
    password = "7PD7PcgZnC6rP75K"  # 替换为您的密码
    
    try:
        # 创建客户端
        client = obs.ReqClient(host=host, port=port, password=password)
        
        # 获取版本信息
        version = client.get_version()
        print(f"✓ 成功连接到OBS Studio")
        print(f"  OBS版本: {version.obs_version}")
        print(f"  WebSocket版本: {version.obs_web_socket_version}")
        
        # 获取场景列表
        scenes = client.get_scene_list()
        print(f"\n当前场景:")
        for scene in scenes.scenes:
            print(f"  - {scene['sceneName']}")
        print(f"当前激活场景: {scenes.current_program_scene_name}")
        
        # 获取音频源
        sources = client.get_input_list()
        print(f"\n音频输入源:")
        for source in sources.inputs:
            print(f"  - {source['inputName']} ({source['inputKind']})")
        
        # 获取音频设置
        print(f"\n音频监听设备:")
        # 注意：不同版本的obsws-python可能有不同的API
        
        return client
        
    except Exception as e:
        print(f"✗ 连接失败: {e}")
        print("\n请检查:")
        print("1. OBS是否正在运行")
        print("2. WebSocket服务器是否启用")
        print("3. 密码是否正确")
        print("4. 端口是否正确（默认4455）")
        return None

def control_audio_monitoring(client):
    """控制音频监听"""
    try:
        # 获取所有输入源
        sources = client.get_input_list()
        
        print("\n=== 音频监听控制 ===")
        for source in sources.inputs:
            if 'audio' in source['inputKind'].lower():
                print(f"\n处理音频源: {source['inputName']}")
                
                # 设置监听模式
                # 监听模式选项: OBS_MONITORING_TYPE_NONE, OBS_MONITORING_TYPE_MONITOR_ONLY, OBS_MONITORING_TYPE_MONITOR_AND_OUTPUT
                try:
                    # 这里需要根据实际的API调整
                    pass
                except Exception as e:
                    print(f"  设置监听失败: {e}")
                    
    except Exception as e:
        print(f"控制音频失败: {e}")

if __name__ == "__main__":
    print("=== OBS WebSocket连接测试 ===\n")
    
    client = test_obs_connection()
    
    if client:
        print("\n✓ 测试成功！OBS连接正常")
        control_audio_monitoring(client)
