#!/usr/bin/env python3
"""
测试VAD修复
验证用户说话后能正确检测结束并开始处理
"""

import sys
import time
import numpy as np
from obs_ai_translator_mic import MicrophoneTranslatorSystem

def generate_speech_audio(duration=2.0, sample_rate=48000, volume=0.025):
    """生成语音音频"""
    t = np.linspace(0, duration, int(sample_rate * duration), False)
    
    # 模拟语音：基频 + 谐波
    audio = (
        0.4 * volume * np.sin(2 * np.pi * 150 * t) +
        0.3 * volume * np.sin(2 * np.pi * 300 * t) +
        0.2 * volume * np.sin(2 * np.pi * 450 * t) +
        0.1 * volume * np.sin(2 * np.pi * 600 * t)
    )
    
    # 添加包络
    envelope = np.hanning(len(audio))
    audio = audio * envelope
    
    # 添加噪声
    noise = np.random.normal(0, 0.001, len(audio))
    audio = audio + noise
    
    return audio.astype(np.float32)

def test_vad_response():
    """测试VAD响应速度"""
    print("=== 测试VAD响应速度 ===\n")
    
    system = MicrophoneTranslatorSystem(
        enable_tts=False,  # 简化测试
        source_lang='zh',
        target_lang='en'
    )
    
    try:
        print("VAD参数:")
        translator = system.translator
        print(f"  语音阈值: {translator.speech_threshold}")
        print(f"  静音超时: {translator.silence_timeout}s")
        print(f"  最小语音长度: {translator.min_speech_duration}s")
        print(f"  最大语音长度: {translator.max_speech_duration}s")
        
        print("\n1. 发送2秒语音...")
        speech_audio = generate_speech_audio(2.0, 48000, 0.02)
        
        start_time = time.time()
        
        # 模拟实时音频流
        chunk_size = 4800  # 0.1秒块
        for i in range(0, len(speech_audio), chunk_size):
            chunk = speech_audio[i:i+chunk_size]
            translator.process_audio(chunk)
            time.sleep(0.01)  # 稍微模拟实时性
        
        detection_time = time.time()
        print(f"2. 语音发送完成，等待VAD检测结束...")
        
        # 发送静音触发结束检测
        silence = np.zeros(4800, dtype=np.float32)
        silence_start = time.time()
        
        for i in range(15):  # 1.5秒静音，应该在1秒时触发
            translator.process_audio(silence)
            time.sleep(0.1)
            
            # 检查是否已经结束说话状态
            if not translator.is_speaking:
                break
        
        end_time = time.time()
        
        total_time = end_time - start_time
        detection_delay = end_time - detection_time
        
        print(f"\n结果分析:")
        print(f"  总时间: {total_time:.1f}s")
        print(f"  检测延迟: {detection_delay:.1f}s")
        print(f"  工作队列: {translator.work_queue.qsize()} 项")
        
        if detection_delay <= 1.5:  # 应该在1.5秒内检测到结束
            print("✅ VAD响应正常")
            return True
        else:
            print("❌ VAD响应过慢")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False
    finally:
        system.translator.shutdown()

def test_multiple_speeches():
    """测试连续语音处理"""
    print("\n=== 测试连续语音处理 ===\n")
    
    system = MicrophoneTranslatorSystem(
        enable_tts=False,
        source_lang='zh',
        target_lang='en'
    )
    
    try:
        print("1. 发送3段语音，每段间隔1.5秒静音...")
        
        for speech_num in range(1, 4):
            print(f"\n--- 第{speech_num}段语音 ---")
            
            # 发送语音
            speech = generate_speech_audio(1.5, 48000, 0.018)
            chunk_size = 4800
            
            for i in range(0, len(speech), chunk_size):
                chunk = speech[i:i+chunk_size]
                system.translator.process_audio(chunk)
                time.sleep(0.01)
            
            # 发送静音
            silence = np.zeros(4800, dtype=np.float32)
            for i in range(12):  # 1.2秒静音
                system.translator.process_audio(silence)
                time.sleep(0.1)
                
                if not system.translator.is_speaking:
                    print(f"语音{speech_num}处理完成")
                    break
            else:
                print(f"⚠️  语音{speech_num}未及时结束")
            
            time.sleep(0.5)  # 段间休息
        
        print(f"\n2. 最终队列状态: {system.translator.work_queue.qsize()} 项")
        
        if system.translator.work_queue.qsize() >= 2:  # 至少处理了2段语音
            print("✅ 连续语音处理正常")
            return True
        else:
            print("❌ 连续语音处理异常")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False
    finally:
        system.translator.shutdown()

def test_edge_cases():
    """测试边缘情况"""
    print("\n=== 测试边缘情况 ===\n")
    
    system = MicrophoneTranslatorSystem(
        enable_tts=False,
        source_lang='zh',
        target_lang='en'
    )
    
    try:
        # 1. 过短语音
        print("1. 测试过短语音（0.2秒）...")
        short_speech = generate_speech_audio(0.2, 48000, 0.02)
        chunk_size = 4800
        
        for i in range(0, len(short_speech), chunk_size):
            system.translator.process_audio(short_speech[i:i+chunk_size])
            time.sleep(0.01)
        
        # 静音结束
        silence = np.zeros(4800, dtype=np.float32)
        for i in range(12):
            system.translator.process_audio(silence)
            time.sleep(0.1)
            if not system.translator.is_speaking:
                break
        
        queue_size_1 = system.translator.work_queue.qsize()
        print(f"   短语音后队列: {queue_size_1} 项")
        
        # 2. 长语音
        print("\n2. 测试长语音（3秒）...")
        long_speech = generate_speech_audio(3.0, 48000, 0.02)
        
        for i in range(0, len(long_speech), chunk_size):
            system.translator.process_audio(long_speech[i:i+chunk_size])
            time.sleep(0.01)
        
        # 静音结束
        for i in range(12):
            system.translator.process_audio(silence)
            time.sleep(0.1)
            if not system.translator.is_speaking:
                break
        
        queue_size_2 = system.translator.work_queue.qsize()
        print(f"   长语音后队列: {queue_size_2} 项")
        
        # 3. 极低音量
        print("\n3. 测试极低音量语音...")
        low_speech = generate_speech_audio(1.0, 48000, 0.005)  # 很低的音量
        
        for i in range(0, len(low_speech), chunk_size):
            system.translator.process_audio(low_speech[i:i+chunk_size])
            time.sleep(0.01)
        
        time.sleep(1.5)  # 等待可能的处理
        queue_size_3 = system.translator.work_queue.qsize()
        print(f"   低音量后队列: {queue_size_3} 项")
        
        print("\n结果:")
        print(f"  短语音应被跳过: {queue_size_1 == 0}")
        print(f"  长语音应被处理: {queue_size_2 > queue_size_1}")
        print(f"  低音量可能被跳过: {queue_size_3 == queue_size_2}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False
    finally:
        system.translator.shutdown()

def main():
    """主测试"""
    print("=== VAD修复验证测试 ===\n")
    print("测试目标:")
    print("🎯 用户说话后能及时检测结束")
    print("🎯 避免长时间无响应")
    print("🎯 正确处理各种语音长度")
    print("🎯 支持连续对话")
    print("\n" + "="*50 + "\n")
    
    results = []
    
    # 1. VAD响应速度
    results.append(("VAD响应速度", test_vad_response()))
    
    # 2. 连续语音处理
    results.append(("连续语音处理", test_multiple_speeches()))
    
    # 3. 边缘情况
    results.append(("边缘情况", test_edge_cases()))
    
    # 结果汇总
    print("\n" + "="*50)
    print("VAD修复测试结果")
    print("="*50)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:<15} {status}")
    
    print("="*50)
    print(f"总计: {passed}/{total} 测试通过")
    
    if passed == total:
        print("\n🎉 VAD修复验证通过！")
        print("\n关键改进:")
        print("✅ 基于实际时间的静音检测（1秒超时）")
        print("✅ 合理的语音长度过滤（最小0.3秒）")
        print("✅ 及时的说话结束检测")
        print("✅ 详细的状态日志输出")
        
        print("\n现在用户说话后应该：")
        print("• 立即看到'开始说话'提示")
        print("• 静音1秒后自动检测结束")
        print("• 快速开始语音识别和翻译")
        print("• 支持连续对话")
    else:
        print("\n⚠️  部分测试失败，需要进一步调试")
    
    return passed == total

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n测试被中断")
        sys.exit(1)
    except Exception as e:
        print(f"测试异常: {e}")
        sys.exit(1)