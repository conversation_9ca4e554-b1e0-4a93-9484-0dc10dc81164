{"test_session": {"start_time": "2025-08-27T14:20:07.879907", "end_time": "2025-08-27T14:21:23.735662", "total_duration_seconds": 75.854049, "python_version": "3.13.1 | packaged by conda-forge | (main, Dec  5 2024, 08:44:03) [Clang 18.1.8 ]", "platform": "darwin"}, "test_results": [{"test_name": "component_isolation", "start_time": "2025-08-27T14:20:07.879907", "status": "passed", "details": {"vad": "passed", "asr": "passed", "queue": "passed"}, "end_time": "2025-08-27T14:20:19.286752", "duration": 11.406845}, {"test_name": "consecutive_inputs", "start_time": "2025-08-27T14:20:19.288148", "status": "passed", "input_count": 5, "successful_processes": 5, "failed_processes": 0, "freeze_detected": false, "end_time": "2025-08-27T14:20:52.077741", "duration": 32.789593}, {"test_name": "stress_scenarios", "start_time": "2025-08-27T14:20:52.077840", "status": "passed", "scenarios": {"rapid_input": "passed", "long_speech": "passed", "noise_interference": "passed"}, "end_time": "2025-08-27T14:21:10.106903", "duration": 18.029063}, {"test_name": "reset_functionality", "start_time": "2025-08-27T14:21:10.107114", "status": "passed", "reset_attempts": 3, "end_time": "2025-08-27T14:21:23.735662", "duration": 13.628548}], "system_monitoring": {"duration_seconds": 75.0, "data_points": 150, "cpu": {"min": 0.0, "max": 355.4, "avg": 109.30600000000001}, "memory": {"min": 0.19540786743164062, "max": 30.59701919555664, "avg": 18.963154474894207}, "threads": {"min": 2, "max": 35, "avg": 23.106666666666666}, "raw_data": [{"timestamp": "2025-08-27T14:21:19.150852", "cpu_percent": 0.6, "memory_rss": 4198907904, "memory_vms": 435994034176, "memory_percent": 24.440860748291016, "thread_count": 34, "num_fds": 7, "threads": [[1, 12.038645, 6.455904], [2, 0.033929, 0.032704], [3, 0.003507, 0.00277], [4, 0.000899, 0.000907], [5, 0.800071, 1.917236], [6, 0.795115, 1.917433], [7, 0.799528, 1.930312], [8, 1.1e-05, 9e-06], [9, 5e-06, 4e-06], [10, 3e-06, 4e-06], [11, 0.000128, 0.000379], [12, 0.000478, 0.000701], [13, 0.000443, 0.002075], [14, 1e-06, 6e-06], [15, 1e-06, 3e-06], [16, 21.359071, 1.766194], [17, 0.074417, 0.091874], [18, 3e-06, 5e-06], [19, 8.5e-05, 0.00014], [20, 0.002259, 0.001742], [21, 6.925366, 0.617771], [22, 5.135959, 0.354452], [23, 6.870774, 0.593786], [24, 5.275796, 0.893521], [25, 3e-06, 5e-06], [26, 3e-06, 4e-06], [27, 7.7e-05, 6.3e-05], [28, 2.353333, 0.239428], [29, 1.600436, 0.121485], [30, 2.295104, 0.220074], [31, 1.7e-05, 5.5e-05], [32, 4e-06, 6e-06], [33, 9e-06, 6e-06], [34, 7e-06, 6e-06]]}, {"timestamp": "2025-08-27T14:21:19.656933", "cpu_percent": 0.2, "memory_rss": 4086366208, "memory_vms": 435994034176, "memory_percent": 23.785781860351562, "thread_count": 34, "num_fds": 7, "threads": [[1, 12.038645, 6.455904], [2, 0.034445, 0.033058], [3, 0.003507, 0.00277], [4, 0.00092, 0.000913], [5, 0.800071, 1.917236], [6, 0.795115, 1.917433], [7, 0.799528, 1.930312], [8, 1.1e-05, 9e-06], [9, 5e-06, 4e-06], [10, 3e-06, 4e-06], [11, 0.000128, 0.000379], [12, 0.000478, 0.000701], [13, 0.000443, 0.002075], [14, 1e-06, 6e-06], [15, 1e-06, 3e-06], [16, 21.359071, 1.766194], [17, 0.074417, 0.091874], [18, 3e-06, 5e-06], [19, 8.5e-05, 0.00014], [20, 0.002259, 0.001742], [21, 6.925366, 0.617771], [22, 5.135959, 0.354452], [23, 6.870774, 0.593786], [24, 5.275796, 0.893521], [25, 3e-06, 5e-06], [26, 3e-06, 4e-06], [27, 7.7e-05, 6.3e-05], [28, 2.353333, 0.239428], [29, 1.600436, 0.121485], [30, 2.295104, 0.220074], [31, 1.7e-05, 5.5e-05], [32, 4e-06, 6e-06], [33, 9e-06, 6e-06], [34, 7e-06, 6e-06]]}, {"timestamp": "2025-08-27T14:21:20.166008", "cpu_percent": 0.4, "memory_rss": 2511634432, "memory_vms": 435994034176, "memory_percent": 14.619636535644531, "thread_count": 34, "num_fds": 7, "threads": [[1, 12.039036, 6.456012], [2, 0.034851, 0.033358], [3, 0.003874, 0.002875], [4, 0.00092, 0.000913], [5, 0.800071, 1.917236], [6, 0.795115, 1.917433], [7, 0.799528, 1.930312], [8, 1.1e-05, 9e-06], [9, 5e-06, 4e-06], [10, 3e-06, 4e-06], [11, 0.000128, 0.000379], [12, 0.000478, 0.000701], [13, 0.000443, 0.002075], [14, 1e-06, 6e-06], [15, 1e-06, 3e-06], [16, 21.359071, 1.766194], [17, 0.074417, 0.091874], [18, 3e-06, 5e-06], [19, 8.5e-05, 0.00014], [20, 0.002347, 0.001785], [21, 6.925366, 0.617771], [22, 5.135959, 0.354452], [23, 6.870774, 0.593786], [24, 5.275796, 0.893521], [25, 3e-06, 5e-06], [26, 3e-06, 4e-06], [27, 7.7e-05, 6.3e-05], [28, 2.353333, 0.239428], [29, 1.600436, 0.121485], [30, 2.295104, 0.220074], [31, 1.7e-05, 5.5e-05], [32, 4e-06, 6e-06], [33, 9e-06, 6e-06], [34, 7e-06, 6e-06]]}, {"timestamp": "2025-08-27T14:21:20.667464", "cpu_percent": 0.1, "memory_rss": 1561444352, "memory_vms": 435994034176, "memory_percent": 9.088802337646484, "thread_count": 34, "num_fds": 7, "threads": [[1, 12.039036, 6.456012], [2, 0.03527, 0.033692], [3, 0.003874, 0.002875], [4, 0.00094, 0.000918], [5, 0.800071, 1.917236], [6, 0.795115, 1.917433], [7, 0.799528, 1.930312], [8, 1.1e-05, 9e-06], [9, 5e-06, 4e-06], [10, 3e-06, 4e-06], [11, 0.000128, 0.000379], [12, 0.000478, 0.000701], [13, 0.000443, 0.002075], [14, 1e-06, 6e-06], [15, 1e-06, 3e-06], [16, 21.359071, 1.766194], [17, 0.074417, 0.091874], [18, 3e-06, 5e-06], [19, 8.5e-05, 0.00014], [20, 0.002347, 0.001785], [21, 6.925366, 0.617771], [22, 5.135959, 0.354452], [23, 6.870774, 0.593786], [24, 5.275796, 0.893521], [25, 3e-06, 5e-06], [26, 3e-06, 4e-06], [27, 7.7e-05, 6.3e-05], [28, 2.353333, 0.239428], [29, 1.600436, 0.121485], [30, 2.295104, 0.220074], [31, 1.7e-05, 5.5e-05], [32, 4e-06, 6e-06], [33, 9e-06, 6e-06], [34, 7e-06, 6e-06]]}, {"timestamp": "2025-08-27T14:21:21.168501", "cpu_percent": 1.5, "memory_rss": 1490976768, "memory_vms": 435994034176, "memory_percent": 8.678627014160156, "thread_count": 34, "num_fds": 7, "threads": [[1, 12.044938, 6.456872], [2, 0.035821, 0.034033], [3, 0.003874, 0.002875], [4, 0.00094, 0.000918], [5, 0.800071, 1.917236], [6, 0.795115, 1.917433], [7, 0.799528, 1.930312], [8, 1.1e-05, 9e-06], [9, 5e-06, 4e-06], [10, 3e-06, 4e-06], [11, 0.000128, 0.000379], [12, 0.000478, 0.000701], [13, 0.000443, 0.002075], [14, 1e-06, 6e-06], [15, 1e-06, 3e-06], [16, 21.359071, 1.766194], [17, 0.074417, 0.091874], [18, 3e-06, 5e-06], [19, 8.5e-05, 0.00014], [20, 0.002409, 0.001792], [21, 6.925366, 0.617771], [22, 5.135959, 0.354452], [23, 6.870774, 0.593786], [24, 5.275796, 0.893521], [25, 3e-06, 5e-06], [26, 3e-06, 4e-06], [27, 7.7e-05, 6.3e-05], [28, 2.353333, 0.239428], [29, 1.600436, 0.121485], [30, 2.295104, 0.220074], [31, 1.7e-05, 5.5e-05], [32, 4e-06, 6e-06], [33, 9e-06, 6e-06], [34, 7e-06, 6e-06]]}, {"timestamp": "2025-08-27T14:21:21.675876", "cpu_percent": 0.2, "memory_rss": 1415823360, "memory_vms": 435994034176, "memory_percent": 8.24117660522461, "thread_count": 34, "num_fds": 7, "threads": [[1, 12.044938, 6.456872], [2, 0.036355, 0.034342], [3, 0.003874, 0.002875], [4, 0.00097, 0.00093], [5, 0.800071, 1.917236], [6, 0.795115, 1.917433], [7, 0.799528, 1.930312], [8, 1.1e-05, 9e-06], [9, 5e-06, 4e-06], [10, 3e-06, 4e-06], [11, 0.000128, 0.000379], [12, 0.000478, 0.000701], [13, 0.000443, 0.002075], [14, 1e-06, 6e-06], [15, 1e-06, 3e-06], [16, 21.359071, 1.766194], [17, 0.074417, 0.091874], [18, 3e-06, 5e-06], [19, 8.5e-05, 0.00014], [20, 0.002409, 0.001792], [21, 6.925366, 0.617771], [22, 5.135959, 0.354452], [23, 6.870774, 0.593786], [24, 5.275796, 0.893521], [25, 3e-06, 5e-06], [26, 3e-06, 4e-06], [27, 7.7e-05, 6.3e-05], [28, 2.353333, 0.239428], [29, 1.600436, 0.121485], [30, 2.295104, 0.220074], [31, 1.7e-05, 5.5e-05], [32, 4e-06, 6e-06], [33, 9e-06, 6e-06], [34, 7e-06, 6e-06]]}, {"timestamp": "2025-08-27T14:21:22.185494", "cpu_percent": 0.4, "memory_rss": 1387905024, "memory_vms": 435994034176, "memory_percent": 8.078670501708984, "thread_count": 34, "num_fds": 7, "threads": [[1, 12.045323, 6.45701], [2, 0.036856, 0.034666], [3, 0.004319, 0.00299], [4, 0.00097, 0.00093], [5, 0.800071, 1.917236], [6, 0.795115, 1.917433], [7, 0.799528, 1.930312], [8, 1.1e-05, 9e-06], [9, 5e-06, 4e-06], [10, 3e-06, 4e-06], [11, 0.000128, 0.000379], [12, 0.000478, 0.000701], [13, 0.000443, 0.002075], [14, 1e-06, 6e-06], [15, 1e-06, 3e-06], [16, 21.359071, 1.766194], [17, 0.074417, 0.091874], [18, 3e-06, 5e-06], [19, 8.5e-05, 0.00014], [20, 0.002517, 0.001799], [21, 6.925366, 0.617771], [22, 5.135959, 0.354452], [23, 6.870774, 0.593786], [24, 5.275796, 0.893521], [25, 3e-06, 5e-06], [26, 3e-06, 4e-06], [27, 7.7e-05, 6.3e-05], [28, 2.353333, 0.239428], [29, 1.600436, 0.121485], [30, 2.295104, 0.220074], [31, 1.7e-05, 5.5e-05], [32, 4e-06, 6e-06], [33, 9e-06, 6e-06], [34, 7e-06, 6e-06]]}, {"timestamp": "2025-08-27T14:21:22.693710", "cpu_percent": 0.2, "memory_rss": 1383219200, "memory_vms": 435994034176, "memory_percent": 8.051395416259766, "thread_count": 34, "num_fds": 7, "threads": [[1, 12.045323, 6.45701], [2, 0.037374, 0.035005], [3, 0.004319, 0.00299], [4, 0.001004, 0.000947], [5, 0.800071, 1.917236], [6, 0.795115, 1.917433], [7, 0.799528, 1.930312], [8, 1.1e-05, 9e-06], [9, 5e-06, 4e-06], [10, 3e-06, 4e-06], [11, 0.000128, 0.000379], [12, 0.000478, 0.000701], [13, 0.000443, 0.002075], [14, 1e-06, 6e-06], [15, 1e-06, 3e-06], [16, 21.359071, 1.766194], [17, 0.074417, 0.091874], [18, 3e-06, 5e-06], [19, 8.5e-05, 0.00014], [20, 0.002517, 0.001799], [21, 6.925366, 0.617771], [22, 5.135959, 0.354452], [23, 6.870774, 0.593786], [24, 5.275796, 0.893521], [25, 3e-06, 5e-06], [26, 3e-06, 4e-06], [27, 7.7e-05, 6.3e-05], [28, 2.353333, 0.239428], [29, 1.600436, 0.121485], [30, 2.295104, 0.220074], [31, 1.7e-05, 5.5e-05], [32, 4e-06, 6e-06], [33, 9e-06, 6e-06], [34, 7e-06, 6e-06]]}, {"timestamp": "2025-08-27T14:21:23.196103", "cpu_percent": 1.9, "memory_rss": 1384644608, "memory_vms": 435994034176, "memory_percent": 8.0596923828125, "thread_count": 34, "num_fds": 7, "threads": [[1, 12.052799, 6.458409], [2, 0.037765, 0.035268], [3, 0.004319, 0.00299], [4, 0.001004, 0.000947], [5, 0.800071, 1.917236], [6, 0.795115, 1.917433], [7, 0.799528, 1.930312], [8, 1.1e-05, 9e-06], [9, 5e-06, 4e-06], [10, 3e-06, 4e-06], [11, 0.000128, 0.000379], [12, 0.000478, 0.000701], [13, 0.000443, 0.002075], [14, 1e-06, 6e-06], [15, 1e-06, 3e-06], [16, 21.359071, 1.766194], [17, 0.074417, 0.091874], [18, 3e-06, 5e-06], [19, 8.5e-05, 0.00014], [20, 0.002612, 0.001809], [21, 6.925366, 0.617771], [22, 5.135959, 0.354452], [23, 6.870774, 0.593786], [24, 5.275796, 0.893521], [25, 3e-06, 5e-06], [26, 3e-06, 4e-06], [27, 7.7e-05, 6.3e-05], [28, 2.353333, 0.239428], [29, 1.600436, 0.121485], [30, 2.295104, 0.220074], [31, 1.7e-05, 5.5e-05], [32, 4e-06, 6e-06], [33, 9e-06, 6e-06], [34, 7e-06, 6e-06]]}, {"timestamp": "2025-08-27T14:21:23.699057", "cpu_percent": 0.1, "memory_rss": 1384792064, "memory_vms": 435994034176, "memory_percent": 8.060550689697266, "thread_count": 34, "num_fds": 7, "threads": [[1, 12.052799, 6.458409], [2, 0.038117, 0.035514], [3, 0.004319, 0.00299], [4, 0.001033, 0.000953], [5, 0.800071, 1.917236], [6, 0.795115, 1.917433], [7, 0.799528, 1.930312], [8, 1.1e-05, 9e-06], [9, 5e-06, 4e-06], [10, 3e-06, 4e-06], [11, 0.000166, 0.000472], [12, 0.000478, 0.000701], [13, 0.000443, 0.002075], [14, 1e-06, 6e-06], [15, 1e-06, 3e-06], [16, 21.359071, 1.766194], [17, 0.074417, 0.091874], [18, 3e-06, 5e-06], [19, 8.5e-05, 0.00014], [20, 0.002612, 0.001809], [21, 6.925366, 0.617771], [22, 5.135959, 0.354452], [23, 6.870774, 0.593786], [24, 5.275796, 0.893521], [25, 3e-06, 5e-06], [26, 3e-06, 4e-06], [27, 7.7e-05, 6.3e-05], [28, 2.353333, 0.239428], [29, 1.600436, 0.121485], [30, 2.295104, 0.220074], [31, 1.7e-05, 5.5e-05], [32, 4e-06, 6e-06], [33, 9e-06, 6e-06], [34, 7e-06, 6e-06]]}]}, "thread_monitoring": {"duration_seconds": 76, "data_points": 38, "thread_count": {"min": 3, "max": 22, "avg": 14.210526315789474}, "unique_thread_names": ["Thread-11 (consumer)", "Thread-21 (_recognition_worker)", "Thread-19 (_tts_worker)", "Thread-9 (_monitor_queues)", "Thread-17 (_recognition_worker)", "Thread-5 (_translation_worker)", "Thread-14 (_tts_worker)", "Thread-2 (_monitor_threads)", "Thread-4 (_recognition_worker)", "Thread-8", "Thread-18 (_translation_worker)", "Thread-20 (_monitor_worker)", "Thread-23 (_tts_worker)", "Thread-13 (_translation_worker)", "Thread-16 (_monitor_queues)", "MainThread", "Thread-3 (_monitor_freeze)", "Thread-22 (_translation_worker)", "Thread-24 (_monitor_worker)", "Thread-12 (_recognition_worker)", "Thread-6 (_tts_worker)", "Thread-1 (_monitor_loop)", "Thread-7 (_monitor_worker)", "Thread-15 (_monitor_worker)"], "recent_threads": [{"name": "MainThread", "ident": 8611323648, "daemon": false, "is_alive": true}, {"name": "Thread-1 (_monitor_loop)", "ident": 6115487744, "daemon": true, "is_alive": true}, {"name": "Thread-2 (_monitor_threads)", "ident": 6132314112, "daemon": true, "is_alive": true}, {"name": "Thread-3 (_monitor_freeze)", "ident": 6149140480, "daemon": true, "is_alive": true}, {"name": "Thread-4 (_recognition_worker)", "ident": 12901707776, "daemon": true, "is_alive": true}, {"name": "Thread-5 (_translation_worker)", "ident": 12918534144, "daemon": true, "is_alive": true}, {"name": "Thread-6 (_tts_worker)", "ident": 12935360512, "daemon": true, "is_alive": true}, {"name": "Thread-7 (_monitor_worker)", "ident": 12952186880, "daemon": true, "is_alive": true}, {"name": "Thread-8", "ident": 12969013248, "daemon": true, "is_alive": true}, {"name": "Thread-12 (_recognition_worker)", "ident": 13572796416, "daemon": true, "is_alive": true}, {"name": "Thread-13 (_translation_worker)", "ident": 13589622784, "daemon": true, "is_alive": true}, {"name": "Thread-14 (_tts_worker)", "ident": 13606449152, "daemon": true, "is_alive": true}, {"name": "Thread-15 (_monitor_worker)", "ident": 13623275520, "daemon": true, "is_alive": true}, {"name": "Thread-16 (_monitor_queues)", "ident": 13640101888, "daemon": true, "is_alive": true}, {"name": "Thread-17 (_recognition_worker)", "ident": 16391368704, "daemon": true, "is_alive": true}, {"name": "Thread-18 (_translation_worker)", "ident": 16408195072, "daemon": true, "is_alive": true}, {"name": "Thread-19 (_tts_worker)", "ident": 16425021440, "daemon": true, "is_alive": true}, {"name": "Thread-20 (_monitor_worker)", "ident": 16441847808, "daemon": true, "is_alive": true}, {"name": "Thread-21 (_recognition_worker)", "ident": 16928239616, "daemon": true, "is_alive": true}, {"name": "Thread-22 (_translation_worker)", "ident": 16945065984, "daemon": true, "is_alive": true}, {"name": "Thread-23 (_tts_worker)", "ident": 16961892352, "daemon": true, "is_alive": true}, {"name": "Thread-24 (_monitor_worker)", "ident": 16978718720, "daemon": true, "is_alive": true}]}, "queue_monitoring": {"duration_seconds": 59, "data_points": 59, "queue_analysis": {"recognition": {"avg_size": 0.5254237288135594, "max_size": 3, "min_size": 0, "full_percentage": 0.0, "empty_percentage": 67.79661016949152}, "translation": {"avg_size": 0.0, "max_size": 0, "min_size": 0, "full_percentage": 0.0, "empty_percentage": 100.0}, "tts": {"avg_size": 0.0, "max_size": 0, "min_size": 0, "full_percentage": 0.0, "empty_percentage": 100.0}}, "recent_data": [{"timestamp": "2025-08-27T14:21:18.962632", "queues": {"recognition": {"qsize": 0, "maxsize": 10, "full": false, "empty": true}, "translation": {"qsize": 0, "maxsize": 10, "full": false, "empty": true}, "tts": {"qsize": 0, "maxsize": 10, "full": false, "empty": true}}}, {"timestamp": "2025-08-27T14:21:19.970800", "queues": {"recognition": {"qsize": 0, "maxsize": 10, "full": false, "empty": true}, "translation": {"qsize": 0, "maxsize": 10, "full": false, "empty": true}, "tts": {"qsize": 0, "maxsize": 10, "full": false, "empty": true}}}, {"timestamp": "2025-08-27T14:21:20.981491", "queues": {"recognition": {"qsize": 0, "maxsize": 10, "full": false, "empty": true}, "translation": {"qsize": 0, "maxsize": 10, "full": false, "empty": true}, "tts": {"qsize": 0, "maxsize": 10, "full": false, "empty": true}}}, {"timestamp": "2025-08-27T14:21:21.987104", "queues": {"recognition": {"qsize": 0, "maxsize": 10, "full": false, "empty": true}, "translation": {"qsize": 0, "maxsize": 10, "full": false, "empty": true}, "tts": {"qsize": 0, "maxsize": 10, "full": false, "empty": true}}}, {"timestamp": "2025-08-27T14:21:22.995914", "queues": {"recognition": {"qsize": 0, "maxsize": 10, "full": false, "empty": true}, "translation": {"qsize": 0, "maxsize": 10, "full": false, "empty": true}, "tts": {"qsize": 0, "maxsize": 10, "full": false, "empty": true}}}]}, "freeze_detection": {"freeze_detected": false, "timeout_threshold": 45}, "summary": {"total_tests": 4, "passed_tests": 4, "failed_tests": 0, "pass_rate": 100.0, "freeze_bug_reproduced": false, "critical_findings": ["高CPU使用: 最高达到 355.4%", "线程数量异常: 最多 22 个线程"]}}