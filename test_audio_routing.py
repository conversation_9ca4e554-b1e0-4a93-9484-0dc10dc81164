#!/usr/bin/env python3
"""
BlackHole + OBS 音频路由测试脚本
"""
import sounddevice as sd
import numpy as np
import time

def list_audio_devices():
    """列出所有音频设备"""
    print("\n=== 音频设备列表 ===")
    devices = sd.query_devices()
    for i, device in enumerate(devices):
        direction = ""
        if device['max_input_channels'] > 0:
            direction += "输入"
        if device['max_output_channels'] > 0:
            if direction:
                direction += "/"
            direction += "输出"
        print(f"{i}: {device['name']} ({direction})")
        print(f"   通道数: 输入={device['max_input_channels']}, 输出={device['max_output_channels']}")
        print(f"   采样率: {device['default_samplerate']} Hz")
    return devices

def find_blackhole_device():
    """查找BlackHole设备"""
    devices = sd.query_devices()
    blackhole_in = None
    blackhole_out = None
    
    for i, device in enumerate(devices):
        if 'BlackHole' in device['name']:
            if device['max_input_channels'] > 0:
                blackhole_in = i
            if device['max_output_channels'] > 0:
                blackhole_out = i
    
    return blackhole_in, blackhole_out

def test_audio_passthrough():
    """测试音频直通"""
    blackhole_in, blackhole_out = find_blackhole_device()
    
    if blackhole_in is None or blackhole_out is None:
        print("错误：未找到BlackHole设备")
        return
    
    print(f"\n使用设备:")
    print(f"  输入: {sd.query_devices(blackhole_in)['name']}")
    print(f"  输出: {sd.query_devices(blackhole_out)['name']}")
    
    def callback(indata, outdata, frames, time, status):
        if status:
            print(f"状态: {status}")
        # 简单的音频直通
        outdata[:] = indata
        
        # 显示音频电平
        volume_norm = np.linalg.norm(indata) * 10
        bar = '#' * int(volume_norm)
        print(f'\r音频电平: {bar}', end='', flush=True)
    
    try:
        with sd.Stream(device=(blackhole_in, blackhole_out),
                      samplerate=48000,
                      blocksize=512,
                      dtype='float32',
                      latency='low',
                      channels=2,
                      callback=callback):
            print("\n音频路由测试中... 按Ctrl+C停止")
            while True:
                time.sleep(0.1)
    except KeyboardInterrupt:
        print("\n测试停止")
    except Exception as e:
        print(f"\n错误: {e}")

if __name__ == "__main__":
    # 列出设备
    list_audio_devices()
    
    # 测试BlackHole
    print("\n=== 测试BlackHole音频路由 ===")
    test_audio_passthrough()
