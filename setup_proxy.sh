#!/bin/bash

# AI翻译系统代理配置脚本

echo "=== AI翻译系统代理配置 ==="
echo ""

# 设置默认代理
export OPENAI_PROXY="http://127.0.0.1:7890"

echo "✅ 已设置代理: $OPENAI_PROXY"
echo ""
echo "其他可用的代理配置选项："
echo ""

# 常见的代理端口
echo "1. Clash/ClashX 默认端口:"
echo "   export OPENAI_PROXY=\"http://127.0.0.1:7890\""
echo ""

echo "2. V2Ray/V2RayU 默认端口:"
echo "   export OPENAI_PROXY=\"http://127.0.0.1:1087\""
echo ""

echo "3. Surge 默认端口:"
echo "   export OPENAI_PROXY=\"http://127.0.0.1:8888\""
echo ""

echo "4. 自定义代理服务器:"
echo "   export OPENAI_PROXY=\"http://your-proxy-server:port\""
echo ""

echo "5. 如果使用API中转服务:"
echo "   export OPENAI_BASE_URL=\"https://your-api-gateway.com/v1\""
echo ""

# 检查代理是否可用
echo "🔍 检查代理连通性..."
if curl -x "$OPENAI_PROXY" --connect-timeout 5 -s "https://api.openai.com" > /dev/null 2>&1; then
    echo "✅ 代理连接正常"
else
    echo "❌ 代理连接失败，请检查:"
    echo "   1. 代理软件是否启动"
    echo "   2. 端口号是否正确"
    echo "   3. 代理是否支持HTTPS"
fi

echo ""
echo "💡 使用方法:"
echo "1. 运行此脚本设置代理: source setup_proxy.sh"
echo "2. 启动翻译系统: python3 obs_ai_translator_mic.py --test"
echo ""
echo "📝 永久设置代理 (添加到 ~/.bash_profile 或 ~/.zshrc):"
echo "   echo 'export OPENAI_PROXY=\"http://127.0.0.1:7890\"' >> ~/.bash_profile"
echo "   source ~/.bash_profile"