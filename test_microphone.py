#!/usr/bin/env python3
"""
简单的麦克风测试工具
"""
import sounddevice as sd
import numpy as np
import time

def test_microphone():
    print("=== 麦克风测试 ===")
    
    # 可用的麦克风设备
    devices = [
        {"id": 0, "name": "Rockenlee的AirPods Pro #2"},
        {"id": 3, "name": "MacBook Pro麦克风"}
    ]
    
    print("可用麦克风:")
    for i, device in enumerate(devices):
        print(f"{i+1}. {device['name']} (ID: {device['id']})")
    
    print("\n自动测试所有麦克风设备...")
    
    for device in devices:
        print(f"\n--- 测试 {device['name']} ---")
        try:
            def callback(indata, frames, time, status):
                volume = np.sqrt(np.mean(indata**2))
                if volume > 0.001:
                    bar_length = int(volume * 50)
                    bar = '█' * bar_length + '░' * (50 - bar_length)
                    print(f'\r{device["name"]}: [{bar}] {volume:.4f}', end='', flush=True)
                
            # 测试5秒
            with sd.InputStream(
                device=device['id'],
                channels=1,
                samplerate=48000,
                blocksize=1024,
                dtype='float32',
                callback=callback
            ):
                print(f"正在测试 {device['name']}，请说话...")
                time.sleep(5)
                print(f"\n{device['name']} 测试完成")
                
        except Exception as e:
            print(f"{device['name']} 测试失败: {e}")
    
    print("\n测试完成！")

if __name__ == "__main__":
    test_microphone()