#!/usr/bin/env python3
"""
音频来源过滤器 - 识别和过滤不需要的系统音频
"""
import re
import time
from typing import List, Dict, Set

class AudioSourceFilter:
    """音频来源过滤器"""
    
    def __init__(self):
        # 营销/广告关键词
        self.marketing_keywords = {
            "点赞", "订阅", "转发", "打赏", "支持", "关注", "分享",
            "like", "subscribe", "share", "follow", "donate", "支持",
            "请多多", "多多关注", "多多支持"
        }
        
        # 视频平台特征词
        self.platform_keywords = {
            "youtube", "bilibili", "抖音", "快手", "小红书",
            "up主", "博主", "主播", "直播", "视频",
            "频道", "channel", "播放", "观看"
        }
        
        # 常见的系统音频模式
        self.system_audio_patterns = [
            r"请不吝.*点赞.*订阅.*转发",  # 常见的营销套话
            r".*感谢.*观看.*支持.*",      # 感谢观看模式
            r".*下期.*再见.*拜拜.*",      # 结尾告别模式
            r".*关注.*订阅.*up主.*",     # B站常见模式
            r".*喜欢.*点赞.*收藏.*",     # 三连请求模式
        ]
        
        # 编译正则表达式
        self.compiled_patterns = [re.compile(pattern, re.IGNORECASE) for pattern in self.system_audio_patterns]
        
        # 重复内容检测
        self.recent_texts: List[Dict] = []
        self.max_recent_texts = 10
        self.repeat_threshold = 0.8  # 相似度阈值
        
    def is_marketing_content(self, text: str) -> bool:
        """检测是否为营销内容"""
        text_lower = text.lower()
        
        # 检查营销关键词密度
        marketing_count = sum(1 for keyword in self.marketing_keywords if keyword in text_lower)
        marketing_density = marketing_count / max(len(text.split()), 1)
        
        if marketing_density > 0.3:  # 30%以上的词是营销词汇
            return True
            
        # 检查视频平台关键词
        platform_count = sum(1 for keyword in self.platform_keywords if keyword in text_lower)
        if platform_count >= 2 and marketing_count >= 1:  # 平台词+营销词组合
            return True
            
        return False
    
    def matches_system_pattern(self, text: str) -> bool:
        """检测是否匹配系统音频模式"""
        for pattern in self.compiled_patterns:
            if pattern.search(text):
                return True
        return False
    
    def is_repeated_content(self, text: str) -> bool:
        """检测是否为重复内容"""
        current_time = time.time()
        
        # 清理过期的记录（5分钟前的）
        self.recent_texts = [
            record for record in self.recent_texts 
            if current_time - record['timestamp'] < 300
        ]
        
        # 检查与最近文本的相似度
        for record in self.recent_texts:
            similarity = self._calculate_similarity(text, record['text'])
            if similarity > self.repeat_threshold:
                print(f"[音频过滤] 检测到重复内容，相似度: {similarity:.2f}")
                return True
        
        # 添加当前文本到记录
        self.recent_texts.append({
            'text': text,
            'timestamp': current_time
        })
        
        # 保持记录数量限制
        if len(self.recent_texts) > self.max_recent_texts:
            self.recent_texts = self.recent_texts[-self.max_recent_texts:]
        
        return False
    
    def _calculate_similarity(self, text1: str, text2: str) -> float:
        """计算两个文本的相似度"""
        # 简单的词汇重叠相似度
        words1 = set(text1.lower().split())
        words2 = set(text2.lower().split())
        
        if not words1 and not words2:
            return 1.0
        if not words1 or not words2:
            return 0.0
            
        intersection = words1.intersection(words2)
        union = words1.union(words2)
        
        return len(intersection) / len(union)
    
    def should_skip_translation(self, text: str) -> tuple[bool, str]:
        """
        判断是否应该跳过翻译
        返回: (should_skip, reason)
        """
        # 检查长度
        if len(text.strip()) < 3:
            return True, "文本过短"
        
        # 检查营销内容
        if self.is_marketing_content(text):
            return True, "疑似营销内容"
        
        # 检查系统音频模式
        if self.matches_system_pattern(text):
            return True, "匹配系统音频模式"
        
        # 检查重复内容
        if self.is_repeated_content(text):
            return True, "重复内容"
        
        # 检查特定的问题文本
        problematic_phrases = [
            "请不吝点赞",
            "订阅转发打赏",
            "支持明镜与点点栏目"
        ]
        
        for phrase in problematic_phrases:
            if phrase in text:
                return True, f"包含问题短语: {phrase}"
        
        return False, "通过检查"

# 创建全局过滤器实例
audio_filter = AudioSourceFilter()

def filter_audio_text(text: str) -> bool:
    """
    过滤音频文本的便捷函数
    返回: True 表示应该处理，False 表示应该跳过
    """
    should_skip, reason = audio_filter.should_skip_translation(text)
    
    if should_skip:
        print(f"[音频过滤] 跳过处理: {reason} | 内容: '{text[:50]}{'...' if len(text) > 50 else ''}'")
        return False
    else:
        print(f"[音频过滤] {reason} | 内容: '{text[:30]}{'...' if len(text) > 30 else ''}'")
        return True