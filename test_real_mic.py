#!/usr/bin/env python3
"""
测试真实麦克风音频音量
找出为什么用户说话没有反应
"""

import numpy as np
import sounddevice as sd
import time
from collections import deque

def test_real_microphone_levels():
    """测试真实麦克风音频音量"""
    print("=== 真实麦克风音频音量测试 ===\n")
    
    print("请对着麦克风说话，我们来监测音频音量...")
    print("测试将运行15秒，请在此期间正常说话\n")
    
    sample_rate = 48000
    chunk_size = 4800  # 0.1秒
    
    volume_history = deque(maxlen=50)  # 保存最近5秒的音量
    max_volume = 0
    speech_detected = False
    
    def audio_callback(indata, frames, time, status):
        nonlocal max_volume, speech_detected
        
        if status:
            print(f"音频状态错误: {status}")
            return
        
        # 转换为单声道
        if len(indata.shape) > 1:
            audio_mono = np.mean(indata, axis=1)
        else:
            audio_mono = indata[:, 0]
        
        # 计算RMS音量
        volume = np.sqrt(np.mean(audio_mono**2))
        volume_history.append(volume)
        
        if volume > max_volume:
            max_volume = volume
        
        # 使用当前阈值检测
        current_threshold = 0.006
        if volume > current_threshold:
            if not speech_detected:
                print(f"🎤 检测到语音! 音量: {volume:.4f}")
                speech_detected = True
        else:
            if speech_detected and len(volume_history) > 6:  # 0.6秒检查
                recent_avg = np.mean(list(volume_history)[-6:])
                if recent_avg <= current_threshold:
                    print(f"🔇 语音结束, 平均音量: {recent_avg:.4f}")
                    speech_detected = False
        
        # 实时显示音量
        bar_length = int(volume * 1000)  # 音量条
        bar = "█" * min(bar_length, 50)
        print(f"\r音量: {volume:.4f} |{bar:<50}| 阈值: {current_threshold}", end="", flush=True)
    
    try:
        with sd.InputStream(
            callback=audio_callback,
            channels=1,
            samplerate=sample_rate,
            blocksize=chunk_size,
            dtype=np.float32
        ):
            print("开始监听麦克风...")
            time.sleep(15)
            
    except Exception as e:
        print(f"麦克风测试失败: {e}")
        return None
    
    print(f"\n\n=== 测试结果 ===")
    print(f"最大音量: {max_volume:.4f}")
    print(f"当前阈值: 0.006")
    print(f"平均音量: {np.mean(volume_history):.4f}")
    
    if max_volume < 0.003:
        print("❌ 麦克风音量过低，建议:")
        print("   1. 检查麦克风音量设置")
        print("   2. 靠近麦克风说话")
        print("   3. 调整系统音频输入音量")
        suggested_threshold = max_volume * 0.3
        print(f"   4. 或将阈值降低到: {suggested_threshold:.4f}")
    elif max_volume < 0.006:
        print("⚠️  麦克风音量偏低")
        suggested_threshold = max_volume * 0.6
        print(f"建议将阈值调整到: {suggested_threshold:.4f}")
    else:
        print("✅ 麦克风音量正常")
    
    return max_volume

def test_adjusted_threshold():
    """使用调整后的阈值测试"""
    max_vol = test_real_microphone_levels()
    
    if max_vol and max_vol < 0.006:
        new_threshold = max(max_vol * 0.5, 0.002)  # 但不要太低
        print(f"\n=== 建议调整阈值到: {new_threshold:.4f} ===")
        
        response = input(f"是否要自动调整阈值到 {new_threshold:.4f}? (y/n): ")
        if response.lower() == 'y':
            # 更新阈值
            update_threshold_in_code(new_threshold)

def update_threshold_in_code(new_threshold):
    """更新代码中的阈值"""
    print(f"正在更新realtime_translator.py中的阈值到 {new_threshold:.4f}...")
    
    try:
        with open('/Users/<USER>/WorkSpace/obs-ai-translator/realtime_translator.py', 'r') as f:
            content = f.read()
        
        # 替换阈值
        old_line = "        self.speech_threshold = 0.006   # 降低语音检测阈值"
        new_line = f"        self.speech_threshold = {new_threshold:.4f}   # 自适应调整的语音检测阈值"
        
        if old_line in content:
            content = content.replace(old_line, new_line)
            
            with open('/Users/<USER>/WorkSpace/obs-ai-translator/realtime_translator.py', 'w') as f:
                f.write(content)
            
            print(f"✅ 阈值已更新为: {new_threshold:.4f}")
        else:
            print("❌ 未找到阈值设置行，请手动调整")
            
    except Exception as e:
        print(f"❌ 更新失败: {e}")

if __name__ == "__main__":
    print("这个测试将帮助找出为什么真实麦克风没有反应")
    print("请确保:")
    print("1. 麦克风已正确连接")
    print("2. 系统音频权限已允许")
    print("3. 麦克风未被其他应用独占\n")
    
    test_adjusted_threshold()