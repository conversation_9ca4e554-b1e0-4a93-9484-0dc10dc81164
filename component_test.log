2025-08-27 14:19:17,734 [INFO] root: 开始执行组件隔离测试...
2025-08-27 14:19:17,734 [INFO] root: === 测试VAD组件 ===
2025-08-27 14:19:17,842 [INFO] root: VAD配置: 采样率=16000, 帧时长=30ms, 帧大小=480
2025-08-27 14:19:17,859 [INFO] root: VAD测试 - silence: 非语音 (耗时: 0.005ms)
2025-08-27 14:19:17,859 [INFO] root: VAD测试 - noise: 语音 (耗时: 0.004ms)
2025-08-27 14:19:17,859 [INFO] root: VAD测试 - speech: 语音 (耗时: 0.003ms)
2025-08-27 14:19:19,864 [INFO] root: === 测试Whisper ASR组件 ===
2025-08-27 14:19:20,821 [INFO] root: 加载Whisper模型...
2025-08-27 14:19:21,131 [INFO] root: 模型加载耗时: 0.31秒
2025-08-27 14:19:21,131 [INFO] root: 生成测试音频...
2025-08-27 14:19:21,132 [INFO] root: 开始语音识别...
2025-08-27 14:19:21,347 [INFO] root: 识别结果: ''
2025-08-27 14:19:21,347 [INFO] root: 识别耗时: 0.21秒
2025-08-27 14:19:21,347 [INFO] root: no_speech_prob: N/A
2025-08-27 14:19:23,355 [INFO] root: === 测试OpenAI翻译组件 ===
2025-08-27 14:19:23,645 [INFO] root: 翻译测试 1: '你好，这是一个测试句子。'
2025-08-27 14:19:25,384 [INFO] httpx: HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-27 14:19:25,393 [INFO] root: 翻译结果: 'Hello, this is a test sentence.' (耗时: 1.75秒)
2025-08-27 14:19:26,394 [INFO] root: 翻译测试 2: '今天天气很好。'
2025-08-27 14:19:27,559 [INFO] httpx: HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-27 14:19:27,562 [INFO] root: 翻译结果: 'The weather is very nice today.' (耗时: 1.17秒)
2025-08-27 14:19:28,566 [INFO] root: 翻译测试 3: '我正在测试翻译功能。'
2025-08-27 14:19:29,692 [INFO] httpx: HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-27 14:19:29,693 [INFO] root: 翻译结果: 'I am testing the translation function.' (耗时: 1.13秒)
2025-08-27 14:19:32,703 [INFO] root: === 测试Edge TTS组件 ===
2025-08-27 14:19:32,800 [INFO] root: TTS测试 1: 'Hello, this is a test.'
2025-08-27 14:19:34,232 [INFO] root: TTS完成: 音频大小 16416 字节 (耗时: 1.43秒)
2025-08-27 14:19:34,232 [INFO] root: TTS测试 2: 'Today is a beautiful day.'
2025-08-27 14:19:35,459 [INFO] root: TTS完成: 音频大小 14256 字节 (耗时: 1.23秒)
2025-08-27 14:19:35,460 [INFO] root: TTS测试 3: 'Testing text-to-speech functionality.'
2025-08-27 14:19:36,802 [INFO] root: TTS完成: 音频大小 18432 字节 (耗时: 1.34秒)
2025-08-27 14:19:38,805 [INFO] root: === 测试多线程行为 ===
2025-08-27 14:19:40,844 [INFO] root: 线程测试完成: 生产10, 消费10, 错误0
2025-08-27 14:19:42,850 [INFO] root: === 测试内存使用 ===
2025-08-27 14:19:42,851 [ERROR] root: 内存测试失败: No module named 'psutil'
2025-08-27 14:19:44,857 [INFO] root: === 组件测试报告 ===
