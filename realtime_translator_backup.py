#!/usr/bin/env python3
"""
实时同声传译核心模块
"""
import asyncio
import numpy as np
from collections import deque
import webrtcvad
import whisper
from openai import OpenAI
import edge_tts
import io
import wave
import time
import os
from threading import Thread, Lock, RLock, Event
from queue import Queue, Empty
import soundfile as sf
import logging
from audio_filter import filter_audio_text
from reliability_utils import (
    CircuitBreaker, CircuitBreakerConfig, BoundedBuffer, ResourceMonitor,
    HealthMonitor, ManagedWorkerThread, SystemRecoveryManager,
    timeout_handler, retry_with_exponential_backoff, safe_execute
)

class RealtimeTranslator:
    """实时同声传译引擎"""
    
    def __init__(self, source_lang='zh', target_lang='en', sample_rate=16000, audio_router=None, enable_tts=True):
        self.source_lang = source_lang
        self.target_lang = target_lang
        self.sample_rate = sample_rate
        self.audio_router = audio_router  # 添加音频路由器引用
        self.enable_tts = enable_tts  # TTS功能开关
        
        # Thread synchronization
        self.state_lock = RLock()  # For VAD state management
        self.buffer_lock = RLock()  # For buffer operations
        self.shutdown_event = Event()  # For graceful shutdown
        
        # VAD配置
        self.vad = webrtcvad.Vad(2)  # 敏感度 0-3
        self.frame_duration = 30  # ms
        self.frame_size = int(self.sample_rate * self.frame_duration / 1000)
        
        # Whisper模型 - 升级到更准确的模型
        self.whisper_model = whisper.load_model("medium")  # 更好的识别准确率
        
        # OpenAI客户端（用于翻译）with timeout
        api_key = os.getenv('OPENAI_API_KEY')
        if not api_key:
            raise ValueError("请设置环境变量 OPENAI_API_KEY")
        self.openai_client = OpenAI(
            api_key=api_key,
            timeout=30.0  # 30 second timeout for all requests
        )
        
        # Bounded buffers with overflow protection
        self.audio_buffer = deque(maxlen=int(self.sample_rate * 5))  # 5秒缓冲
        self.speech_buffer = BoundedBuffer(
            max_size=int(self.sample_rate * 30),  # Max 30 seconds of audio
            overflow_strategy="drop_oldest"
        )
        
        # 处理队列
        self.recognition_queue = Queue(maxsize=10)
        self.translation_queue = Queue(maxsize=10)
        self.tts_queue = Queue(maxsize=10)
        
        # VAD状态 (protected by state_lock)
        self.is_speaking = False
        self.silence_frames = 0
        self.min_speech_frames = 8   # 降低到8帧，更容易开始录音
        self.max_silence_frames = 45  # 增加到45帧（约1.35s），允许更长的说话停顿
        
        # Circuit breakers for external services
        self.openai_circuit_breaker = CircuitBreaker(
            CircuitBreakerConfig(failure_threshold=3, recovery_timeout=60)
        )
        self.tts_circuit_breaker = CircuitBreaker(
            CircuitBreakerConfig(failure_threshold=5, recovery_timeout=30)
        )
        
        # Resource monitoring
        self.resource_monitor = ResourceMonitor(memory_threshold_mb=800, cpu_threshold=85.0)
        self.health_monitor = HealthMonitor(check_interval=30)
        
        # Recovery manager
        self.recovery_manager = SystemRecoveryManager()
        self._register_recovery_strategies()
        
        # Worker threads (managed, non-daemon)
        self.workers = []
        
        # 启动处理线程
        self._start_workers()
        
        # 启动健康监控
        self._start_health_monitoring()
    
    def _start_workers(self):
        """启动后台处理线程（非daemon，可控制关闭）"""
        # Create managed worker threads
        recognition_worker = ManagedWorkerThread(
            "ASR-Worker", 
            self._process_recognition_item,
            self.recognition_queue
        )
        translation_worker = ManagedWorkerThread(
            "Translation-Worker",
            self._process_translation_item,
            self.translation_queue
        )
        tts_worker = ManagedWorkerThread(
            "TTS-Worker",
            self._process_tts_item, 
            self.tts_queue
        )
        
        self.workers = [recognition_worker, translation_worker, tts_worker]
        
        # Start all workers
        for worker in self.workers:
            worker.start()
            
        logging.info("All worker threads started successfully")
    
    def process_audio(self, audio_data):
        """处理输入音频流"""
        if len(audio_data) == 0:
            return
            
        try:
            # 确保是单声道
            if len(audio_data.shape) > 1:
                audio_data = np.mean(audio_data, axis=1)
            
            # 转换为16位PCM用于VAD
            audio_16bit = (np.clip(audio_data, -1.0, 1.0) * 32767).astype(np.int16)
            
            # 改进的VAD：基于音量阈值和能量分布
            volume = np.sqrt(np.mean(audio_data**2))
            # 平衡的阈值设置，既要灵敏又要准确
            is_speech = volume > 0.006 and volume < 0.9  # 进一步降低到0.006，但保持合理范围
            
            # 增加动态范围检查，确保是真实语音而非噪音
            if is_speech:
                audio_std = np.std(audio_data)
                # 如果音频变化太小，可能是持续噪音而非语音
                if audio_std < 0.0005:
                    is_speech = False
            
            if is_speech:
                with self.buffer_lock:
                    self.speech_buffer.add(audio_16bit)
                with self.state_lock:
                    self.silence_frames = 0
                    
                    if not self.is_speaking:
                        self.is_speaking = True
                        print(f"[VAD] 检测到说话开始 (音量: {volume:.3f})")
                
                # 检查缓冲区大小和资源使用情况
                with self.buffer_lock:
                    buffer_stats = self.speech_buffer.get_stats()
                    
                if buffer_stats['utilization'] > 80:  # 80% full
                    print(f"[VAD] 缓冲区接近上限，处理语音片段: {buffer_stats['current_size']} items")
                    self._process_speech_segment(force_reset=False)  # 不强制重置，保持连续性
                    
                # Check memory usage
                if self.resource_monitor.is_memory_critical():
                    print(f"[VAD] 内存使用过高，强制清理缓冲区")
                    self._emergency_buffer_cleanup()
                    
            else:
                with self.state_lock:
                    self.silence_frames += 1
                    
                    if self.is_speaking and self.silence_frames > self.max_silence_frames:
                        # 说话结束，处理剩余音频
                        with self.buffer_lock:
                            buffer_size = self.speech_buffer.size()
                        print(f"[VAD] 检测到说话结束，处理剩余 {buffer_size} items")
                        
                        if buffer_size > self.sample_rate * 0.3 / 1024:  # Adjusted for new buffer structure
                            self._process_speech_segment(force_reset=True)
                        else:
                            print(f"[VAD] 音频片段太短({buffer_size} items)，跳过处理")
                        # 确保状态完全重置
                        self._reset_vad_state()
                    
        except Exception as e:
            print(f"[VAD] 处理音频错误: {e}")
            # 安全重置状态防止卡死
            safe_execute(lambda: self._reset_vad_state(), log_errors=True)
    
    def _detect_speech(self, audio_16bit):
        """语音活动检测"""
        try:
            # 确保音频长度正确
            if len(audio_16bit) < self.frame_size:
                return False
            
            frame = audio_16bit[:self.frame_size].tobytes()
            return self.vad.is_speech(frame, self.sample_rate)
        except:
            return False
    
    def _reset_vad_state(self):
        """重置VAD状态（线程安全）"""
        with self.state_lock:
            self.is_speaking = False
            self.silence_frames = 0
        with self.buffer_lock:
            self.speech_buffer = BoundedBuffer(
                max_size=int(self.sample_rate * 30),
                overflow_strategy="drop_oldest"
            )
        print("[VAD] 状态已重置，准备处理下一段语音")
    
    def _process_speech_segment(self, force_reset=False):
        """处理语音片段（线程安全）"""
        with self.buffer_lock:
            if self.speech_buffer.size() > 0:
                audio_items = self.speech_buffer.get_all_and_clear()
                audio_segment = np.concatenate(audio_items) if audio_items else np.array([])
                
                if len(audio_segment) > 0:
                    # 添加到识别队列，如果满了先清空一个
                    if self.recognition_queue.full():
                        try:
                            self.recognition_queue.get_nowait()
                            print("[VAD] 识别队列已满，丢弃最旧数据")
                        except Empty:
                            pass
                    
                    try:
                        self.recognition_queue.put(audio_segment, timeout=1.0)
                        print(f"[VAD] 音频片段已加入识别队列: {len(audio_segment)} samples")
                    except Exception as e:
                        print(f"[VAD] 加入识别队列失败: {e}")
                
                if not force_reset and len(audio_segment) > 0:
                    # 保留最后0.5秒用于上下文
                    keep_samples = int(self.sample_rate * 0.5)
                    if len(audio_segment) > keep_samples:
                        context_audio = audio_segment[-keep_samples:]
                        self.speech_buffer.add(context_audio)
                        print("[VAD] 保留上下文音频用于连续识别")
    
    def _process_recognition_item(self, audio_segment):
        """处理单个识别任务（由ManagedWorkerThread调用）"""
        self._recognition_worker_impl(audio_segment)
        
    @timeout_handler(10)  # 10 second timeout for Whisper
    @retry_with_exponential_backoff(max_retries=2, base_delay=0.5)
    def _recognition_worker_impl(self, audio_segment):
        """语音识别实现（带超时保护）"""
        try:
            print(f"[ASR] 开始识别音频片段: {len(audio_segment)} samples")
            
            # 转换为float32并重采样到16kHz (Whisper要求)
            audio_float = audio_segment.astype(np.float32) / 32767.0
            
            # 先检查是否为静音或噪声
            audio_energy = np.sqrt(np.mean(audio_float**2))
            print(f"[ASR] 音频能量: {audio_energy:.6f}")
            
            # 平衡的静音检测 - 防止幻听但不过度拦截真实语音
            is_silence = False
            
            # 1. 基本能量阈值检查 - 降低阈值，只过滤极低能量
            if audio_energy < 0.00005:  # 只过滤极低能量音频
                is_silence = True
                print("[ASR] 检测到极低能量音频")
            
            # 2. 最大音量检查 - 降低阈值
            max_amplitude = np.max(np.abs(audio_float))
            if max_amplitude < 0.0005:  # 降低阈值
                is_silence = True
                print(f"[ASR] 检测到极低最大音量: {max_amplitude:.6f}")
            
            # 3. 动态范围检查 - 只在能量很低时才检查动态范围
            if audio_energy < 0.0001:  # 只有在很低能量时才检查动态范围
                audio_std = np.std(audio_float)
                if audio_std < 0.00005:  # 降低阈值
                    is_silence = True
                    print(f"[ASR] 检测到低动态范围噪音: {audio_std:.6f}")
            
            if is_silence:
                print("[ASR] 检测到静音/噪音，跳过识别避免幻听")
                return
            
            # 音频重采样从48kHz到16kHz
            if len(audio_float) > 0:
                try:
                        # 首先尝试使用高质量重采样
                        import librosa
                        audio_16k = librosa.resample(audio_float, orig_sr=48000, target_sr=16000, res_type='kaiser_fast')
                        print(f"[ASR] 高质量重采样: {len(audio_16k)} samples @ 16kHz")
                    except Exception as e:
                        print(f"[ASR] 高质量重采样失败: {e}，使用简单重采样")
                        # 降级：使用scipy简单重采样
                        try:
                            from scipy import signal
                            # 计算重采样比例
                            resample_ratio = 16000 / 48000
                            new_length = int(len(audio_float) * resample_ratio)
                            audio_16k = signal.resample(audio_float, new_length)
                            print(f"[ASR] 简单重采样: {len(audio_16k)} samples @ 16kHz")
                        except Exception as e2:
                            print(f"[ASR] 简单重采样也失败: {e2}，跳过处理")
                            continue
                else:
                    print("[ASR] 音频数据为空，跳过")
                    continue
                
                # Whisper识别
                start_time = time.time()
                # 优化的Whisper参数 - 提高识别质量
                whisper_params = {
                    "language": self.source_lang,
                    "fp16": False,
                    "verbose": False,
                    "no_speech_threshold": 0.6,  # 平衡的阈值
                    "logprob_threshold": -1.0,   # 适中的置信度要求
                    "temperature": 0.0,  # 确定性输出
                    "beam_size": 5,  # beam search提高准确率
                    "patience": 1.0,
                    "condition_on_previous_text": False,  # 避免上下文干扰
                    "initial_prompt": None,  # 不使用提示
                    "suppress_tokens": "-1",  # 不抑制token
                    "without_timestamps": True,  # 不生成时间戳
                    "word_timestamps": False,
                    "prepend_punctuations": "\"'¿([{-",
                    "append_punctuations": "\"'.。,，!！?？:：\")]}、"
                }
                
                # 针对中文的特殊优化
                if self.source_lang == 'zh':
                    whisper_params.update({
                        "temperature": 0.1,  # 稍微增加一点随机性帮助识别
                        "compression_ratio_threshold": 2.4,  # 适合中文的压缩比
                        "no_speech_threshold": 0.4,  # 更宽松的语音检测
                        "logprob_threshold": -1.2,  # 稍微宽松的置信度要求
                    })
                else:
                    # 英文也稍微调整
                    whisper_params.update({
                        "temperature": 0.1,  # 增加一点随机性
                        "no_speech_threshold": 0.5,  # 英文也稍微宽松
                    })
                
                result = self.whisper_model.transcribe(audio_16k, **whisper_params)
                
                text = result['text'].strip()
                duration = time.time() - start_time
                
                # 检查是否是幻听内容
                # Whisper常见的幻听模式
                hallucination_patterns = [
                    "请不吝点赞",  # 这个特定的重复文本
                    "订阅转发打赏",
                    "明镜与点点栏目",
                    "明镜与点点",
                    "谢谢大家",
                    "感谢观看", 
                    "下期再见",
                    "支持明镜",
                    "打赏支持",
                    "请多多支持",
                    "Thank you for watching",
                    "Subscribe and like",
                    "Please support",
                    # 空白或重复字符
                    "...", 
                    "   ",
                    "的的的",
                    "啊啊啊"
                ]
                
                # 检查no_speech概率
                no_speech_prob = result.get('no_speech_prob', 0)
                print(f"[ASR] no_speech概率: {no_speech_prob:.3f}")
                
                is_hallucination = False
                
                # 1. 检查no_speech概率 - 平衡的阈值
                if no_speech_prob > 0.7:  # 提高到0.7，更平衡
                    is_hallucination = True
                    print(f"[ASR] 检测到高no_speech概率({no_speech_prob:.3f})，可能是幻听")
                
                # 2. 检查是否包含幻听模式
                for pattern in hallucination_patterns:
                    if pattern in text:
                        is_hallucination = True
                        print(f"[ASR] 检测到幻听模式: {pattern}")
                        break
                
                # 3. 检查识别结果的平均logprob - 平衡的置信度要求
                if 'segments' in result and result['segments']:
                    avg_logprob = result['segments'][0].get('avg_logprob', 0)
                    print(f"[ASR] 平均logprob: {avg_logprob:.3f}")
                    if avg_logprob < -1.2:  # 适中的阈值，不过度拦截
                        is_hallucination = True
                        print(f"[ASR] 低置信度识别({avg_logprob:.3f})，可能是幻听")
                
                # 4. 检查文本长度和重复性
                if len(text) > 50:  # 异常长的识别结果
                    char_counts = {}
                    for char in text:
                        char_counts[char] = char_counts.get(char, 0) + 1
                    # 检查是否有字符过度重复
                    max_char_count = max(char_counts.values()) if char_counts else 0
                    if max_char_count > len(text) * 0.3:  # 某个字符占比超过30%
                        is_hallucination = True
                        print("[ASR] 检测到字符过度重复，可能是幻听")
                
                if is_hallucination:
                    print(f"[ASR] 跳过幻听内容: '{text[:50]}'")
                    continue
                    
                if text and len(text.strip()) > 1 and not is_hallucination:
                    print(f"[ASR] 有效识别结果: '{text}' (耗时: {duration:.2f}s)")
                    
                    # 使用音频过滤器检查是否应该跳过翻译
                    if filter_audio_text(text):
                        # 添加到翻译队列，如果满了先清空一个
                        if self.translation_queue.full():
                            try:
                                self.translation_queue.get_nowait()
                                print("[ASR] 翻译队列已满，丢弃最旧数据")
                            except:
                                pass
                        self.translation_queue.put(text)
                        print("[ASR] 已添加到翻译队列")
                    else:
                        print("[ASR] 音频内容被过滤器拦截，跳过翻译")
                else:
                    print(f"[ASR] 无有效识别结果或文本过短: '{text}' (耗时: {duration:.2f}s)")
                        
            except Exception as e:
                print(f"[ASR] 错误: {e}")
                import traceback
                traceback.print_exc()
                # 继续处理下一个任务，不要破坏整个流程
    
    def _translation_worker(self):
        """翻译工作线程"""
        while True:
            try:
                text = self.translation_queue.get()
                print(f"[翻译] 开始翻译: '{text}'")
                
                # 使用改进的GPT进行翻译
                start_time = time.time()
                # 更详细的翻译提示词
                system_prompt = f"""你是一个专业的同声传译员。请将以下{self.source_lang}文本翻译成{self.target_lang}。

翻译要求：
1. 保持原意的准确性和完整性
2. 使用自然流畅的表达方式
3. 适合口语交流的语调
4. 如果是专业术语，请使用准确的专业词汇
5. 只返回翻译结果，不要额外说明

文本："""
                
                response = self.openai_client.chat.completions.create(
                    model="gpt-4o-mini",  # 使用更先进的模型
                    messages=[
                        {"role": "system", "content": system_prompt},
                        {"role": "user", "content": text}
                    ],
                    temperature=0.1,  # 降低随意性，提高一致性
                    max_tokens=300,   # 增加最大令牌数
                    top_p=0.9,       # 控制输出多样性
                    frequency_penalty=0.0,
                    presence_penalty=0.0
                )
                
                translated = response.choices[0].message.content.strip()
                duration = time.time() - start_time
                
                # 翻译质量检查
                if self._is_valid_translation(text, translated):
                    print(f"[翻译] 翻译结果: '{translated}' (耗时: {duration:.2f}s)")
                    
                    # 根据TTS开关决定是否播放语音
                    if self.enable_tts:
                        # 添加到TTS队列，如果满了先清空一个
                        if self.tts_queue.full():
                            try:
                                self.tts_queue.get_nowait()
                                print("[翻译] TTS队列已满，丢弃最旧数据")
                            except:
                                pass
                        self.tts_queue.put(translated)
                        print("[翻译] 已添加到TTS队列")
                    else:
                        print("[翻译] TTS已禁用，跳过语音合成")
                else:
                    print(f"[翻译] 翻译质量不佳，跳过: '{translated}' (耗时: {duration:.2f}s)")
                    
            except Exception as e:
                print(f"[翻译] 错误: {e}")
                import traceback
                traceback.print_exc()
                # 继续处理下一个任务，不要破坏整个流程
    
    def _tts_worker(self):
        """语音合成工作线程"""
        while True:
            try:
                text = self.tts_queue.get()
                print(f"[TTS] 开始合成: '{text}'")
                
                # 使用edge-tts合成
                start_time = time.time()
                asyncio.run(self._synthesize_speech(text))
                print(f"[TTS] 完成 (耗时: {time.time()-start_time:.2f}s)")
                
            except Exception as e:
                print(f"[TTS] 错误: {e}")
                import traceback
                traceback.print_exc()
                # 继续处理下一个任务，不要破坏整个流程
    
    async def _synthesize_speech(self, text):
        """异步语音合成"""
        voice = "en-US-JennyNeural" if self.target_lang == 'en' else "zh-CN-XiaoxiaoNeural"
        
        communicate = edge_tts.Communicate(text, voice)
        audio_data = b""
        
        async for chunk in communicate.stream():
            if chunk["type"] == "audio":
                audio_data += chunk["data"]
        
        # 播放音频到输出设备
        if audio_data:
            try:
                # 将MP3数据转换为numpy数组
                audio_np = self._convert_audio_for_playback(audio_data)
                if audio_np is not None:
                    
                    # 如果有音频路由器，输出到BlackHole (供OBS捕获)
                    if self.audio_router:
                        try:
                            self.audio_router.send_to_output(audio_np)
                            print(f"[音频] 已输出到BlackHole: {text[:30]}...")
                        except Exception as e:
                            print(f"[音频] BlackHole输出错误: {e}")
                    
                    # 同时输出到扬声器 (供用户监听)
                    try:
                        import sounddevice as sd
                        
                        # 优先播放到AirPods或扬声器，避免与麦克风设备冲突
                        playback_device = self._find_best_output_device()
                        
                        # 使用非阻塞播放，避免设备冲突
                        sd.play(audio_np, 48000, device=playback_device)
                        print(f"[音频] 播放翻译语音到设备{playback_device}: {text[:30]}...")
                        
                    except Exception as e:
                        print(f"[音频] 扬声器播放错误（可能设备冲突）: {e}")
                        # 尝试使用默认设备播放
                        try:
                            import sounddevice as sd
                            sd.play(audio_np, 48000)
                            print(f"[音频] 使用默认设备播放: {text[:30]}...")
                        except Exception as e2:
                            print(f"[音频] 默认设备播放也失败: {e2}")
                    
            except Exception as e:
                print(f"[音频播放] 总体错误: {e}")
        
        return audio_data
    
    def _find_best_output_device(self):
        """查找最佳的音频输出设备（优先选择真实扬声器，避免与麦克风冲突）"""
        try:
            import sounddevice as sd
            devices = sd.query_devices()
            
            # 获取默认输入设备，避免选择相同的设备造成冲突
            try:
                default_input = sd.default.device[0]
                if default_input is not None:
                    input_device_name = devices[default_input]['name'].lower()
                else:
                    input_device_name = ""
            except:
                input_device_name = ""
            
            # 优先级顺序：AirPods > 扬声器 > 其他输出设备
            priority_keywords = [
                ['airpods'],                      # AirPods最优先
                ['扬声器', 'speaker', 'speakers'], # 内置扬声器
                ['display', 'monitor'],           # 外部显示器
                ['headphones', 'headset'],        # 耳机
            ]
            
            for priority_group in priority_keywords:
                for i, device in enumerate(devices):
                    if device['max_output_channels'] > 0:
                        device_name = device['name'].lower()
                        
                        # 避免选择与麦克风相同的设备
                        if input_device_name and input_device_name in device_name:
                            continue
                            
                        for keyword in priority_group:
                            if keyword.lower() in device_name:
                                print(f"[音频] 选择输出设备: {device['name']} (ID: {i})")
                                return i
            
            # 如果没找到特定设备，使用第一个可用的输出设备（非虚拟，非输入设备）
            for i, device in enumerate(devices):
                device_name = device['name'].lower()
                if (device['max_output_channels'] > 0 and 
                    not any(virt in device_name for virt in ['blackhole', 'vb-cable', 'ai同传', '多输出']) and
                    (not input_device_name or input_device_name not in device_name)):
                    print(f"[音频] 使用备选输出设备: {device['name']} (ID: {i})")
                    return i
            
            # 最后使用系统默认（None表示默认设备）
            print("[音频] 使用系统默认输出设备")
            return None
            
        except Exception as e:
            print(f"[音频] 查找输出设备错误: {e}")
            return None
    
    def _convert_audio_for_playback(self, audio_data):
        """将Edge-TTS的音频数据转换为可播放格式"""
        try:
            import io
            from pydub import AudioSegment
            import numpy as np
            
            # Edge-TTS输出MP3格式，转换为wav
            audio_segment = AudioSegment.from_mp3(io.BytesIO(audio_data))
            
            # 转换为48kHz立体声
            audio_segment = audio_segment.set_frame_rate(48000).set_channels(2)
            
            # 转换为numpy数组
            audio_np = np.array(audio_segment.get_array_of_samples(), dtype=np.float32)
            
            # 如果是立体声，重新reshape
            if audio_segment.channels == 2:
                audio_np = audio_np.reshape((-1, 2))
            
            # 归一化到[-1, 1]范围
            audio_np = audio_np / 32768.0
            
            return audio_np
            
        except Exception as e:
            print(f"[音频转换] 错误: {e}")
            return None
    
    def _is_valid_translation(self, original, translated):
        """检查翻译质量"""
        if not translated or len(translated.strip()) == 0:
            print(f"[翻译质量] 翻译为空")
            return False
            
        # 检查是否只是重复原文
        if translated.strip().lower() == original.strip().lower():
            print(f"[翻译质量] 翻译结果与原文相同")
            return False
            
        # 仅检查翻译系统返回的真正错误，不检查正常翻译内容
        # 移除了过于宽泛的词汇如"不能"、"无法"等，因为这些可能是正常翻译内容
        error_indicators = [
            "i cannot translate", "i can't translate", "sorry, i", "unable to translate",
            "error:", "api error"
        ]
        
        translated_lower = translated.lower()
        for indicator in error_indicators:
            if indicator in translated_lower:
                print(f"[翻译质量] 包含系统错误标识: {indicator}")
                return False
        
        # 计算字符长度（对中文更准确）
        original_chars = len(original.strip())
        translated_chars = len(translated.strip())
        
        # 计算词汇数量（对英文更准确）
        original_words = len(original.split())
        translated_words = len(translated.split())
        
        # 使用更智能的长度比较
        # 中文主要看字符数，英文主要看词数
        if self.source_lang == 'zh' and self.target_lang == 'en':
            # 中译英：中文字符通常翻译成更多英文单词
            # 一个中文字符大约对应0.5-2个英文单词
            if translated_words < original_chars * 0.3 or translated_words > original_chars * 2.5:
                print(f"[翻译质量] 中译英比例异常: {original_chars}中文字符 -> {translated_words}英文词, 比例{translated_words/max(original_chars,1):.2f}")
                return False
        elif self.source_lang == 'en' and self.target_lang == 'zh':
            # 英译中：英文单词通常翻译成较少的中文字符
            # 一个英文单词大约对应1-3个中文字符  
            if translated_chars < original_words * 0.5 or translated_chars > original_words * 4:
                print(f"[翻译质量] 英译中比例异常: {original_words}英文词 -> {translated_chars}中文字符, 比例{translated_chars/max(original_words,1):.2f}")
                return False
        else:
            # 其他语言对：使用宽松的比例
            min_ratio = 0.1  # 非常宽松
            max_ratio = 10.0  
            ratio = translated_chars / max(original_chars, 1)
            if ratio < min_ratio or ratio > max_ratio:
                print(f"[翻译质量] 长度比例异常: 原文{original_chars}字符, 译文{translated_chars}字符, 比例{ratio:.2f}")
                return False
        
        # 检查是否包含明显的广告或营销内容（可能是误捕获的系统音频）
        marketing_keywords = [
            "点赞", "订阅", "转发", "打赏", "支持", "关注",
            "like", "subscribe", "share", "follow", "donate"
        ]
        
        marketing_count = sum(1 for keyword in marketing_keywords if keyword in original.lower())
        if marketing_count >= 3:  # 如果包含3个或以上营销词汇
            print(f"[翻译质量] 疑似营销内容，跳过翻译: {original[:50]}...")
            return False
            
        print(f"[翻译质量] 通过检查: 原文{original_chars}字符 -> 译文{translated_chars}字符")
        return True
    
    def _monitor_worker(self):
        """监控队列状态"""
        while True:
            try:
                time.sleep(10)  # 每10秒检查一次
                rec_size = self.recognition_queue.qsize()
                trans_size = self.translation_queue.qsize()
                tts_size = self.tts_queue.qsize()
                
                if rec_size > 5 or trans_size > 5 or tts_size > 5:
                    print(f"[监控] 队列状态 - ASR:{rec_size}/10, 翻译:{trans_size}/10, TTS:{tts_size}/10")
                    
            except Exception as e:
                print(f"[监控] 错误: {e}")
    
    def reset_system(self):
        """手动重置整个系统"""
        print("[系统] 手动重置开始...")
        
        # 清空所有队列
        while not self.recognition_queue.empty():
            try:
                self.recognition_queue.get_nowait()
            except:
                break
                
        while not self.translation_queue.empty():
            try:
                self.translation_queue.get_nowait()
            except:
                break
                
        while not self.tts_queue.empty():
            try:
                self.tts_queue.get_nowait()
            except:
                break
        
        # 重置VAD状态
        self._reset_vad_state()
        
        print("[系统] 手动重置完成，系统已准备就绪")

class StreamingVAD:
    """流式VAD处理器"""
    
    def __init__(self, sample_rate=16000, frame_duration_ms=30):
        self.sample_rate = sample_rate
        self.frame_duration_ms = frame_duration_ms
        self.frame_size = int(sample_rate * frame_duration_ms / 1000)
        self.vad = webrtcvad.Vad(2)
        
        self.ring_buffer = deque(maxlen=30)  # 900ms的环形缓冲区
        self.triggered = False
        self.num_voiced_frames = 0
        self.num_unvoiced_frames = 0
        
    def process_frame(self, audio_frame):
        """处理单帧音频"""
        is_speech = self.vad.is_speech(audio_frame, self.sample_rate)
        
        self.ring_buffer.append((audio_frame, is_speech))
        
        if not self.triggered:
            # 等待语音开始
            self.num_voiced_frames = sum(1 for _, speech in self.ring_buffer if speech)
            
            if self.num_voiced_frames > 0.8 * len(self.ring_buffer):
                self.triggered = True
                return 'speech_start', self._get_buffered_audio()
        else:
            # 等待语音结束
            self.num_unvoiced_frames = sum(1 for _, speech in self.ring_buffer if not speech)
            
            if self.num_unvoiced_frames > 0.8 * len(self.ring_buffer):
                self.triggered = False
                self.ring_buffer.clear()
                return 'speech_end', None
        
        return 'speech_continue' if self.triggered else 'silence', None
    
    def _get_buffered_audio(self):
        """获取缓冲的音频"""
        return b''.join([frame for frame, _ in self.ring_buffer])