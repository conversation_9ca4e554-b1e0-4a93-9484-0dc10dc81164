#!/usr/bin/env python3
"""
测试AUHAL错误修复
验证新的音频设备管理器是否有效防止PaMacCore Error -10863
"""

import sys
import time
import numpy as np
from obs_ai_translator_mic import MicrophoneTranslatorSystem
import asyncio
import edge_tts
import io
from pydub import AudioSegment

def test_auhal_fix_integration():
    """测试AUHAL修复的完整集成"""
    print("=== 测试AUHAL修复集成 ===\n")
    
    print("1. 创建麦克风翻译系统（启用新的AUHAL修复）...")
    system = MicrophoneTranslatorSystem(
        enable_tts=True,
        source_lang='zh',
        target_lang='en'
    )
    
    print("2. 检查AUHAL修复组件...")
    translator = system.translator
    
    # 检查音频设备管理器
    if hasattr(translator, 'audio_device_manager'):
        print("✅ 音频设备管理器已集成")
        device_manager = translator.audio_device_manager
        print(f"   检测到设备数量: {len(device_manager.devices)}")
        
        # 测试安全设备查找
        exclude_devices = []
        if translator.audio_capture and hasattr(translator.audio_capture, 'device_id'):
            exclude_devices.append(translator.audio_capture.device_id)
        
        safe_device = device_manager.get_safe_output_device(exclude_devices)
        print(f"   安全输出设备: {safe_device}")
    else:
        print("❌ 音频设备管理器未集成")
        return False
    
    print("\n3. 测试TTS合成和安全播放...")
    
    # 模拟真实TTS场景
    async def test_tts_synthesis():
        print("   进行TTS合成...")
        text = "This is a test for AUHAL error fix"
        voice = "en-US-JennyNeural"
        
        communicate = edge_tts.Communicate(text, voice)
        audio_data = b""
        
        async for chunk in communicate.stream():
            if chunk["type"] == "audio":
                audio_data += chunk["data"]
        
        if audio_data:
            print("   转换音频格式...")
            audio_segment = AudioSegment.from_mp3(io.BytesIO(audio_data))
            audio_segment = audio_segment.set_frame_rate(48000).set_channels(1)
            samples = audio_segment.get_array_of_samples()
            audio_np = np.array(samples).astype(np.float32) / 32768.0
            
            print(f"   音频数据准备完成 ({len(audio_np)} 样本)")
            
            # 调用新的安全播放机制
            try:
                print("   调用AUHAL修复的安全播放...")
                
                # 获取安全输出设备
                exclude_devices = []
                if translator.audio_capture and hasattr(translator.audio_capture, 'device_id'):
                    exclude_devices.append(translator.audio_capture.device_id)
                
                playback_device = device_manager.get_safe_output_device(exclude_devices)
                
                if playback_device is not None:
                    print(f"   使用安全设备 {playback_device} 进行播放")
                    
                    # 使用设备预留机制
                    with device_manager.reserve_device(playback_device, "测试播放"):
                        import sounddevice as sd
                        sd.play(audio_np, 48000, device=playback_device, blocking=False)
                        
                        duration = len(audio_np) / 48000
                        time.sleep(duration + 0.5)
                        sd.stop()
                        
                        print("   ✅ AUHAL安全播放成功完成")
                        return True
                else:
                    print("   ⚠️  未找到安全输出设备")
                    return False
                    
            except Exception as e:
                print(f"   ❌ AUHAL安全播放失败: {e}")
                if "-10863" in str(e):
                    print("   🔍 仍然出现AUHAL Error -10863")
                return False
        else:
            print("   ❌ TTS合成失败")
            return False
    
    try:
        # 运行TTS测试
        success = asyncio.run(test_tts_synthesis())
        
        print("\n4. 清理系统...")
        system.translator.shutdown()
        
        if success:
            print("\n✅ AUHAL修复集成测试通过")
            return True
        else:
            print("\n❌ AUHAL修复集成测试失败")
            return False
            
    except Exception as e:
        print(f"\n测试异常: {e}")
        system.translator.shutdown()
        return False

def test_device_conflict_prevention():
    """测试设备冲突预防"""
    print("\n=== 测试设备冲突预防 ===\n")
    
    print("1. 创建翻译系统...")
    system = MicrophoneTranslatorSystem(
        enable_tts=True,
        source_lang='zh',
        target_lang='en'
    )
    
    try:
        device_manager = system.translator.audio_device_manager
        
        print("2. 测试并发设备访问保护...")
        
        # 获取一个输出设备
        safe_device = device_manager.get_safe_output_device()
        if safe_device is None:
            print("❌ 未找到可用设备")
            return False
        
        print(f"   测试设备: {safe_device}")
        
        # 测试设备预留
        print("   测试设备预留机制...")
        try:
            with device_manager.reserve_device(safe_device, "测试1"):
                print("   ✅ 设备预留成功")
                
                # 尝试重复预留同一设备（应该失败）
                try:
                    with device_manager.reserve_device(safe_device, "测试2"):
                        print("   ❌ 重复预留应该失败")
                        return False
                except RuntimeError as e:
                    print(f"   ✅ 正确阻止重复预留: {e}")
            
            print("   ✅ 设备正确释放")
            
        except Exception as e:
            print(f"   ❌ 设备预留测试失败: {e}")
            return False
        
        print("3. 测试输入设备排除...")
        
        # 模拟有输入设备的情况
        if system.translator.audio_capture and hasattr(system.translator.audio_capture, 'device_id'):
            input_device_id = system.translator.audio_capture.device_id
            print(f"   输入设备ID: {input_device_id}")
            
            # 获取安全设备时应该排除输入设备
            safe_device_with_exclusion = device_manager.get_safe_output_device([input_device_id])
            
            if safe_device_with_exclusion == input_device_id:
                print("   ❌ 未正确排除输入设备")
                return False
            else:
                print(f"   ✅ 正确排除输入设备，选择设备: {safe_device_with_exclusion}")
        
        print("\n✅ 设备冲突预防测试通过")
        return True
        
    except Exception as e:
        print(f"测试异常: {e}")
        return False
    finally:
        system.translator.shutdown()

def test_auhal_error_recovery():
    """测试AUHAL错误恢复"""
    print("\n=== 测试AUHAL错误恢复机制 ===\n")
    
    system = MicrophoneTranslatorSystem(
        enable_tts=True,
        source_lang='zh',
        target_lang='en'
    )
    
    try:
        device_manager = system.translator.audio_device_manager
        
        print("1. 模拟AUHAL错误场景...")
        
        # 获取设备信息
        original_device_count = len(device_manager.devices)
        print(f"   原始设备数量: {original_device_count}")
        
        print("2. 测试设备信息重新加载...")
        device_manager._update_device_info()
        new_device_count = len(device_manager.devices)
        
        if new_device_count == original_device_count:
            print("   ✅ 设备信息正确重载")
        else:
            print(f"   ⚠️  设备数量变化: {original_device_count} -> {new_device_count}")
        
        print("3. 测试音频系统重置...")
        import sounddevice as sd
        
        try:
            sd.stop()  # 模拟重置
            time.sleep(0.1)
            device_manager._update_device_info()
            print("   ✅ 音频系统重置成功")
        except Exception as e:
            print(f"   ❌ 音频系统重置失败: {e}")
            return False
        
        print("\n✅ AUHAL错误恢复测试通过")
        return True
        
    except Exception as e:
        print(f"测试异常: {e}")
        return False
    finally:
        system.translator.shutdown()

def main():
    """主测试函数"""
    print("=== PaMacCore AUHAL Error -10863 修复验证 ===\n")
    
    results = []
    
    # 1. 集成测试
    print("执行集成测试...")
    results.append(("集成测试", test_auhal_fix_integration()))
    
    # 2. 设备冲突预防测试
    print("执行设备冲突预防测试...")
    results.append(("设备冲突预防", test_device_conflict_prevention()))
    
    # 3. 错误恢复测试
    print("执行错误恢复测试...")
    results.append(("错误恢复", test_auhal_error_recovery()))
    
    # 汇总结果
    print("\n" + "="*50)
    print("测试结果汇总:")
    print("="*50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:<20} {status}")
        if result:
            passed += 1
    
    print("="*50)
    print(f"总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有AUHAL修复测试通过！")
        print("\n修复功能:")
        print("✅ 音频设备冲突预防")
        print("✅ 设备预留机制")
        print("✅ 安全设备选择")
        print("✅ AUHAL错误检测和恢复")
        print("✅ 输入设备排除")
        return True
    else:
        print("⚠️  部分测试失败，需要进一步调试")
        return False

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n测试被中断")
        sys.exit(1)
    except Exception as e:
        print(f"测试失败: {e}")
        sys.exit(1)