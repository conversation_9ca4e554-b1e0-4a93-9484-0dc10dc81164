#!/usr/bin/env python3
"""
实时同声传译核心模块 - 修复关键系统冻结问题
Fixed critical system freeze issues with:
1. Timeout protection for blocking operations
2. Bounded buffers with overflow protection  
3. Thread synchronization improvements
4. Circuit breaker patterns
5. Resource monitoring and recovery
6. Graceful shutdown mechanisms
"""
import asyncio
import numpy as np
from collections import deque
import webrtcvad
import whisper
from openai import OpenAI
import edge_tts
import io
import wave
import time
import os
from threading import Thread, Lock, RLock, Event
from queue import Queue, Empty
import soundfile as sf
import logging
from audio_filter import filter_audio_text
from reliability_utils import (
    CircuitBreaker, CircuitBreakerConfig, BoundedBuffer, ResourceMonitor,
    HealthMonitor, ManagedWorkerThread, SystemRecoveryManager,
    timeout_handler, retry_with_exponential_backoff, safe_execute
)
from contextlib import contextmanager

class AudioDeviceManager:
    """音频设备管理器 - 防止AUHAL错误"""
    
    def __init__(self):
        self.active_streams = {}
        self.device_locks = {}
        self.stream_lock = RLock()
        self.device_health = {}  # 跟踪设备健康状态
        self.error_counts = {}   # 跟踪设备错误次数
        self.last_playback_time = 0  # 跟踪上次播放时间
        self.min_playback_interval = 0.05  # 最小播放间隔（秒）
        self._update_device_info()
    
    def _update_device_info(self):
        """更新设备信息"""
        try:
            import sounddevice as sd
            self.devices = sd.query_devices()
            self.default_input = sd.default.device[0]
            self.default_output = sd.default.device[1]
            
            # 初始化设备健康状态
            for i, device in enumerate(self.devices):
                if i not in self.device_health:
                    self.device_health[i] = True
                    self.error_counts[i] = 0
                    
        except Exception as e:
            print(f"[音频] 获取设备信息失败: {e}")
            self.devices = []
    
    def get_safe_output_device(self, exclude_device_ids=None):
        """获取安全的输出设备，避免AUHAL冲突"""
        if exclude_device_ids is None:
            exclude_device_ids = []
        
        # 优先级顺序 - 避免AUHAL错误
        priority_keywords = [
            ['airpods', 'bluetooth'],  # 蓝牙设备最安全
            ['headphones', 'headset'],  # 耳机
            ['speaker', '扬声器'],  # 扬声器
            ['display', 'monitor'],  # 显示器音频
            ['usb', 'external'],  # 外接设备
        ]
        
        safe_devices = []
        
        for i, device in enumerate(self.devices):
            # 必须有输出通道
            if device['max_output_channels'] == 0:
                continue
            
            # 排除指定设备（通常是输入设备）
            if i in exclude_device_ids:
                continue
            
            # 排除不健康的设备
            if not self.device_health.get(i, True):
                print(f"[音频] 跳过不健康设备 {i}: {device['name']}")
                continue
            
            # 排除正在使用的设备
            if i in self.active_streams:
                continue
            
            # 排除明显的输入设备
            device_name_lower = device['name'].lower()
            input_keywords = ['mic', 'microphone', '麦克风', 'input']
            if any(keyword in device_name_lower for keyword in input_keywords):
                continue
            
            safe_devices.append((i, device))
        
        if not safe_devices:
            print("[音频] 未找到安全的输出设备")
            return None
        
        # 按优先级选择
        for priority_group in priority_keywords:
            for device_id, device in safe_devices:
                device_name_lower = device['name'].lower()
                for keyword in priority_group:
                    if keyword in device_name_lower:
                        print(f"[音频] 选择安全设备 {device_id}: {device['name']}")
                        return device_id
        
        # 使用第一个安全设备
        device_id, device = safe_devices[0]
        print(f"[音频] 使用第一个安全设备 {device_id}: {device['name']}")
        return device_id
    
    def mark_device_error(self, device_id, error):
        """记录设备错误"""
        with self.stream_lock:
            self.error_counts[device_id] = self.error_counts.get(device_id, 0) + 1
            
            # 如果错误次数过多，标记设备不健康
            if self.error_counts[device_id] >= 3:
                self.device_health[device_id] = False
                print(f"[音频] 设备 {device_id} 标记为不健康（错误{self.error_counts[device_id]}次）")
                
            # 检查AUHAL错误
            if "-10863" in str(error):
                print(f"[音频] ⚠️  检测到AUHAL错误 -10863 在设备 {device_id}")
                self.device_health[device_id] = False
                # 触发恢复程序
                self.recover_from_auhal_error()
                return True
        return False
    
    def recover_from_auhal_error(self):
        """从AUHAL错误恢复"""
        try:
            print("[音频] 执行AUHAL错误恢复程序...")
            import sounddevice as sd
            
            # 1. 停止所有音频
            sd.stop()
            time.sleep(0.5)
            
            # 2. 清理设备状态
            self.active_streams.clear()
            
            # 3. 重新初始化设备信息
            self._update_device_info()
            
            print("[音频] AUHAL错误恢复完成")
        except Exception as e:
            print(f"[音频] 恢复失败: {e}")
    
    @contextmanager
    def reserve_device(self, device_id, stream_type):
        """预留设备使用权，防止冲突"""
        acquired = False
        try:
            with self.stream_lock:
                # 检查播放间隔
                current_time = time.time()
                time_since_last = current_time - self.last_playback_time
                if time_since_last < self.min_playback_interval:
                    wait_time = self.min_playback_interval - time_since_last
                    print(f"[音频] 等待 {wait_time:.3f}s 避免设备冲突")
                    time.sleep(wait_time)
                
                if device_id in self.active_streams:
                    raise RuntimeError(f"设备 {device_id} 已被占用: {self.active_streams[device_id]}")
                
                self.active_streams[device_id] = stream_type
                acquired = True
                self.last_playback_time = time.time()
                print(f"[音频] 预留设备 {device_id} 用于 {stream_type}")
            
            yield device_id
            
        except Exception as e:
            if acquired:
                self.mark_device_error(device_id, e)
            raise
        finally:
            if acquired:
                with self.stream_lock:
                    if device_id in self.active_streams:
                        del self.active_streams[device_id]
                        print(f"[音频] 释放设备 {device_id}")

class RealtimeTranslator:
    """实时同声传译引擎 - 增强可靠性版本"""
    
    def __init__(self, source_lang='zh', target_lang='en', sample_rate=16000, audio_router=None, enable_tts=True, audio_capture=None):
        self.source_lang = source_lang
        self.target_lang = target_lang
        self.sample_rate = sample_rate
        self.audio_router = audio_router
        self.enable_tts = enable_tts
        self.audio_capture = audio_capture  # For device conflict detection
        
        # AUHAL错误修复 - 音频设备管理器
        self.audio_device_manager = AudioDeviceManager()
        
        # Thread synchronization - CRITICAL FIX
        self.state_lock = RLock()  # For VAD state management
        self.buffer_lock = RLock()  # For buffer operations
        self.shutdown_event = Event()  # For graceful shutdown
        
        # VAD配置
        self.vad = webrtcvad.Vad(2)
        self.frame_duration = 30  # ms
        self.frame_size = int(self.sample_rate * self.frame_duration / 1000)
        
        # Whisper模型
        self.whisper_model = whisper.load_model("medium")
        
        # OpenAI客户端 with proxy support - CRITICAL FIX
        api_key = os.getenv('OPENAI_API_KEY')
        if not api_key:
            raise ValueError("请设置环境变量 OPENAI_API_KEY")
        
        # 代理设置支持
        proxy_url = os.getenv('OPENAI_PROXY', 'http://127.0.0.1:7890')  # 默认使用本地代理
        base_url = os.getenv('OPENAI_BASE_URL')  # 支持自定义API基础URL
        
        try:
            import httpx
            # 创建支持代理的HTTP客户端
            # httpx使用mounts参数而不是proxies
            http_client = httpx.Client(
                proxy=proxy_url,  # 使用proxy参数而不是proxies
                timeout=45.0,
                verify=False  # 如果代理有证书问题，可以跳过验证
            )
            
            client_kwargs = {
                'api_key': api_key,
                'http_client': http_client,
                'timeout': 45.0
            }
            
            if base_url:
                client_kwargs['base_url'] = base_url
                
            self.openai_client = OpenAI(**client_kwargs)
            print(f"[系统] OpenAI客户端已配置代理: {proxy_url}")
            
        except ImportError:
            print("[系统] 警告: httpx未安装，使用默认客户端（可能需要代理）")
            client_kwargs = {
                'api_key': api_key,
                'timeout': 45.0
            }
            if base_url:
                client_kwargs['base_url'] = base_url
            self.openai_client = OpenAI(**client_kwargs)
        except Exception as e:
            print(f"[系统] 代理配置警告: {e}")
            print("[系统] 使用默认客户端配置")
            client_kwargs = {
                'api_key': api_key,
                'timeout': 45.0
            }
            if base_url:
                client_kwargs['base_url'] = base_url
            self.openai_client = OpenAI(**client_kwargs)
        
        # Bounded buffers - CRITICAL FIX
        self.audio_buffer = deque(maxlen=int(self.sample_rate * 5))
        self.speech_buffer = BoundedBuffer(
            max_size=int(self.sample_rate * 30 / 1024),  # Max 30 seconds, chunked
            overflow_strategy="drop_oldest"
        )
        
        # Processing queues
        self.recognition_queue = Queue(maxsize=10)
        self.translation_queue = Queue(maxsize=10)
        self.tts_queue = Queue(maxsize=10)
        
        # VAD状态 (protected by locks)
        self.is_speaking = False
        self.silence_frames = 0
        self.min_speech_frames = 8
        self.max_silence_frames = 45
        
        # Circuit breakers - CRITICAL FIX
        self.openai_circuit_breaker = CircuitBreaker(
            CircuitBreakerConfig(failure_threshold=3, recovery_timeout=60)
        )
        self.tts_circuit_breaker = CircuitBreaker(
            CircuitBreakerConfig(failure_threshold=5, recovery_timeout=30)
        )
        
        # Resource monitoring - CRITICAL FIX
        self.resource_monitor = ResourceMonitor(memory_threshold_mb=6144, cpu_threshold=95.0)  # 提高到6GB，为AI模型留足够空间
        self.health_monitor = HealthMonitor(check_interval=30)
        # 配置健康监控器使用相同的资源监控器参数
        self.health_monitor.resource_monitor = self.resource_monitor
        
        # Recovery manager
        self.recovery_manager = SystemRecoveryManager()
        self._register_recovery_strategies()
        
        # Managed worker threads - CRITICAL FIX
        self.workers = []
        
        # Initialize system
        self._start_workers()
        self._start_health_monitoring()
        
        print("[系统] RealtimeTranslator初始化完成，已启用可靠性增强功能")
        
    def _start_workers(self):
        """启动受管理的工作线程（非daemon）"""
        # Create managed worker threads
        recognition_worker = ManagedWorkerThread(
            "ASR-Worker", 
            self._process_recognition_item,
            self.recognition_queue
        )
        translation_worker = ManagedWorkerThread(
            "Translation-Worker",
            self._process_translation_item,
            self.translation_queue
        )
        tts_worker = ManagedWorkerThread(
            "TTS-Worker",
            self._process_tts_item, 
            self.tts_queue
        )
        
        self.workers = [recognition_worker, translation_worker, tts_worker]
        
        # Start all workers
        for worker in self.workers:
            worker.start()
            
        print(f"[系统] 已启动 {len(self.workers)} 个受管理的工作线程")
    
    def process_audio(self, audio_data):
        """处理输入音频流（增强可靠性）"""
        if len(audio_data) == 0 or self.shutdown_event.is_set():
            return
            
        try:
            # Ensure single channel
            if len(audio_data.shape) > 1:
                audio_data = np.mean(audio_data, axis=1)
            
            # Convert to 16-bit PCM for VAD
            audio_16bit = (np.clip(audio_data, -1.0, 1.0) * 32767).astype(np.int16)
            
            # Enhanced VAD with energy distribution
            volume = np.sqrt(np.mean(audio_data**2))
            is_speech = volume > 0.006 and volume < 0.9
            
            # Dynamic range check for real speech vs noise
            if is_speech:
                audio_std = np.std(audio_data)
                if audio_std < 0.0005:
                    is_speech = False
            
            if is_speech:
                with self.buffer_lock:
                    self.speech_buffer.add(audio_16bit)
                with self.state_lock:
                    self.silence_frames = 0
                    
                    if not self.is_speaking:
                        self.is_speaking = True
                        print(f"[VAD] 检测到说话开始 (音量: {volume:.3f})")
                
                # Check buffer utilization and resource usage
                with self.buffer_lock:
                    buffer_stats = self.speech_buffer.get_stats()
                    
                if buffer_stats['utilization'] > 80:  # 80% full
                    print(f"[VAD] 缓冲区接近上限，处理语音片段: {buffer_stats['current_size']} items")
                    self._process_speech_segment(force_reset=False)
                    
                # Memory pressure check - with improved logging
                if self.resource_monitor.is_memory_critical():
                    memory_usage = self.resource_monitor.get_memory_usage_mb()
                    print(f"[VAD] 内存使用达到阈值 ({memory_usage:.1f}MB)，清理缓冲区保持性能")
                    self._emergency_buffer_cleanup()
                    
            else:
                with self.state_lock:
                    self.silence_frames += 1
                    
                    if self.is_speaking and self.silence_frames > self.max_silence_frames:
                        # Speech ended, process remaining audio
                        with self.buffer_lock:
                            buffer_size = self.speech_buffer.size()
                        print(f"[VAD] 检测到说话结束，处理剩余 {buffer_size} items")
                        
                        if buffer_size > 10:  # Minimum threshold for processing
                            self._process_speech_segment(force_reset=True)
                        else:
                            print(f"[VAD] 音频片段太短({buffer_size} items)，跳过处理")
                        # Reset state
                        self._reset_vad_state()
                    
        except Exception as e:
            print(f"[VAD] 处理音频错误: {e}")
            # Safe state reset
            safe_execute(lambda: self._reset_vad_state(), log_errors=True)
    
    def _reset_vad_state(self):
        """重置VAD状态（线程安全）"""
        with self.state_lock:
            self.is_speaking = False
            self.silence_frames = 0
        with self.buffer_lock:
            self.speech_buffer = BoundedBuffer(
                max_size=int(self.sample_rate * 30 / 1024),
                overflow_strategy="drop_oldest"
            )
        print("[VAD] 状态已重置，准备处理下一段语音")
    
    def _process_speech_segment(self, force_reset=False):
        """处理语音片段（线程安全）"""
        with self.buffer_lock:
            if self.speech_buffer.size() > 0:
                audio_items = self.speech_buffer.get_all_and_clear()
                audio_segment = np.concatenate(audio_items) if audio_items else np.array([])
                
                if len(audio_segment) > 0:
                    # Add to recognition queue with timeout
                    if self.recognition_queue.full():
                        try:
                            self.recognition_queue.get_nowait()
                            print("[VAD] 识别队列已满，丢弃最旧数据")
                        except Empty:
                            pass
                    
                    try:
                        self.recognition_queue.put(audio_segment, timeout=1.0)
                        print(f"[VAD] 音频片段已加入识别队列: {len(audio_segment)} samples")
                    except Exception as e:
                        print(f"[VAD] 加入识别队列失败: {e}")
                
                if not force_reset and len(audio_segment) > 0:
                    # Keep context for continuous recognition
                    keep_samples = int(self.sample_rate * 0.5)
                    if len(audio_segment) > keep_samples:
                        context_audio = audio_segment[-keep_samples:]
                        self.speech_buffer.add(context_audio)
                        print("[VAD] 保留上下文音频用于连续识别")

    # Recognition Worker with timeout protection
    def _process_recognition_item(self, audio_segment):
        """处理单个识别任务（由ManagedWorkerThread调用）"""
        try:
            self._recognition_worker_impl(audio_segment)
        except Exception as e:
            print(f"[ASR] 识别任务失败: {e}")
            self.recovery_manager.attempt_recovery("asr_failure", str(e))
        
    @timeout_handler(10)  # 10 second timeout for Whisper - CRITICAL FIX
    @retry_with_exponential_backoff(max_retries=2, base_delay=0.5)
    def _recognition_worker_impl(self, audio_segment):
        """语音识别实现（带超时保护）"""
        print(f"[ASR] 开始识别音频片段: {len(audio_segment)} samples")
        
        # Convert and resample audio
        audio_float = audio_segment.astype(np.float32) / 32767.0
        
        # Audio energy check
        audio_energy = np.sqrt(np.mean(audio_float**2))
        print(f"[ASR] 音频能量: {audio_energy:.6f}")
        
        # Silence detection
        if audio_energy < 0.00005:
            print("[ASR] 检测到极低能量音频，跳过识别")
            return
        
        # Resample to 16kHz for Whisper
        if len(audio_float) > 0:
            try:
                import librosa
                audio_16k = librosa.resample(audio_float, orig_sr=48000, target_sr=16000, res_type='kaiser_fast')
                print(f"[ASR] 重采样完成: {len(audio_16k)} samples @ 16kHz")
            except Exception as e:
                print(f"[ASR] 重采样失败: {e}")
                return
        else:
            print("[ASR] 音频数据为空，跳过")
            return
        
        # Whisper transcription with timeout protection
        start_time = time.time()
        whisper_params = {
            "language": self.source_lang,
            "fp16": False,
            "verbose": False,
            "no_speech_threshold": 0.6,
            "logprob_threshold": -1.0,
            "temperature": 0.0,
            "beam_size": 5,
            "patience": 1.0,
            "condition_on_previous_text": False,
            "without_timestamps": True,
        }
        
        # Language-specific optimizations
        if self.source_lang == 'zh':
            whisper_params.update({
                "temperature": 0.1,
                "compression_ratio_threshold": 2.4,
                "no_speech_threshold": 0.4,
                "logprob_threshold": -1.2,
            })
        
        result = self.whisper_model.transcribe(audio_16k, **whisper_params)
        text = result['text'].strip()
        duration = time.time() - start_time
        
        # Hallucination detection
        no_speech_prob = result.get('no_speech_prob', 0)
        print(f"[ASR] no_speech概率: {no_speech_prob:.3f}")
        
        if no_speech_prob > 0.7:
            print(f"[ASR] 检测到高no_speech概率({no_speech_prob:.3f})，跳过")
            return
        
        # Check for hallucination patterns
        hallucination_patterns = [
            "请不吝点赞", "订阅转发打赏", "明镜与点点", "谢谢大家", "感谢观看",
            "Thank you for watching", "Subscribe and like", "Please support"
        ]
        
        for pattern in hallucination_patterns:
            if pattern in text:
                print(f"[ASR] 检测到幻听模式: {pattern}")
                return
        
        if text and len(text.strip()) > 1:
            print(f"[ASR] 有效识别结果: '{text}' (耗时: {duration:.2f}s)")
            
            # Apply audio filter
            if filter_audio_text(text):
                # Add to translation queue with timeout
                if self.translation_queue.full():
                    try:
                        self.translation_queue.get_nowait()
                        print("[ASR] 翻译队列已满，丢弃最旧数据")
                    except Empty:
                        pass
                
                try:
                    self.translation_queue.put(text, timeout=1.0)
                    print("[ASR] 已添加到翻译队列")
                except Exception as e:
                    print(f"[ASR] 添加到翻译队列失败: {e}")
            else:
                print("[ASR] 音频内容被过滤器拦截，跳过翻译")
        else:
            print(f"[ASR] 无有效识别结果: '{text}' (耗时: {duration:.2f}s)")

    # Translation Worker with circuit breaker
    def _process_translation_item(self, text):
        """处理单个翻译任务（由ManagedWorkerThread调用）"""
        try:
            self._translation_worker_impl(text)
        except Exception as e:
            print(f"[翻译] 翻译任务失败: {e}")
            self.recovery_manager.attempt_recovery("translation_failure", str(e))
        
    @timeout_handler(60)  # 60 second timeout for translation - 增加到1分钟适应复杂翻译
    @retry_with_exponential_backoff(max_retries=2, base_delay=1.0)
    def _translation_worker_impl(self, text):
        """翻译实现（带超时和断路器保护）"""
        print(f"[翻译] 开始翻译: '{text}'")
        
        # Create enhanced system prompt
        system_prompt = f"""你是一个专业的同声传译员。请将以下{self.source_lang}文本翻译成{self.target_lang}。

翻译要求：
1. 保持原意的准确性和完整性
2. 使用自然流畅的表达方式
3. 适合口语交流的语调
4. 如果是专业术语，请使用准确的专业词汇
5. 只返回翻译结果，不要额外说明

文本："""
        
        start_time = time.time()
        
        # Use circuit breaker for OpenAI API calls - CRITICAL FIX with enhanced error handling
        try:
            response = self.openai_circuit_breaker.call(
                self.openai_client.chat.completions.create,
                model="gpt-4o-mini",
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": text}
                ],
                temperature=0.1,
                max_tokens=300,
                top_p=0.9,
            timeout=25.0  # Explicit timeout
            )
            
            translated = response.choices[0].message.content.strip()
            duration = time.time() - start_time
            
            # Translation quality check
            if self._is_valid_translation(text, translated):
                print(f"[翻译] 翻译结果: '{translated}' (耗时: {duration:.2f}s)")
                
                # Add to TTS queue if enabled AND not in microphone mode
                if self.enable_tts and self.audio_capture is None:
                    if self.tts_queue.full():
                        try:
                            self.tts_queue.get_nowait()
                            print("[翻译] TTS队列已满，丢弃最旧数据")
                        except Empty:
                            pass
                    
                    try:
                        self.tts_queue.put(translated, timeout=1.0)
                        print("[翻译] 已添加到TTS队列")
                    except Exception as e:
                        print(f"[翻译] 添加到TTS队列失败: {e}")
                elif self.audio_capture is not None:
                    print("[翻译] 麦克风模式：强制跳过TTS避免PaMacCore -10863错误")
                else:
                    print("[翻译] TTS已禁用，跳过语音合成")
            else:
                print(f"[翻译] 翻译质量不佳，跳过: '{translated}' (耗时: {duration:.2f}s)")
                
        except Exception as e:
            duration = time.time() - start_time
            error_msg = str(e)
            
            # 特别处理网络连接错误
            if "connection" in error_msg.lower() or "timeout" in error_msg.lower():
                print(f"[翻译] 网络连接问题: {error_msg} (耗时: {duration:.2f}s)")
                print("[翻译] 将由重试机制自动处理...")
            elif "rate limit" in error_msg.lower():
                print(f"[翻译] API速率限制: {error_msg} (耗时: {duration:.2f}s)")
                print("[翻译] 等待重试...")
            else:
                print(f"[翻译] API调用错误: {error_msg} (耗时: {duration:.2f}s)")
            
            # 重新抛出异常让重试机制处理
            raise

    # TTS Worker with circuit breaker
    def _process_tts_item(self, text):
        """处理单个TTS任务（由ManagedWorkerThread调用）"""
        
        # CRITICAL PROTECTION: Double-check microphone mode at worker level
        if self.audio_capture is not None:
            print(f"[TTS-Worker] 麦克风模式保护：工作线程拒绝处理TTS任务")
            print(f"[TTS-Worker] 跳过文本: '{text}'")
            return
        
        try:
            self._tts_worker_impl(text)
        except Exception as e:
            print(f"[TTS] TTS任务失败: {e}")
            self.recovery_manager.attempt_recovery("tts_failure", str(e))
        
    @timeout_handler(15)  # 15 second timeout for TTS - CRITICAL FIX
    @retry_with_exponential_backoff(max_retries=2, base_delay=1.0)
    def _tts_worker_impl(self, text):
        """TTS实现（带超时和断路器保护）"""
        
        # CRITICAL PROTECTION: Immediately abort if in microphone mode
        if self.audio_capture is not None:
            print(f"[TTS] 麦克风模式保护：拒绝TTS合成请求，避免PaMacCore -10863错误")
            print(f"[TTS] 跳过的文本: '{text}'")
            return None
        
        print(f"[TTS] 开始合成: '{text}'")
        start_time = time.time()
        
        # Create event loop for this thread if none exists
        try:
            loop = asyncio.get_event_loop()
        except RuntimeError:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
        # Use circuit breaker for TTS - CRITICAL FIX
        audio_data = self.tts_circuit_breaker.call(
            lambda: loop.run_until_complete(self._synthesize_speech(text))
        )
        
        print(f"[TTS] 完成 (耗时: {time.time()-start_time:.2f}s)")

    async def _synthesize_speech(self, text):
        """异步语音合成（带超时保护）"""
        voice = "en-US-JennyNeural" if self.target_lang == 'en' else "zh-CN-XiaoxiaoNeural"
        
        communicate = edge_tts.Communicate(text, voice)
        audio_data = b""
        
        # Add timeout to TTS streaming - CRITICAL FIX
        try:
            timeout_duration = 10  # 10 seconds
            start_time = time.time()
            
            async for chunk in communicate.stream():
                if chunk["type"] == "audio":
                    audio_data += chunk["data"]
                    
                # Check timeout
                if time.time() - start_time > timeout_duration:
                    raise TimeoutError("TTS streaming timed out")
                    
        except Exception as e:
            print(f"[TTS] 流式合成错误: {e}")
            raise
        
        # Play audio to output device
        if audio_data:
            try:
                audio_np = self._convert_audio_for_playback(audio_data)
                if audio_np is not None:
                    
                    # Output to BlackHole for OBS capture
                    if self.audio_router:
                        try:
                            self.audio_router.send_to_output(audio_np)
                            print(f"[音频] 已输出到BlackHole: {text[:30]}...")
                        except Exception as e:
                            print(f"[音频] BlackHole输出错误: {e}")
                    
                    # Output to speakers for monitoring - WITH DEVICE CONFLICT PREVENTION
                    try:
                        import sounddevice as sd
                        
                        # CRITICAL FIX: Check if TTS is disabled
                        if not self.enable_tts:
                            print(f"[音频] TTS已禁用，跳过播放: {text[:30]}...")
                            return audio_data
                        
                        # CRITICAL FIX: For microphone mode, ALWAYS disable audio playback to prevent PaMacCore conflicts
                        if self.audio_capture is not None:
                            print(f"[音频] 麦克风模式强制禁用音频播放，避免PaMacCore Error -10863")
                            print(f"[音频] 翻译完成但跳过播放: {text[:50]}...")
                            print("[音频] 提示: 如需语音播放，请使用外接音箱或蓝牙耳机")
                            # 确保音频数据被清理
                            del audio_data
                            return None
                        
                        # AUHAL修复：使用安全设备管理器避免冲突
                        exclude_devices = []
                        if self.audio_capture is not None and hasattr(self.audio_capture, 'device_id'):
                            exclude_devices.append(self.audio_capture.device_id)
                        
                        playback_device = self.audio_device_manager.get_safe_output_device(exclude_devices)
                        
                        if playback_device is None:
                            print(f"[音频] 未找到安全的输出设备，跳过播放: {text[:30]}...")
                            return audio_data
                        
                        # Use shorter audio chunks to prevent device locking
                        if len(audio_np) > 48000 * 2:  # More than 2 seconds
                            print(f"[音频] 音频过长({len(audio_np)}样本)，截断至2秒")
                            audio_np = audio_np[:48000 * 2]
                        
                        # FINAL SAFETY CHECK: Double-check microphone mode before playing
                        if self.audio_capture is not None:
                            print(f"[音频] 最终安全检查：检测到麦克风模式，强制跳过播放避免Error -10863")
                            del audio_np
                            return None
                        
                        # AUHAL修复：使用设备预留机制安全播放
                        max_retry = 2
                        retry_count = 0
                        
                        while retry_count < max_retry:
                            try:
                                with self.audio_device_manager.reserve_device(playback_device, "TTS播放"):
                                    sd.play(audio_np, 48000, device=playback_device, blocking=False)
                                    
                                    # 等待播放完成，避免设备冲突
                                    duration = len(audio_np) / 48000
                                    time.sleep(min(duration + 0.2, 10.0))  # 最多等待10秒
                                    
                                    sd.stop()  # 确保停止播放
                                    print(f"[音频] AUHAL安全播放完成到设备{playback_device}: {text[:30]}...")
                                    break  # 成功则跳出重试循环
                                
                            except Exception as play_error:
                                retry_count += 1
                                print(f"[音频] 播放失败 (尝试 {retry_count}/{max_retry}): {play_error}")
                                
                                # 检查是否为AUHAL错误
                                if "-10863" in str(play_error):
                                    print(f"[音频] ⚠️  检测到PaMacCore AUHAL Error -10863")
                                    
                                    # 标记设备错误
                                    self.audio_device_manager.mark_device_error(playback_device, play_error)
                                    
                                    if retry_count < max_retry:
                                        # 尝试获取新设备
                                        print("[音频] 尝试使用其他设备...")
                                        time.sleep(1.0)  # 等待恢复
                                        
                                        # 重新获取安全设备
                                        playback_device = self.audio_device_manager.get_safe_output_device(exclude_devices)
                                        if playback_device is None:
                                            print("[音频] 无可用设备，放弃播放")
                                            break
                                    else:
                                        print(f"[音频] 建议：使用蓝牙耳机或重启Core Audio服务")
                                        print(f"[音频] 命令：sudo pkill coreaudiod")
                                else:
                                    print(f"[音频] 其他音频错误: {play_error}")
                                    if retry_count >= max_retry:
                                        break
                        
                    except Exception as e:
                        print(f"[音频] 扬声器播放错误（忽略继续）: {e}")
                        
            except Exception as e:
                print(f"[音频播放] 总体错误: {e}")
        
        return audio_data

    def _convert_audio_for_playback(self, audio_data):
        """将Edge-TTS的音频数据转换为可播放格式"""
        try:
            from pydub import AudioSegment
            
            # Edge-TTS outputs MP3, convert to wav
            audio_segment = AudioSegment.from_mp3(io.BytesIO(audio_data))
            audio_segment = audio_segment.set_frame_rate(48000).set_channels(2)
            
            # Convert to numpy array
            audio_np = np.array(audio_segment.get_array_of_samples(), dtype=np.float32)
            
            if audio_segment.channels == 2:
                audio_np = audio_np.reshape((-1, 2))
            
            # Normalize to [-1, 1] range
            audio_np = audio_np / 32768.0
            
            return audio_np
            
        except Exception as e:
            print(f"[音频转换] 错误: {e}")
            return None

    def _find_best_output_device(self):
        """查找最佳的音频输出设备"""
        try:
            import sounddevice as sd
            devices = sd.query_devices()
            
            # Priority order for output devices
            priority_keywords = [
                ['airpods'],
                ['扬声器', 'speaker', 'speakers'],
                ['display', 'monitor'],
                ['headphones', 'headset'],
            ]
            
            for priority_group in priority_keywords:
                for i, device in enumerate(devices):
                    if device['max_output_channels'] > 0:
                        device_name = device['name'].lower()
                        for keyword in priority_group:
                            if keyword.lower() in device_name:
                                print(f"[音频] 选择输出设备: {device['name']} (ID: {i})")
                                return i
            
            # Use system default
            print("[音频] 使用系统默认输出设备")
            return None
            
        except Exception as e:
            print(f"[音频] 查找输出设备错误: {e}")
            return None


    def _is_valid_translation(self, original, translated):
        """检查翻译质量"""
        if not translated or len(translated.strip()) == 0:
            return False
            
        if translated.strip().lower() == original.strip().lower():
            return False
        
        # Check for system error indicators
        error_indicators = [
            "i cannot translate", "i can't translate", "sorry, i", "unable to translate",
            "error:", "api error"
        ]
        
        translated_lower = translated.lower()
        for indicator in error_indicators:
            if indicator in translated_lower:
                return False
        
        # Length ratio validation
        original_chars = len(original.strip())
        translated_chars = len(translated.strip())
        
        if self.source_lang == 'zh' and self.target_lang == 'en':
            # Chinese to English: 1 Chinese char ≈ 0.5-2 English words
            translated_words = len(translated.split())
            if translated_words < original_chars * 0.3 or translated_words > original_chars * 2.5:
                return False
        elif self.source_lang == 'en' and self.target_lang == 'zh':
            # English to Chinese: 1 English word ≈ 1-3 Chinese chars
            original_words = len(original.split())
            if translated_chars < original_words * 0.5 or translated_chars > original_words * 4:
                return False
        else:
            # Other language pairs: loose ratio
            ratio = translated_chars / max(original_chars, 1)
            if ratio < 0.1 or ratio > 10.0:
                return False
        
        return True

    # Health monitoring and recovery - CRITICAL FIX
    def _start_health_monitoring(self):
        """启动健康监控"""
        self.health_monitor.add_health_callback(self._health_check_callback)
        self.health_monitor.start_monitoring()
        
    def _health_check_callback(self, health_status):
        """健康检查回调"""
        resources = health_status['resources']
        
        # Log queue status
        rec_size = self.recognition_queue.qsize()
        trans_size = self.translation_queue.qsize()
        tts_size = self.tts_queue.qsize()
        
        if rec_size > 5 or trans_size > 5 or tts_size > 5:
            print(f"[监控] 队列状态 - ASR:{rec_size}/10, 翻译:{trans_size}/10, TTS:{tts_size}/10")
            
        # Check resource usage
        if resources['memory_critical'] or resources['cpu_critical']:
            print(f"[监控] 资源使用告警 - 内存:{resources['memory_mb']:.1f}MB, CPU:{resources['cpu_percent']:.1f}%")
            self._handle_resource_pressure()
            
        # Check worker health
        unhealthy_workers = [w for w in self.workers if not w.is_healthy()]
        if unhealthy_workers:
            print(f"[监控] 检测到{len(unhealthy_workers)}个不健康的工作线程")
            self._restart_unhealthy_workers(unhealthy_workers)
            
    def _handle_resource_pressure(self):
        """处理资源压力"""
        print("[系统] 检测到资源压力，执行清理...")
        self._emergency_queue_cleanup()
        import gc
        gc.collect()
        
    def _emergency_queue_cleanup(self):
        """紧急队列清理"""
        for queue_name, queue in [("recognition", self.recognition_queue), 
                                  ("translation", self.translation_queue),
                                  ("tts", self.tts_queue)]:
            if queue.qsize() > 8:
                cleared = 0
                while queue.qsize() > 2:  # Keep only 2 most recent
                    try:
                        queue.get_nowait()
                        cleared += 1
                    except Empty:
                        break
                if cleared > 0:
                    print(f"[紧急清理] 清理了{queue_name}队列中的{cleared}个任务")
                    
    def _emergency_buffer_cleanup(self):
        """紧急缓冲区清理"""
        with self.buffer_lock:
            buffer_stats = self.speech_buffer.get_stats()
            if buffer_stats['utilization'] > 90:
                items = self.speech_buffer.get_all_and_clear()
                if items:
                    # Keep only the most recent half
                    recent_items = items[len(items)//2:]
                    for item in recent_items:
                        self.speech_buffer.add(item)
                    print(f"[紧急清理] 清理了语音缓冲区，保留{len(recent_items)}个项目")
                    
    def _restart_unhealthy_workers(self, unhealthy_workers):
        """重启不健康的工作线程"""
        for worker in unhealthy_workers:
            print(f"[系统] 重启不健康的工作线程: {worker.name}")
            try:
                worker.stop(timeout=5)
                worker.start()
                print(f"[系统] 工作线程 {worker.name} 重启成功")
            except Exception as e:
                print(f"[系统] 重启工作线程 {worker.name} 失败: {e}")

    def _register_recovery_strategies(self):
        """注册恢复策略"""
        def reset_asr_failure():
            print("[恢复] 执行ASR故障恢复")
            self._emergency_queue_cleanup()
            return True
            
        def reset_translation_failure():
            print("[恢复] 执行翻译故障恢复")
            self.openai_circuit_breaker = CircuitBreaker(
                CircuitBreakerConfig(failure_threshold=3, recovery_timeout=60)
            )
            return True
            
        def reset_tts_failure():
            print("[恢复] 执行TTS故障恢复")
            self.tts_circuit_breaker = CircuitBreaker(
                CircuitBreakerConfig(failure_threshold=5, recovery_timeout=30)
            )
            return True
            
        self.recovery_manager.register_recovery_strategy("asr_failure", reset_asr_failure)
        self.recovery_manager.register_recovery_strategy("translation_failure", reset_translation_failure)
        self.recovery_manager.register_recovery_strategy("tts_failure", reset_tts_failure)

    # System management
    def reset_system(self):
        """手动重置整个系统（线程安全）"""
        print("[系统] 手动重置开始...")
        
        try:
            # Clear all queues
            self._emergency_queue_cleanup()
            
            # Reset VAD state
            self._reset_vad_state()
            
            # Reset circuit breakers
            self.openai_circuit_breaker = CircuitBreaker(
                CircuitBreakerConfig(failure_threshold=3, recovery_timeout=60)
            )
            self.tts_circuit_breaker = CircuitBreaker(
                CircuitBreakerConfig(failure_threshold=5, recovery_timeout=30)
            )
            
            # Force garbage collection
            import gc
            gc.collect()
            
            print("[系统] 手动重置完成，系统已准备就绪")
            
        except Exception as e:
            print(f"[系统] 重置过程中发生错误: {e}")
            
    def shutdown(self):
        """优雅关闭系统 - CRITICAL FIX"""
        print("[系统] 开始优雅关闭...")
        
        # Signal shutdown to all components
        self.shutdown_event.set()
        
        # Stop health monitoring
        self.health_monitor.stop_monitoring()
        
        # Stop all worker threads
        for worker in self.workers:
            print(f"[系统] 停止工作线程: {worker.name}")
            worker.stop(timeout=10)
            
        print("[系统] 系统已优雅关闭")

# Keep compatibility with existing streaming VAD class
class StreamingVAD:
    """流式VAD处理器"""
    
    def __init__(self, sample_rate=16000, frame_duration_ms=30):
        self.sample_rate = sample_rate
        self.frame_duration_ms = frame_duration_ms
        self.frame_size = int(sample_rate * frame_duration_ms / 1000)
        self.vad = webrtcvad.Vad(2)
        
        self.ring_buffer = deque(maxlen=30)  # 900ms ring buffer
        self.triggered = False
        self.num_voiced_frames = 0
        self.num_unvoiced_frames = 0
        
    def process_frame(self, audio_frame):
        """处理单帧音频"""
        is_speech = self.vad.is_speech(audio_frame, self.sample_rate)
        
        self.ring_buffer.append((audio_frame, is_speech))
        
        if not self.triggered:
            # Wait for speech start
            self.num_voiced_frames = sum(1 for _, speech in self.ring_buffer if speech)
            
            if self.num_voiced_frames > 0.8 * len(self.ring_buffer):
                self.triggered = True
                return 'speech_start', self._get_buffered_audio()
        else:
            # Wait for speech end
            self.num_unvoiced_frames = sum(1 for _, speech in self.ring_buffer if not speech)
            
            if self.num_unvoiced_frames > 0.8 * len(self.ring_buffer):
                self.triggered = False
                self.ring_buffer.clear()
                return 'speech_end', None
        
        return 'speech_continue' if self.triggered else 'silence', None
    
    def _get_buffered_audio(self):
        """获取缓冲的音频"""
        return b''.join([frame for frame, _ in self.ring_buffer])