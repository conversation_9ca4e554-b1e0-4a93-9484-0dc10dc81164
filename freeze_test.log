2025-08-27 14:20:07,879 [INFO] root: ======= 开始AI翻译系统冻结回归测试 =======
2025-08-27 14:20:07,879 [INFO] root: === 设置测试环境 ===
2025-08-27 14:20:07,879 [INFO] root: 系统监控已启动
2025-08-27 14:20:07,879 [INFO] root: 线程监控已启动
2025-08-27 14:20:07,879 [INFO] root: 冻结检测器启动，超时阈值: 45秒
2025-08-27 14:20:07,879 [INFO] root: === 测试1: 组件隔离测试 ===
2025-08-27 14:20:07,879 [INFO] root: 测试VAD组件...
2025-08-27 14:20:10,422 [WARNING] root: 高CPU使用率: 98.8%
2025-08-27 14:20:10,932 [WARNING] root: 高CPU使用率: 100.6%
2025-08-27 14:20:11,441 [WARNING] root: 高CPU使用率: 95.0%
2025-08-27 14:20:12,462 [WARNING] root: 高CPU使用率: 99.3%
2025-08-27 14:20:12,972 [WARNING] root: 高CPU使用率: 168.7%
2025-08-27 14:20:13,477 [WARNING] root: 高CPU使用率: 355.4%
2025-08-27 14:20:13,615 [INFO] root: 测试语音信号处理...
2025-08-27 14:20:13,616 [INFO] root: 测试静音信号处理...
2025-08-27 14:20:15,621 [INFO] root: 测试ASR组件...
2025-08-27 14:20:15,950 [INFO] root: 测试Whisper ASR...
2025-08-27 14:20:16,212 [INFO] root: ASR测试结果: 
2025-08-27 14:20:16,213 [INFO] root: 测试队列系统...
2025-08-27 14:20:16,213 [INFO] root: 队列监控已启动
2025-08-27 14:20:16,213 [DEBUG] root: 消费者 ASR 处理: item_0
2025-08-27 14:20:16,518 [DEBUG] root: 消费者 ASR 处理: item_1
2025-08-27 14:20:16,821 [DEBUG] root: 消费者 ASR 处理: item_2
2025-08-27 14:20:17,131 [DEBUG] root: 消费者 ASR 处理: item_3
2025-08-27 14:20:17,216 [WARNING] root: 队列 recognition 已满: 5/5
2025-08-27 14:20:17,441 [DEBUG] root: 消费者 ASR 处理: item_4
2025-08-27 14:20:17,744 [DEBUG] root: 消费者 ASR 处理: item_5
2025-08-27 14:20:18,054 [DEBUG] root: 消费者 ASR 处理: item_6
2025-08-27 14:20:18,361 [DEBUG] root: 消费者 ASR 处理: item_7
2025-08-27 14:20:18,672 [DEBUG] root: 消费者 ASR 处理: item_8
2025-08-27 14:20:18,982 [DEBUG] root: 消费者 ASR 处理: item_9
2025-08-27 14:20:19,286 [INFO] root: === 测试2: 连续输入测试 ===
2025-08-27 14:20:21,085 [WARNING] root: 高CPU使用率: 91.6%
2025-08-27 14:20:22,098 [WARNING] root: 高CPU使用率: 95.8%
2025-08-27 14:20:22,608 [WARNING] root: 高CPU使用率: 99.0%
2025-08-27 14:20:23,117 [WARNING] root: 高CPU使用率: 97.4%
2025-08-27 14:20:23,627 [WARNING] root: 高CPU使用率: 138.2%
2025-08-27 14:20:24,137 [WARNING] root: 高CPU使用率: 301.5%
2025-08-27 14:20:24,578 [INFO] root: 队列监控已启动
2025-08-27 14:20:24,578 [INFO] root: 开始模拟连续语音输入...
2025-08-27 14:20:24,578 [INFO] root: --- 第 1 轮输入 ---
2025-08-27 14:20:24,584 [INFO] root: 生成短语音数据: 2秒
2025-08-27 14:20:24,585 [INFO] root: 开始输入 94 个音频块...
2025-08-27 14:20:24,645 [WARNING] root: 高CPU使用率: 233.2%
2025-08-27 14:20:25,847 [INFO] root: 添加句子间停顿...
2025-08-27 14:20:26,515 [INFO] root: 等待当前轮次处理完成...
2025-08-27 14:20:26,999 [DEBUG] numba.core.byteflow: bytecode dump:
>          0	NOP(arg=None, lineno=1137)
           2	RESUME(arg=0, lineno=1137)
           4	LOAD_FAST(arg=0, lineno=1140)
           6	LOAD_CONST(arg=1, lineno=1140)
           8	BINARY_SUBSCR(arg=None, lineno=1140)
          12	STORE_FAST(arg=3, lineno=1140)
          14	LOAD_FAST(arg=1, lineno=1141)
          16	UNARY_NEGATIVE(arg=None, lineno=1141)
          18	LOAD_FAST(arg=3, lineno=1141)
          20	SWAP(arg=2, lineno=1141)
          22	COPY(arg=2, lineno=1141)
          24	COMPARE_OP(arg=58, lineno=1141)
          28	POP_JUMP_IF_FALSE(arg=6, lineno=1141)
          32	LOAD_FAST(arg=1, lineno=1141)
          34	COMPARE_OP(arg=58, lineno=1141)
          38	POP_JUMP_IF_FALSE(arg=5, lineno=1141)
          42	JUMP_FORWARD(arg=2, lineno=1141)
>         44	POP_TOP(arg=None, lineno=1141)
          46	JUMP_FORWARD(arg=2, lineno=1141)
>         48	LOAD_CONST(arg=1, lineno=1142)
          50	STORE_FAST(arg=3, lineno=1142)
>         52	LOAD_FAST(arg=0, lineno=1144)
          54	LOAD_CONST(arg=2, lineno=1144)
          56	BINARY_SUBSCR(arg=None, lineno=1144)
          60	STORE_FAST(arg=4, lineno=1144)
          62	LOAD_FAST(arg=1, lineno=1145)
          64	UNARY_NEGATIVE(arg=None, lineno=1145)
          66	LOAD_FAST(arg=4, lineno=1145)
          68	SWAP(arg=2, lineno=1145)
          70	COPY(arg=2, lineno=1145)
          72	COMPARE_OP(arg=58, lineno=1145)
          76	POP_JUMP_IF_FALSE(arg=6, lineno=1145)
          80	LOAD_FAST(arg=1, lineno=1145)
          82	COMPARE_OP(arg=58, lineno=1145)
          86	POP_JUMP_IF_FALSE(arg=5, lineno=1145)
          90	JUMP_FORWARD(arg=2, lineno=1145)
>         92	POP_TOP(arg=None, lineno=1145)
          94	JUMP_FORWARD(arg=2, lineno=1145)
>         96	LOAD_CONST(arg=1, lineno=1146)
          98	STORE_FAST(arg=4, lineno=1146)
>        100	LOAD_FAST(arg=2, lineno=1148)
         102	TO_BOOL(arg=None, lineno=1148)
         110	POP_JUMP_IF_FALSE(arg=45, lineno=1148)
         114	LOAD_GLOBAL(arg=0, lineno=1149)
         124	LOAD_ATTR(arg=2, lineno=1149)
         144	PUSH_NULL(arg=None, lineno=1149)
         146	LOAD_FAST(arg=3, lineno=1149)
         148	CALL(arg=1, lineno=1149)
         156	LOAD_GLOBAL(arg=0, lineno=1149)
         166	LOAD_ATTR(arg=2, lineno=1149)
         186	PUSH_NULL(arg=None, lineno=1149)
         188	LOAD_FAST(arg=4, lineno=1149)
         190	CALL(arg=1, lineno=1149)
         198	COMPARE_OP(arg=103, lineno=1149)
         202	RETURN_VALUE(arg=None, lineno=1149)
>        204	LOAD_GLOBAL(arg=0, lineno=1151)
         214	LOAD_ATTR(arg=4, lineno=1151)
         234	PUSH_NULL(arg=None, lineno=1151)
         236	LOAD_FAST(arg=3, lineno=1151)
         238	CALL(arg=1, lineno=1151)
         246	LOAD_GLOBAL(arg=0, lineno=1151)
         256	LOAD_ATTR(arg=4, lineno=1151)
         276	PUSH_NULL(arg=None, lineno=1151)
         278	LOAD_FAST(arg=4, lineno=1151)
         280	CALL(arg=1, lineno=1151)
         288	COMPARE_OP(arg=103, lineno=1151)
         292	RETURN_VALUE(arg=None, lineno=1151)
2025-08-27 14:20:27,000 [DEBUG] numba.core.byteflow: pending: deque([State(pc_initial=0 nstack_initial=0)])
2025-08-27 14:20:27,000 [DEBUG] numba.core.byteflow: stack: []
2025-08-27 14:20:27,000 [DEBUG] numba.core.byteflow: state.pc_initial: State(pc_initial=0 nstack_initial=0)
2025-08-27 14:20:27,000 [DEBUG] numba.core.byteflow: dispatch pc=0, inst=NOP(arg=None, lineno=1137)
2025-08-27 14:20:27,000 [DEBUG] numba.core.byteflow: stack []
2025-08-27 14:20:27,000 [DEBUG] numba.core.byteflow: dispatch pc=2, inst=RESUME(arg=0, lineno=1137)
2025-08-27 14:20:27,000 [DEBUG] numba.core.byteflow: stack []
2025-08-27 14:20:27,000 [DEBUG] numba.core.byteflow: dispatch pc=4, inst=LOAD_FAST(arg=0, lineno=1140)
2025-08-27 14:20:27,000 [DEBUG] numba.core.byteflow: stack []
2025-08-27 14:20:27,000 [DEBUG] numba.core.byteflow: dispatch pc=6, inst=LOAD_CONST(arg=1, lineno=1140)
2025-08-27 14:20:27,000 [DEBUG] numba.core.byteflow: stack ['$x4.0']
2025-08-27 14:20:27,000 [DEBUG] numba.core.byteflow: dispatch pc=8, inst=BINARY_SUBSCR(arg=None, lineno=1140)
2025-08-27 14:20:27,000 [DEBUG] numba.core.byteflow: stack ['$x4.0', '$const6.1.1']
2025-08-27 14:20:27,000 [DEBUG] numba.core.byteflow: dispatch pc=12, inst=STORE_FAST(arg=3, lineno=1140)
2025-08-27 14:20:27,000 [DEBUG] numba.core.byteflow: stack ['$8binary_subscr.2']
2025-08-27 14:20:27,000 [DEBUG] numba.core.byteflow: dispatch pc=14, inst=LOAD_FAST(arg=1, lineno=1141)
2025-08-27 14:20:27,000 [DEBUG] numba.core.byteflow: stack []
2025-08-27 14:20:27,000 [DEBUG] numba.core.byteflow: dispatch pc=16, inst=UNARY_NEGATIVE(arg=None, lineno=1141)
2025-08-27 14:20:27,000 [DEBUG] numba.core.byteflow: stack ['$threshold14.3']
2025-08-27 14:20:27,000 [DEBUG] numba.core.byteflow: dispatch pc=18, inst=LOAD_FAST(arg=3, lineno=1141)
2025-08-27 14:20:27,000 [DEBUG] numba.core.byteflow: stack ['$16unary_negative.4']
2025-08-27 14:20:27,000 [DEBUG] numba.core.byteflow: dispatch pc=20, inst=SWAP(arg=2, lineno=1141)
2025-08-27 14:20:27,000 [DEBUG] numba.core.byteflow: stack ['$16unary_negative.4', '$x018.5']
2025-08-27 14:20:27,000 [DEBUG] numba.core.byteflow: dispatch pc=22, inst=COPY(arg=2, lineno=1141)
2025-08-27 14:20:27,000 [DEBUG] numba.core.byteflow: stack ['$x018.5', '$16unary_negative.4']
2025-08-27 14:20:27,000 [DEBUG] numba.core.byteflow: dispatch pc=24, inst=COMPARE_OP(arg=58, lineno=1141)
2025-08-27 14:20:27,000 [DEBUG] numba.core.byteflow: stack ['$x018.5', '$16unary_negative.4', '$x018.5']
2025-08-27 14:20:27,000 [DEBUG] numba.core.byteflow: dispatch pc=28, inst=POP_JUMP_IF_FALSE(arg=6, lineno=1141)
2025-08-27 14:20:27,000 [DEBUG] numba.core.byteflow: stack ['$x018.5', '$24compare_op.6']
2025-08-27 14:20:27,000 [DEBUG] numba.core.byteflow: end state. edges=[Edge(pc=32, stack=('$x018.5',), blockstack=(), npush=0), Edge(pc=44, stack=('$x018.5',), blockstack=(), npush=0)]
2025-08-27 14:20:27,000 [DEBUG] numba.core.byteflow: pending: deque([State(pc_initial=32 nstack_initial=1), State(pc_initial=44 nstack_initial=1)])
2025-08-27 14:20:27,000 [DEBUG] numba.core.byteflow: stack: ['$phi32.0']
2025-08-27 14:20:27,000 [DEBUG] numba.core.byteflow: state.pc_initial: State(pc_initial=32 nstack_initial=1)
2025-08-27 14:20:27,000 [DEBUG] numba.core.byteflow: dispatch pc=32, inst=LOAD_FAST(arg=1, lineno=1141)
2025-08-27 14:20:27,000 [DEBUG] numba.core.byteflow: stack ['$phi32.0']
2025-08-27 14:20:27,001 [DEBUG] numba.core.byteflow: dispatch pc=34, inst=COMPARE_OP(arg=58, lineno=1141)
2025-08-27 14:20:27,001 [DEBUG] numba.core.byteflow: stack ['$phi32.0', '$threshold32.1']
2025-08-27 14:20:27,001 [DEBUG] numba.core.byteflow: dispatch pc=38, inst=POP_JUMP_IF_FALSE(arg=5, lineno=1141)
2025-08-27 14:20:27,001 [DEBUG] numba.core.byteflow: stack ['$34compare_op.2']
2025-08-27 14:20:27,001 [DEBUG] numba.core.byteflow: end state. edges=[Edge(pc=42, stack=(), blockstack=(), npush=0), Edge(pc=52, stack=(), blockstack=(), npush=0)]
2025-08-27 14:20:27,001 [DEBUG] numba.core.byteflow: pending: deque([State(pc_initial=44 nstack_initial=1), State(pc_initial=42 nstack_initial=0), State(pc_initial=52 nstack_initial=0)])
2025-08-27 14:20:27,001 [DEBUG] numba.core.byteflow: stack: ['$phi44.0']
2025-08-27 14:20:27,001 [DEBUG] numba.core.byteflow: state.pc_initial: State(pc_initial=44 nstack_initial=1)
2025-08-27 14:20:27,001 [DEBUG] numba.core.byteflow: dispatch pc=44, inst=POP_TOP(arg=None, lineno=1141)
2025-08-27 14:20:27,001 [DEBUG] numba.core.byteflow: stack ['$phi44.0']
2025-08-27 14:20:27,001 [DEBUG] numba.core.byteflow: dispatch pc=46, inst=JUMP_FORWARD(arg=2, lineno=1141)
2025-08-27 14:20:27,001 [DEBUG] numba.core.byteflow: stack []
2025-08-27 14:20:27,001 [DEBUG] numba.core.byteflow: end state. edges=[Edge(pc=52, stack=(), blockstack=(), npush=0)]
2025-08-27 14:20:27,001 [DEBUG] numba.core.byteflow: pending: deque([State(pc_initial=42 nstack_initial=0), State(pc_initial=52 nstack_initial=0), State(pc_initial=52 nstack_initial=0)])
2025-08-27 14:20:27,001 [DEBUG] numba.core.byteflow: stack: []
2025-08-27 14:20:27,001 [DEBUG] numba.core.byteflow: state.pc_initial: State(pc_initial=42 nstack_initial=0)
2025-08-27 14:20:27,001 [DEBUG] numba.core.byteflow: dispatch pc=42, inst=JUMP_FORWARD(arg=2, lineno=1141)
2025-08-27 14:20:27,001 [DEBUG] numba.core.byteflow: stack []
2025-08-27 14:20:27,001 [DEBUG] numba.core.byteflow: end state. edges=[Edge(pc=48, stack=(), blockstack=(), npush=0)]
2025-08-27 14:20:27,001 [DEBUG] numba.core.byteflow: pending: deque([State(pc_initial=52 nstack_initial=0), State(pc_initial=52 nstack_initial=0), State(pc_initial=48 nstack_initial=0)])
2025-08-27 14:20:27,001 [DEBUG] numba.core.byteflow: stack: []
2025-08-27 14:20:27,001 [DEBUG] numba.core.byteflow: state.pc_initial: State(pc_initial=52 nstack_initial=0)
2025-08-27 14:20:27,001 [DEBUG] numba.core.byteflow: dispatch pc=52, inst=LOAD_FAST(arg=0, lineno=1144)
2025-08-27 14:20:27,001 [DEBUG] numba.core.byteflow: stack []
2025-08-27 14:20:27,001 [DEBUG] numba.core.byteflow: dispatch pc=54, inst=LOAD_CONST(arg=2, lineno=1144)
2025-08-27 14:20:27,001 [DEBUG] numba.core.byteflow: stack ['$x52.0']
2025-08-27 14:20:27,001 [DEBUG] numba.core.byteflow: dispatch pc=56, inst=BINARY_SUBSCR(arg=None, lineno=1144)
2025-08-27 14:20:27,001 [DEBUG] numba.core.byteflow: stack ['$x52.0', '$const54.1.2']
2025-08-27 14:20:27,001 [DEBUG] numba.core.byteflow: dispatch pc=60, inst=STORE_FAST(arg=4, lineno=1144)
2025-08-27 14:20:27,001 [DEBUG] numba.core.byteflow: stack ['$56binary_subscr.2']
2025-08-27 14:20:27,001 [DEBUG] numba.core.byteflow: dispatch pc=62, inst=LOAD_FAST(arg=1, lineno=1145)
2025-08-27 14:20:27,001 [DEBUG] numba.core.byteflow: stack []
2025-08-27 14:20:27,001 [DEBUG] numba.core.byteflow: dispatch pc=64, inst=UNARY_NEGATIVE(arg=None, lineno=1145)
2025-08-27 14:20:27,001 [DEBUG] numba.core.byteflow: stack ['$threshold62.3']
2025-08-27 14:20:27,001 [DEBUG] numba.core.byteflow: dispatch pc=66, inst=LOAD_FAST(arg=4, lineno=1145)
2025-08-27 14:20:27,001 [DEBUG] numba.core.byteflow: stack ['$64unary_negative.4']
2025-08-27 14:20:27,001 [DEBUG] numba.core.byteflow: dispatch pc=68, inst=SWAP(arg=2, lineno=1145)
2025-08-27 14:20:27,001 [DEBUG] numba.core.byteflow: stack ['$64unary_negative.4', '$x166.5']
2025-08-27 14:20:27,001 [DEBUG] numba.core.byteflow: dispatch pc=70, inst=COPY(arg=2, lineno=1145)
2025-08-27 14:20:27,001 [DEBUG] numba.core.byteflow: stack ['$x166.5', '$64unary_negative.4']
2025-08-27 14:20:27,001 [DEBUG] numba.core.byteflow: dispatch pc=72, inst=COMPARE_OP(arg=58, lineno=1145)
2025-08-27 14:20:27,001 [DEBUG] numba.core.byteflow: stack ['$x166.5', '$64unary_negative.4', '$x166.5']
2025-08-27 14:20:27,001 [DEBUG] numba.core.byteflow: dispatch pc=76, inst=POP_JUMP_IF_FALSE(arg=6, lineno=1145)
2025-08-27 14:20:27,001 [DEBUG] numba.core.byteflow: stack ['$x166.5', '$72compare_op.6']
2025-08-27 14:20:27,001 [DEBUG] numba.core.byteflow: end state. edges=[Edge(pc=80, stack=('$x166.5',), blockstack=(), npush=0), Edge(pc=92, stack=('$x166.5',), blockstack=(), npush=0)]
2025-08-27 14:20:27,001 [DEBUG] numba.core.byteflow: pending: deque([State(pc_initial=52 nstack_initial=0), State(pc_initial=48 nstack_initial=0), State(pc_initial=80 nstack_initial=1), State(pc_initial=92 nstack_initial=1)])
2025-08-27 14:20:27,001 [DEBUG] numba.core.byteflow: pending: deque([State(pc_initial=48 nstack_initial=0), State(pc_initial=80 nstack_initial=1), State(pc_initial=92 nstack_initial=1)])
2025-08-27 14:20:27,001 [DEBUG] numba.core.byteflow: stack: []
2025-08-27 14:20:27,001 [DEBUG] numba.core.byteflow: state.pc_initial: State(pc_initial=48 nstack_initial=0)
2025-08-27 14:20:27,001 [DEBUG] numba.core.byteflow: dispatch pc=48, inst=LOAD_CONST(arg=1, lineno=1142)
2025-08-27 14:20:27,001 [DEBUG] numba.core.byteflow: stack []
2025-08-27 14:20:27,001 [DEBUG] numba.core.byteflow: dispatch pc=50, inst=STORE_FAST(arg=3, lineno=1142)
2025-08-27 14:20:27,001 [DEBUG] numba.core.byteflow: stack ['$const48.0.1']
2025-08-27 14:20:27,001 [DEBUG] numba.core.byteflow: end state. edges=[Edge(pc=52, stack=(), blockstack=(), npush=0)]
2025-08-27 14:20:27,001 [DEBUG] numba.core.byteflow: pending: deque([State(pc_initial=80 nstack_initial=1), State(pc_initial=92 nstack_initial=1), State(pc_initial=52 nstack_initial=0)])
2025-08-27 14:20:27,001 [DEBUG] numba.core.byteflow: stack: ['$phi80.0']
2025-08-27 14:20:27,001 [DEBUG] numba.core.byteflow: state.pc_initial: State(pc_initial=80 nstack_initial=1)
2025-08-27 14:20:27,001 [DEBUG] numba.core.byteflow: dispatch pc=80, inst=LOAD_FAST(arg=1, lineno=1145)
2025-08-27 14:20:27,001 [DEBUG] numba.core.byteflow: stack ['$phi80.0']
2025-08-27 14:20:27,001 [DEBUG] numba.core.byteflow: dispatch pc=82, inst=COMPARE_OP(arg=58, lineno=1145)
2025-08-27 14:20:27,001 [DEBUG] numba.core.byteflow: stack ['$phi80.0', '$threshold80.1']
2025-08-27 14:20:27,001 [DEBUG] numba.core.byteflow: dispatch pc=86, inst=POP_JUMP_IF_FALSE(arg=5, lineno=1145)
2025-08-27 14:20:27,001 [DEBUG] numba.core.byteflow: stack ['$82compare_op.2']
2025-08-27 14:20:27,001 [DEBUG] numba.core.byteflow: end state. edges=[Edge(pc=90, stack=(), blockstack=(), npush=0), Edge(pc=100, stack=(), blockstack=(), npush=0)]
2025-08-27 14:20:27,001 [DEBUG] numba.core.byteflow: pending: deque([State(pc_initial=92 nstack_initial=1), State(pc_initial=52 nstack_initial=0), State(pc_initial=90 nstack_initial=0), State(pc_initial=100 nstack_initial=0)])
2025-08-27 14:20:27,001 [DEBUG] numba.core.byteflow: stack: ['$phi92.0']
2025-08-27 14:20:27,002 [DEBUG] numba.core.byteflow: state.pc_initial: State(pc_initial=92 nstack_initial=1)
2025-08-27 14:20:27,002 [DEBUG] numba.core.byteflow: dispatch pc=92, inst=POP_TOP(arg=None, lineno=1145)
2025-08-27 14:20:27,002 [DEBUG] numba.core.byteflow: stack ['$phi92.0']
2025-08-27 14:20:27,002 [DEBUG] numba.core.byteflow: dispatch pc=94, inst=JUMP_FORWARD(arg=2, lineno=1145)
2025-08-27 14:20:27,002 [DEBUG] numba.core.byteflow: stack []
2025-08-27 14:20:27,002 [DEBUG] numba.core.byteflow: end state. edges=[Edge(pc=100, stack=(), blockstack=(), npush=0)]
2025-08-27 14:20:27,002 [DEBUG] numba.core.byteflow: pending: deque([State(pc_initial=52 nstack_initial=0), State(pc_initial=90 nstack_initial=0), State(pc_initial=100 nstack_initial=0), State(pc_initial=100 nstack_initial=0)])
2025-08-27 14:20:27,002 [DEBUG] numba.core.byteflow: pending: deque([State(pc_initial=90 nstack_initial=0), State(pc_initial=100 nstack_initial=0), State(pc_initial=100 nstack_initial=0)])
2025-08-27 14:20:27,002 [DEBUG] numba.core.byteflow: stack: []
2025-08-27 14:20:27,002 [DEBUG] numba.core.byteflow: state.pc_initial: State(pc_initial=90 nstack_initial=0)
2025-08-27 14:20:27,002 [DEBUG] numba.core.byteflow: dispatch pc=90, inst=JUMP_FORWARD(arg=2, lineno=1145)
2025-08-27 14:20:27,002 [DEBUG] numba.core.byteflow: stack []
2025-08-27 14:20:27,002 [DEBUG] numba.core.byteflow: end state. edges=[Edge(pc=96, stack=(), blockstack=(), npush=0)]
2025-08-27 14:20:27,002 [DEBUG] numba.core.byteflow: pending: deque([State(pc_initial=100 nstack_initial=0), State(pc_initial=100 nstack_initial=0), State(pc_initial=96 nstack_initial=0)])
2025-08-27 14:20:27,002 [DEBUG] numba.core.byteflow: stack: []
2025-08-27 14:20:27,002 [DEBUG] numba.core.byteflow: state.pc_initial: State(pc_initial=100 nstack_initial=0)
2025-08-27 14:20:27,002 [DEBUG] numba.core.byteflow: dispatch pc=100, inst=LOAD_FAST(arg=2, lineno=1148)
2025-08-27 14:20:27,002 [DEBUG] numba.core.byteflow: stack []
2025-08-27 14:20:27,002 [DEBUG] numba.core.byteflow: dispatch pc=102, inst=TO_BOOL(arg=None, lineno=1148)
2025-08-27 14:20:27,002 [DEBUG] numba.core.byteflow: stack ['$zero_pos100.0']
2025-08-27 14:20:27,002 [DEBUG] numba.core.byteflow: dispatch pc=110, inst=POP_JUMP_IF_FALSE(arg=45, lineno=1148)
2025-08-27 14:20:27,002 [DEBUG] numba.core.byteflow: stack ['$102to_bool.1']
2025-08-27 14:20:27,002 [DEBUG] numba.core.byteflow: end state. edges=[Edge(pc=114, stack=(), blockstack=(), npush=0), Edge(pc=204, stack=(), blockstack=(), npush=0)]
2025-08-27 14:20:27,002 [DEBUG] numba.core.byteflow: pending: deque([State(pc_initial=100 nstack_initial=0), State(pc_initial=96 nstack_initial=0), State(pc_initial=114 nstack_initial=0), State(pc_initial=204 nstack_initial=0)])
2025-08-27 14:20:27,002 [DEBUG] numba.core.byteflow: pending: deque([State(pc_initial=96 nstack_initial=0), State(pc_initial=114 nstack_initial=0), State(pc_initial=204 nstack_initial=0)])
2025-08-27 14:20:27,002 [DEBUG] numba.core.byteflow: stack: []
2025-08-27 14:20:27,002 [DEBUG] numba.core.byteflow: state.pc_initial: State(pc_initial=96 nstack_initial=0)
2025-08-27 14:20:27,002 [DEBUG] numba.core.byteflow: dispatch pc=96, inst=LOAD_CONST(arg=1, lineno=1146)
2025-08-27 14:20:27,002 [DEBUG] numba.core.byteflow: stack []
2025-08-27 14:20:27,002 [DEBUG] numba.core.byteflow: dispatch pc=98, inst=STORE_FAST(arg=4, lineno=1146)
2025-08-27 14:20:27,002 [DEBUG] numba.core.byteflow: stack ['$const96.0.1']
2025-08-27 14:20:27,002 [DEBUG] numba.core.byteflow: end state. edges=[Edge(pc=100, stack=(), blockstack=(), npush=0)]
2025-08-27 14:20:27,002 [DEBUG] numba.core.byteflow: pending: deque([State(pc_initial=114 nstack_initial=0), State(pc_initial=204 nstack_initial=0), State(pc_initial=100 nstack_initial=0)])
2025-08-27 14:20:27,002 [DEBUG] numba.core.byteflow: stack: []
2025-08-27 14:20:27,002 [DEBUG] numba.core.byteflow: state.pc_initial: State(pc_initial=114 nstack_initial=0)
2025-08-27 14:20:27,002 [DEBUG] numba.core.byteflow: dispatch pc=114, inst=LOAD_GLOBAL(arg=0, lineno=1149)
2025-08-27 14:20:27,002 [DEBUG] numba.core.byteflow: stack []
2025-08-27 14:20:27,002 [DEBUG] numba.core.byteflow: dispatch pc=124, inst=LOAD_ATTR(arg=2, lineno=1149)
2025-08-27 14:20:27,002 [DEBUG] numba.core.byteflow: stack ['$114load_global.0']
2025-08-27 14:20:27,002 [DEBUG] numba.core.byteflow: dispatch pc=144, inst=PUSH_NULL(arg=None, lineno=1149)
2025-08-27 14:20:27,002 [DEBUG] numba.core.byteflow: stack ['$124load_attr.1']
2025-08-27 14:20:27,002 [DEBUG] numba.core.byteflow: dispatch pc=146, inst=LOAD_FAST(arg=3, lineno=1149)
2025-08-27 14:20:27,002 [DEBUG] numba.core.byteflow: stack ['$124load_attr.1', '$null$144.2']
2025-08-27 14:20:27,002 [DEBUG] numba.core.byteflow: dispatch pc=148, inst=CALL(arg=1, lineno=1149)
2025-08-27 14:20:27,002 [DEBUG] numba.core.byteflow: stack ['$124load_attr.1', '$null$144.2', '$x0146.3']
2025-08-27 14:20:27,002 [DEBUG] numba.core.byteflow: dispatch pc=156, inst=LOAD_GLOBAL(arg=0, lineno=1149)
2025-08-27 14:20:27,002 [DEBUG] numba.core.byteflow: stack ['$148call.4']
2025-08-27 14:20:27,002 [DEBUG] numba.core.byteflow: dispatch pc=166, inst=LOAD_ATTR(arg=2, lineno=1149)
2025-08-27 14:20:27,002 [DEBUG] numba.core.byteflow: stack ['$148call.4', '$156load_global.5']
2025-08-27 14:20:27,002 [DEBUG] numba.core.byteflow: dispatch pc=186, inst=PUSH_NULL(arg=None, lineno=1149)
2025-08-27 14:20:27,002 [DEBUG] numba.core.byteflow: stack ['$148call.4', '$166load_attr.6']
2025-08-27 14:20:27,002 [DEBUG] numba.core.byteflow: dispatch pc=188, inst=LOAD_FAST(arg=4, lineno=1149)
2025-08-27 14:20:27,002 [DEBUG] numba.core.byteflow: stack ['$148call.4', '$166load_attr.6', '$null$186.7']
2025-08-27 14:20:27,002 [DEBUG] numba.core.byteflow: dispatch pc=190, inst=CALL(arg=1, lineno=1149)
2025-08-27 14:20:27,002 [DEBUG] numba.core.byteflow: stack ['$148call.4', '$166load_attr.6', '$null$186.7', '$x1188.8']
2025-08-27 14:20:27,002 [DEBUG] numba.core.byteflow: dispatch pc=198, inst=COMPARE_OP(arg=103, lineno=1149)
2025-08-27 14:20:27,002 [DEBUG] numba.core.byteflow: stack ['$148call.4', '$190call.9']
2025-08-27 14:20:27,002 [DEBUG] numba.core.byteflow: dispatch pc=202, inst=RETURN_VALUE(arg=None, lineno=1149)
2025-08-27 14:20:27,002 [DEBUG] numba.core.byteflow: stack ['$198compare_op.10']
2025-08-27 14:20:27,002 [DEBUG] numba.core.byteflow: end state. edges=[]
2025-08-27 14:20:27,002 [DEBUG] numba.core.byteflow: pending: deque([State(pc_initial=204 nstack_initial=0), State(pc_initial=100 nstack_initial=0)])
2025-08-27 14:20:27,002 [DEBUG] numba.core.byteflow: stack: []
2025-08-27 14:20:27,002 [DEBUG] numba.core.byteflow: state.pc_initial: State(pc_initial=204 nstack_initial=0)
2025-08-27 14:20:27,002 [DEBUG] numba.core.byteflow: dispatch pc=204, inst=LOAD_GLOBAL(arg=0, lineno=1151)
2025-08-27 14:20:27,002 [DEBUG] numba.core.byteflow: stack []
2025-08-27 14:20:27,002 [DEBUG] numba.core.byteflow: dispatch pc=214, inst=LOAD_ATTR(arg=4, lineno=1151)
2025-08-27 14:20:27,002 [DEBUG] numba.core.byteflow: stack ['$204load_global.0']
2025-08-27 14:20:27,002 [DEBUG] numba.core.byteflow: dispatch pc=234, inst=PUSH_NULL(arg=None, lineno=1151)
2025-08-27 14:20:27,003 [DEBUG] numba.core.byteflow: stack ['$214load_attr.1']
2025-08-27 14:20:27,003 [DEBUG] numba.core.byteflow: dispatch pc=236, inst=LOAD_FAST(arg=3, lineno=1151)
2025-08-27 14:20:27,003 [DEBUG] numba.core.byteflow: stack ['$214load_attr.1', '$null$234.2']
2025-08-27 14:20:27,003 [DEBUG] numba.core.byteflow: dispatch pc=238, inst=CALL(arg=1, lineno=1151)
2025-08-27 14:20:27,003 [DEBUG] numba.core.byteflow: stack ['$214load_attr.1', '$null$234.2', '$x0236.3']
2025-08-27 14:20:27,003 [DEBUG] numba.core.byteflow: dispatch pc=246, inst=LOAD_GLOBAL(arg=0, lineno=1151)
2025-08-27 14:20:27,003 [DEBUG] numba.core.byteflow: stack ['$238call.4']
2025-08-27 14:20:27,003 [DEBUG] numba.core.byteflow: dispatch pc=256, inst=LOAD_ATTR(arg=4, lineno=1151)
2025-08-27 14:20:27,003 [DEBUG] numba.core.byteflow: stack ['$238call.4', '$246load_global.5']
2025-08-27 14:20:27,003 [DEBUG] numba.core.byteflow: dispatch pc=276, inst=PUSH_NULL(arg=None, lineno=1151)
2025-08-27 14:20:27,003 [DEBUG] numba.core.byteflow: stack ['$238call.4', '$256load_attr.6']
2025-08-27 14:20:27,003 [DEBUG] numba.core.byteflow: dispatch pc=278, inst=LOAD_FAST(arg=4, lineno=1151)
2025-08-27 14:20:27,003 [DEBUG] numba.core.byteflow: stack ['$238call.4', '$256load_attr.6', '$null$276.7']
2025-08-27 14:20:27,003 [DEBUG] numba.core.byteflow: dispatch pc=280, inst=CALL(arg=1, lineno=1151)
2025-08-27 14:20:27,003 [DEBUG] numba.core.byteflow: stack ['$238call.4', '$256load_attr.6', '$null$276.7', '$x1278.8']
2025-08-27 14:20:27,003 [DEBUG] numba.core.byteflow: dispatch pc=288, inst=COMPARE_OP(arg=103, lineno=1151)
2025-08-27 14:20:27,003 [DEBUG] numba.core.byteflow: stack ['$238call.4', '$280call.9']
2025-08-27 14:20:27,003 [DEBUG] numba.core.byteflow: dispatch pc=292, inst=RETURN_VALUE(arg=None, lineno=1151)
2025-08-27 14:20:27,003 [DEBUG] numba.core.byteflow: stack ['$288compare_op.10']
2025-08-27 14:20:27,003 [DEBUG] numba.core.byteflow: end state. edges=[]
2025-08-27 14:20:27,003 [DEBUG] numba.core.byteflow: pending: deque([State(pc_initial=100 nstack_initial=0)])
2025-08-27 14:20:27,003 [DEBUG] numba.core.byteflow: -------------------------Prune PHIs-------------------------
2025-08-27 14:20:27,003 [DEBUG] numba.core.byteflow: Used_phis: defaultdict(<class 'set'>,
            {State(pc_initial=0 nstack_initial=0): set(),
             State(pc_initial=32 nstack_initial=1): {'$phi32.0'},
             State(pc_initial=42 nstack_initial=0): set(),
             State(pc_initial=44 nstack_initial=1): set(),
             State(pc_initial=48 nstack_initial=0): set(),
             State(pc_initial=52 nstack_initial=0): set(),
             State(pc_initial=80 nstack_initial=1): {'$phi80.0'},
             State(pc_initial=90 nstack_initial=0): set(),
             State(pc_initial=92 nstack_initial=1): set(),
             State(pc_initial=96 nstack_initial=0): set(),
             State(pc_initial=100 nstack_initial=0): set(),
             State(pc_initial=114 nstack_initial=0): set(),
             State(pc_initial=204 nstack_initial=0): set()})
2025-08-27 14:20:27,003 [DEBUG] numba.core.byteflow: defmap: {'$phi32.0': State(pc_initial=0 nstack_initial=0),
 '$phi44.0': State(pc_initial=0 nstack_initial=0),
 '$phi80.0': State(pc_initial=52 nstack_initial=0),
 '$phi92.0': State(pc_initial=52 nstack_initial=0)}
2025-08-27 14:20:27,003 [DEBUG] numba.core.byteflow: phismap: defaultdict(<class 'set'>,
            {'$phi32.0': {('$x018.5', State(pc_initial=0 nstack_initial=0))},
             '$phi44.0': {('$x018.5', State(pc_initial=0 nstack_initial=0))},
             '$phi80.0': {('$x166.5', State(pc_initial=52 nstack_initial=0))},
             '$phi92.0': {('$x166.5', State(pc_initial=52 nstack_initial=0))}})
2025-08-27 14:20:27,003 [DEBUG] numba.core.byteflow: changing phismap: defaultdict(<class 'set'>,
            {'$phi32.0': {('$x018.5', State(pc_initial=0 nstack_initial=0))},
             '$phi44.0': {('$x018.5', State(pc_initial=0 nstack_initial=0))},
             '$phi80.0': {('$x166.5', State(pc_initial=52 nstack_initial=0))},
             '$phi92.0': {('$x166.5', State(pc_initial=52 nstack_initial=0))}})
2025-08-27 14:20:27,003 [DEBUG] numba.core.byteflow: keep phismap: {'$phi32.0': {('$x018.5', State(pc_initial=0 nstack_initial=0))},
 '$phi80.0': {('$x166.5', State(pc_initial=52 nstack_initial=0))}}
2025-08-27 14:20:27,003 [DEBUG] numba.core.byteflow: new_out: defaultdict(<class 'dict'>,
            {State(pc_initial=0 nstack_initial=0): {'$phi32.0': '$x018.5'},
             State(pc_initial=52 nstack_initial=0): {'$phi80.0': '$x166.5'}})
2025-08-27 14:20:27,003 [DEBUG] numba.core.byteflow: ----------------------DONE Prune PHIs-----------------------
2025-08-27 14:20:27,003 [DEBUG] numba.core.byteflow: block_infos State(pc_initial=0 nstack_initial=0):
AdaptBlockInfo(insts=((0, {}), (2, {}), (4, {'res': '$x4.0'}), (6, {'res': '$const6.1.1'}), (8, {'index': '$const6.1.1', 'target': '$x4.0', 'res': '$8binary_subscr.2'}), (12, {'value': '$8binary_subscr.2'}), (14, {'res': '$threshold14.3'}), (16, {'value': '$threshold14.3', 'res': '$16unary_negative.4'}), (18, {'res': '$x018.5'}), (24, {'lhs': '$16unary_negative.4', 'rhs': '$x018.5', 'res': '$24compare_op.6'}), (28, {'pred': '$24compare_op.6'})), outgoing_phis={'$phi32.0': '$x018.5'}, blockstack=(), active_try_block=None, outgoing_edgepushed={32: ('$x018.5',), 44: ('$x018.5',)})
2025-08-27 14:20:27,003 [DEBUG] numba.core.byteflow: block_infos State(pc_initial=32 nstack_initial=1):
AdaptBlockInfo(insts=((32, {'res': '$threshold32.1'}), (34, {'lhs': '$phi32.0', 'rhs': '$threshold32.1', 'res': '$34compare_op.2'}), (38, {'pred': '$34compare_op.2'})), outgoing_phis={}, blockstack=(), active_try_block=None, outgoing_edgepushed={42: (), 52: ()})
2025-08-27 14:20:27,003 [DEBUG] numba.core.byteflow: block_infos State(pc_initial=42 nstack_initial=0):
AdaptBlockInfo(insts=((42, {}),), outgoing_phis={}, blockstack=(), active_try_block=None, outgoing_edgepushed={48: ()})
2025-08-27 14:20:27,003 [DEBUG] numba.core.byteflow: block_infos State(pc_initial=44 nstack_initial=1):
AdaptBlockInfo(insts=((46, {}),), outgoing_phis={}, blockstack=(), active_try_block=None, outgoing_edgepushed={52: ()})
2025-08-27 14:20:27,003 [DEBUG] numba.core.byteflow: block_infos State(pc_initial=48 nstack_initial=0):
AdaptBlockInfo(insts=((48, {'res': '$const48.0.1'}), (50, {'value': '$const48.0.1'})), outgoing_phis={}, blockstack=(), active_try_block=None, outgoing_edgepushed={52: ()})
2025-08-27 14:20:27,003 [DEBUG] numba.core.byteflow: block_infos State(pc_initial=52 nstack_initial=0):
AdaptBlockInfo(insts=((52, {'res': '$x52.0'}), (54, {'res': '$const54.1.2'}), (56, {'index': '$const54.1.2', 'target': '$x52.0', 'res': '$56binary_subscr.2'}), (60, {'value': '$56binary_subscr.2'}), (62, {'res': '$threshold62.3'}), (64, {'value': '$threshold62.3', 'res': '$64unary_negative.4'}), (66, {'res': '$x166.5'}), (72, {'lhs': '$64unary_negative.4', 'rhs': '$x166.5', 'res': '$72compare_op.6'}), (76, {'pred': '$72compare_op.6'})), outgoing_phis={'$phi80.0': '$x166.5'}, blockstack=(), active_try_block=None, outgoing_edgepushed={80: ('$x166.5',), 92: ('$x166.5',)})
2025-08-27 14:20:27,003 [DEBUG] numba.core.byteflow: block_infos State(pc_initial=80 nstack_initial=1):
AdaptBlockInfo(insts=((80, {'res': '$threshold80.1'}), (82, {'lhs': '$phi80.0', 'rhs': '$threshold80.1', 'res': '$82compare_op.2'}), (86, {'pred': '$82compare_op.2'})), outgoing_phis={}, blockstack=(), active_try_block=None, outgoing_edgepushed={90: (), 100: ()})
2025-08-27 14:20:27,004 [DEBUG] numba.core.byteflow: block_infos State(pc_initial=90 nstack_initial=0):
AdaptBlockInfo(insts=((90, {}),), outgoing_phis={}, blockstack=(), active_try_block=None, outgoing_edgepushed={96: ()})
2025-08-27 14:20:27,004 [DEBUG] numba.core.byteflow: block_infos State(pc_initial=92 nstack_initial=1):
AdaptBlockInfo(insts=((94, {}),), outgoing_phis={}, blockstack=(), active_try_block=None, outgoing_edgepushed={100: ()})
2025-08-27 14:20:27,004 [DEBUG] numba.core.byteflow: block_infos State(pc_initial=96 nstack_initial=0):
AdaptBlockInfo(insts=((96, {'res': '$const96.0.1'}), (98, {'value': '$const96.0.1'})), outgoing_phis={}, blockstack=(), active_try_block=None, outgoing_edgepushed={100: ()})
2025-08-27 14:20:27,004 [DEBUG] numba.core.byteflow: block_infos State(pc_initial=100 nstack_initial=0):
AdaptBlockInfo(insts=((100, {'res': '$zero_pos100.0'}), (102, {'val': '$zero_pos100.0', 'res': '$102to_bool.1'}), (110, {'pred': '$102to_bool.1'})), outgoing_phis={}, blockstack=(), active_try_block=None, outgoing_edgepushed={114: (), 204: ()})
2025-08-27 14:20:27,004 [DEBUG] numba.core.byteflow: block_infos State(pc_initial=114 nstack_initial=0):
AdaptBlockInfo(insts=((114, {'idx': 0, 'res': '$114load_global.0'}), (124, {'item': '$114load_global.0', 'res': '$124load_attr.1'}), (144, {}), (146, {'res': '$x0146.3'}), (148, {'func': '$124load_attr.1', 'args': ['$x0146.3'], 'kw_names': None, 'res': '$148call.4'}), (156, {'idx': 0, 'res': '$156load_global.5'}), (166, {'item': '$156load_global.5', 'res': '$166load_attr.6'}), (186, {}), (188, {'res': '$x1188.8'}), (190, {'func': '$166load_attr.6', 'args': ['$x1188.8'], 'kw_names': None, 'res': '$190call.9'}), (198, {'lhs': '$148call.4', 'rhs': '$190call.9', 'res': '$198compare_op.10'}), (202, {'retval': '$198compare_op.10', 'castval': '$202return_value.11'})), outgoing_phis={}, blockstack=(), active_try_block=None, outgoing_edgepushed={})
2025-08-27 14:20:27,004 [DEBUG] numba.core.byteflow: block_infos State(pc_initial=204 nstack_initial=0):
AdaptBlockInfo(insts=((204, {'idx': 0, 'res': '$204load_global.0'}), (214, {'item': '$204load_global.0', 'res': '$214load_attr.1'}), (234, {}), (236, {'res': '$x0236.3'}), (238, {'func': '$214load_attr.1', 'args': ['$x0236.3'], 'kw_names': None, 'res': '$238call.4'}), (246, {'idx': 0, 'res': '$246load_global.5'}), (256, {'item': '$246load_global.5', 'res': '$256load_attr.6'}), (276, {}), (278, {'res': '$x1278.8'}), (280, {'func': '$256load_attr.6', 'args': ['$x1278.8'], 'kw_names': None, 'res': '$280call.9'}), (288, {'lhs': '$238call.4', 'rhs': '$280call.9', 'res': '$288compare_op.10'}), (292, {'retval': '$288compare_op.10', 'castval': '$292return_value.11'})), outgoing_phis={}, blockstack=(), active_try_block=None, outgoing_edgepushed={})
2025-08-27 14:20:27,004 [DEBUG] numba.core.interpreter: label 0:
    x = arg(0, name=x)                       ['x']
    threshold = arg(1, name=threshold)       ['threshold']
    zero_pos = arg(2, name=zero_pos)         ['zero_pos']
    $const6.1.1 = const(int, 0)              ['$const6.1.1']
    x0 = getitem(value=x, index=$const6.1.1, fn=<built-in function getitem>) ['$const6.1.1', 'x', 'x0']
    $16unary_negative.4 = unary(fn=<built-in function neg>, value=threshold) ['$16unary_negative.4', 'threshold']
    $24compare_op.6 = $16unary_negative.4 <= x0 ['$16unary_negative.4', '$24compare_op.6', 'x0']
    bool28 = global(bool: <class 'bool'>)    ['bool28']
    $28pred = call bool28($24compare_op.6, func=bool28, args=(Var($24compare_op.6, audio.py:1141),), kws=(), vararg=None, varkwarg=None, target=None) ['$24compare_op.6', '$28pred', 'bool28']
    $phi32.0 = x0                            ['$phi32.0', 'x0']
    branch $28pred, 32, 44                   ['$28pred']
label 32:
    $34compare_op.2 = $phi32.0 <= threshold  ['$34compare_op.2', '$phi32.0', 'threshold']
    bool38 = global(bool: <class 'bool'>)    ['bool38']
    $38pred = call bool38($34compare_op.2, func=bool38, args=(Var($34compare_op.2, audio.py:1141),), kws=(), vararg=None, varkwarg=None, target=None) ['$34compare_op.2', '$38pred', 'bool38']
    branch $38pred, 42, 52                   ['$38pred']
label 42:
    jump 48                                  []
label 44:
    jump 52                                  []
label 48:
    x0 = const(int, 0)                       ['x0']
    jump 52                                  []
label 52:
    $const54.1.2 = const(int, -1)            ['$const54.1.2']
    x1 = getitem(value=x, index=$const54.1.2, fn=<built-in function getitem>) ['$const54.1.2', 'x', 'x1']
    $64unary_negative.4 = unary(fn=<built-in function neg>, value=threshold) ['$64unary_negative.4', 'threshold']
    $72compare_op.6 = $64unary_negative.4 <= x1 ['$64unary_negative.4', '$72compare_op.6', 'x1']
    bool76 = global(bool: <class 'bool'>)    ['bool76']
    $76pred = call bool76($72compare_op.6, func=bool76, args=(Var($72compare_op.6, audio.py:1145),), kws=(), vararg=None, varkwarg=None, target=None) ['$72compare_op.6', '$76pred', 'bool76']
    $phi80.0 = x1                            ['$phi80.0', 'x1']
    branch $76pred, 80, 92                   ['$76pred']
label 80:
    $82compare_op.2 = $phi80.0 <= threshold  ['$82compare_op.2', '$phi80.0', 'threshold']
    bool86 = global(bool: <class 'bool'>)    ['bool86']
    $86pred = call bool86($82compare_op.2, func=bool86, args=(Var($82compare_op.2, audio.py:1145),), kws=(), vararg=None, varkwarg=None, target=None) ['$82compare_op.2', '$86pred', 'bool86']
    branch $86pred, 90, 100                  ['$86pred']
label 90:
    jump 96                                  []
label 92:
    jump 100                                 []
label 96:
    x1 = const(int, 0)                       ['x1']
    jump 100                                 []
label 100:
    bool110 = global(bool: <class 'bool'>)   ['bool110']
    $110pred = call bool110(zero_pos, func=bool110, args=(Var(zero_pos, audio.py:1137),), kws=(), vararg=None, varkwarg=None, target=None) ['$110pred', 'bool110', 'zero_pos']
    branch $110pred, 114, 204                ['$110pred']
label 114:
    $114load_global.0 = global(np: <module 'numpy' from '/Users/<USER>/WorkSpace/obs-ai-translator/venv/lib/python3.13/site-packages/numpy/__init__.py'>) ['$114load_global.0']
    $124load_attr.1 = getattr(value=$114load_global.0, attr=signbit) ['$114load_global.0', '$124load_attr.1']
    $148call.4 = call $124load_attr.1(x0, func=$124load_attr.1, args=[Var(x0, audio.py:1140)], kws=(), vararg=None, varkwarg=None, target=None) ['$124load_attr.1', '$148call.4', 'x0']
    $156load_global.5 = global(np: <module 'numpy' from '/Users/<USER>/WorkSpace/obs-ai-translator/venv/lib/python3.13/site-packages/numpy/__init__.py'>) ['$156load_global.5']
    $166load_attr.6 = getattr(value=$156load_global.5, attr=signbit) ['$156load_global.5', '$166load_attr.6']
    $190call.9 = call $166load_attr.6(x1, func=$166load_attr.6, args=[Var(x1, audio.py:1144)], kws=(), vararg=None, varkwarg=None, target=None) ['$166load_attr.6', '$190call.9', 'x1']
    $198compare_op.10 = $148call.4 != $190call.9 ['$148call.4', '$190call.9', '$198compare_op.10']
    $202return_value.11 = cast(value=$198compare_op.10) ['$198compare_op.10', '$202return_value.11']
    return $202return_value.11               ['$202return_value.11']
label 204:
    $204load_global.0 = global(np: <module 'numpy' from '/Users/<USER>/WorkSpace/obs-ai-translator/venv/lib/python3.13/site-packages/numpy/__init__.py'>) ['$204load_global.0']
    $214load_attr.1 = getattr(value=$204load_global.0, attr=sign) ['$204load_global.0', '$214load_attr.1']
    $238call.4 = call $214load_attr.1(x0, func=$214load_attr.1, args=[Var(x0, audio.py:1140)], kws=(), vararg=None, varkwarg=None, target=None) ['$214load_attr.1', '$238call.4', 'x0']
    $246load_global.5 = global(np: <module 'numpy' from '/Users/<USER>/WorkSpace/obs-ai-translator/venv/lib/python3.13/site-packages/numpy/__init__.py'>) ['$246load_global.5']
    $256load_attr.6 = getattr(value=$246load_global.5, attr=sign) ['$246load_global.5', '$256load_attr.6']
    $280call.9 = call $256load_attr.6(x1, func=$256load_attr.6, args=[Var(x1, audio.py:1144)], kws=(), vararg=None, varkwarg=None, target=None) ['$256load_attr.6', '$280call.9', 'x1']
    $288compare_op.10 = $238call.4 != $280call.9 ['$238call.4', '$280call.9', '$288compare_op.10']
    $292return_value.11 = cast(value=$288compare_op.10) ['$288compare_op.10', '$292return_value.11']
    return $292return_value.11               ['$292return_value.11']

2025-08-27 14:20:27,231 [DEBUG] numba.core.byteflow: bytecode dump:
>          0	NOP(arg=None, lineno=1023)
           2	RESUME(arg=0, lineno=1023)
           4	LOAD_FAST(arg=0, lineno=1026)
           6	LOAD_CONST(arg=1, lineno=1026)
           8	BINARY_SUBSCR(arg=None, lineno=1026)
          12	LOAD_FAST(arg=0, lineno=1026)
          14	LOAD_CONST(arg=2, lineno=1026)
          16	BINARY_SUBSCR(arg=None, lineno=1026)
          20	COMPARE_OP(arg=132, lineno=1026)
          24	LOAD_FAST(arg=0, lineno=1026)
          26	LOAD_CONST(arg=1, lineno=1026)
          28	BINARY_SUBSCR(arg=None, lineno=1026)
          32	LOAD_FAST(arg=0, lineno=1026)
          34	LOAD_CONST(arg=3, lineno=1026)
          36	BINARY_SUBSCR(arg=None, lineno=1026)
          40	COMPARE_OP(arg=172, lineno=1026)
          44	BINARY_OP(arg=1, lineno=1026)
          48	RETURN_VALUE(arg=None, lineno=1026)
2025-08-27 14:20:27,231 [DEBUG] numba.core.byteflow: pending: deque([State(pc_initial=0 nstack_initial=0)])
2025-08-27 14:20:27,231 [DEBUG] numba.core.byteflow: stack: []
2025-08-27 14:20:27,231 [DEBUG] numba.core.byteflow: state.pc_initial: State(pc_initial=0 nstack_initial=0)
2025-08-27 14:20:27,231 [DEBUG] numba.core.byteflow: dispatch pc=0, inst=NOP(arg=None, lineno=1023)
2025-08-27 14:20:27,231 [DEBUG] numba.core.byteflow: stack []
2025-08-27 14:20:27,231 [DEBUG] numba.core.byteflow: dispatch pc=2, inst=RESUME(arg=0, lineno=1023)
2025-08-27 14:20:27,231 [DEBUG] numba.core.byteflow: stack []
2025-08-27 14:20:27,231 [DEBUG] numba.core.byteflow: dispatch pc=4, inst=LOAD_FAST(arg=0, lineno=1026)
2025-08-27 14:20:27,231 [DEBUG] numba.core.byteflow: stack []
2025-08-27 14:20:27,231 [DEBUG] numba.core.byteflow: dispatch pc=6, inst=LOAD_CONST(arg=1, lineno=1026)
2025-08-27 14:20:27,231 [DEBUG] numba.core.byteflow: stack ['$x4.0']
2025-08-27 14:20:27,231 [DEBUG] numba.core.byteflow: dispatch pc=8, inst=BINARY_SUBSCR(arg=None, lineno=1026)
2025-08-27 14:20:27,231 [DEBUG] numba.core.byteflow: stack ['$x4.0', '$const6.1.1']
2025-08-27 14:20:27,231 [DEBUG] numba.core.byteflow: dispatch pc=12, inst=LOAD_FAST(arg=0, lineno=1026)
2025-08-27 14:20:27,231 [DEBUG] numba.core.byteflow: stack ['$8binary_subscr.2']
2025-08-27 14:20:27,231 [DEBUG] numba.core.byteflow: dispatch pc=14, inst=LOAD_CONST(arg=2, lineno=1026)
2025-08-27 14:20:27,231 [DEBUG] numba.core.byteflow: stack ['$8binary_subscr.2', '$x12.3']
2025-08-27 14:20:27,231 [DEBUG] numba.core.byteflow: dispatch pc=16, inst=BINARY_SUBSCR(arg=None, lineno=1026)
2025-08-27 14:20:27,231 [DEBUG] numba.core.byteflow: stack ['$8binary_subscr.2', '$x12.3', '$const14.4.2']
2025-08-27 14:20:27,231 [DEBUG] numba.core.byteflow: dispatch pc=20, inst=COMPARE_OP(arg=132, lineno=1026)
2025-08-27 14:20:27,232 [DEBUG] numba.core.byteflow: stack ['$8binary_subscr.2', '$16binary_subscr.5']
2025-08-27 14:20:27,232 [DEBUG] numba.core.byteflow: dispatch pc=24, inst=LOAD_FAST(arg=0, lineno=1026)
2025-08-27 14:20:27,232 [DEBUG] numba.core.byteflow: stack ['$20compare_op.6']
2025-08-27 14:20:27,232 [DEBUG] numba.core.byteflow: dispatch pc=26, inst=LOAD_CONST(arg=1, lineno=1026)
2025-08-27 14:20:27,232 [DEBUG] numba.core.byteflow: stack ['$20compare_op.6', '$x24.7']
2025-08-27 14:20:27,232 [DEBUG] numba.core.byteflow: dispatch pc=28, inst=BINARY_SUBSCR(arg=None, lineno=1026)
2025-08-27 14:20:27,232 [DEBUG] numba.core.byteflow: stack ['$20compare_op.6', '$x24.7', '$const26.8.1']
2025-08-27 14:20:27,232 [DEBUG] numba.core.byteflow: dispatch pc=32, inst=LOAD_FAST(arg=0, lineno=1026)
2025-08-27 14:20:27,232 [DEBUG] numba.core.byteflow: stack ['$20compare_op.6', '$28binary_subscr.9']
2025-08-27 14:20:27,232 [DEBUG] numba.core.byteflow: dispatch pc=34, inst=LOAD_CONST(arg=3, lineno=1026)
2025-08-27 14:20:27,232 [DEBUG] numba.core.byteflow: stack ['$20compare_op.6', '$28binary_subscr.9', '$x32.10']
2025-08-27 14:20:27,232 [DEBUG] numba.core.byteflow: dispatch pc=36, inst=BINARY_SUBSCR(arg=None, lineno=1026)
2025-08-27 14:20:27,232 [DEBUG] numba.core.byteflow: stack ['$20compare_op.6', '$28binary_subscr.9', '$x32.10', '$const34.11.3']
2025-08-27 14:20:27,232 [DEBUG] numba.core.byteflow: dispatch pc=40, inst=COMPARE_OP(arg=172, lineno=1026)
2025-08-27 14:20:27,232 [DEBUG] numba.core.byteflow: stack ['$20compare_op.6', '$28binary_subscr.9', '$36binary_subscr.12']
2025-08-27 14:20:27,232 [DEBUG] numba.core.byteflow: dispatch pc=44, inst=BINARY_OP(arg=1, lineno=1026)
2025-08-27 14:20:27,232 [DEBUG] numba.core.byteflow: stack ['$20compare_op.6', '$40compare_op.13']
2025-08-27 14:20:27,232 [DEBUG] numba.core.byteflow: dispatch pc=48, inst=RETURN_VALUE(arg=None, lineno=1026)
2025-08-27 14:20:27,232 [DEBUG] numba.core.byteflow: stack ['$binop_and_44.14']
2025-08-27 14:20:27,232 [DEBUG] numba.core.byteflow: end state. edges=[]
2025-08-27 14:20:27,232 [DEBUG] numba.core.byteflow: -------------------------Prune PHIs-------------------------
2025-08-27 14:20:27,232 [DEBUG] numba.core.byteflow: Used_phis: defaultdict(<class 'set'>, {State(pc_initial=0 nstack_initial=0): set()})
2025-08-27 14:20:27,232 [DEBUG] numba.core.byteflow: defmap: {}
2025-08-27 14:20:27,232 [DEBUG] numba.core.byteflow: phismap: defaultdict(<class 'set'>, {})
2025-08-27 14:20:27,232 [DEBUG] numba.core.byteflow: changing phismap: defaultdict(<class 'set'>, {})
2025-08-27 14:20:27,232 [DEBUG] numba.core.byteflow: keep phismap: {}
2025-08-27 14:20:27,232 [DEBUG] numba.core.byteflow: new_out: defaultdict(<class 'dict'>, {})
2025-08-27 14:20:27,232 [DEBUG] numba.core.byteflow: ----------------------DONE Prune PHIs-----------------------
2025-08-27 14:20:27,232 [DEBUG] numba.core.byteflow: block_infos State(pc_initial=0 nstack_initial=0):
AdaptBlockInfo(insts=((0, {}), (2, {}), (4, {'res': '$x4.0'}), (6, {'res': '$const6.1.1'}), (8, {'index': '$const6.1.1', 'target': '$x4.0', 'res': '$8binary_subscr.2'}), (12, {'res': '$x12.3'}), (14, {'res': '$const14.4.2'}), (16, {'index': '$const14.4.2', 'target': '$x12.3', 'res': '$16binary_subscr.5'}), (20, {'lhs': '$8binary_subscr.2', 'rhs': '$16binary_subscr.5', 'res': '$20compare_op.6'}), (24, {'res': '$x24.7'}), (26, {'res': '$const26.8.1'}), (28, {'index': '$const26.8.1', 'target': '$x24.7', 'res': '$28binary_subscr.9'}), (32, {'res': '$x32.10'}), (34, {'res': '$const34.11.3'}), (36, {'index': '$const34.11.3', 'target': '$x32.10', 'res': '$36binary_subscr.12'}), (40, {'lhs': '$28binary_subscr.9', 'rhs': '$36binary_subscr.12', 'res': '$40compare_op.13'}), (44, {'op': '&', 'lhs': '$20compare_op.6', 'rhs': '$40compare_op.13', 'res': '$binop_and_44.14'}), (48, {'retval': '$binop_and_44.14', 'castval': '$48return_value.15'})), outgoing_phis={}, blockstack=(), active_try_block=None, outgoing_edgepushed={})
2025-08-27 14:20:27,232 [DEBUG] numba.core.interpreter: label 0:
    x = arg(0, name=x)                       ['x']
    $const6.1.1 = const(int, 0)              ['$const6.1.1']
    $8binary_subscr.2 = getitem(value=x, index=$const6.1.1, fn=<built-in function getitem>) ['$8binary_subscr.2', '$const6.1.1', 'x']
    $const14.4.2 = const(int, -1)            ['$const14.4.2']
    $16binary_subscr.5 = getitem(value=x, index=$const14.4.2, fn=<built-in function getitem>) ['$16binary_subscr.5', '$const14.4.2', 'x']
    $20compare_op.6 = $8binary_subscr.2 > $16binary_subscr.5 ['$16binary_subscr.5', '$20compare_op.6', '$8binary_subscr.2']
    $const26.8.1 = const(int, 0)             ['$const26.8.1']
    $28binary_subscr.9 = getitem(value=x, index=$const26.8.1, fn=<built-in function getitem>) ['$28binary_subscr.9', '$const26.8.1', 'x']
    $const34.11.3 = const(int, 1)            ['$const34.11.3']
    $36binary_subscr.12 = getitem(value=x, index=$const34.11.3, fn=<built-in function getitem>) ['$36binary_subscr.12', '$const34.11.3', 'x']
    $40compare_op.13 = $28binary_subscr.9 >= $36binary_subscr.12 ['$28binary_subscr.9', '$36binary_subscr.12', '$40compare_op.13']
    $binop_and_44.14 = $20compare_op.6 & $40compare_op.13 ['$20compare_op.6', '$40compare_op.13', '$binop_and_44.14']
    $48return_value.15 = cast(value=$binop_and_44.14) ['$48return_value.15', '$binop_and_44.14']
    return $48return_value.15                ['$48return_value.15']

2025-08-27 14:20:27,232 [DEBUG] numba.core.byteflow: bytecode dump:
>          0	NOP(arg=None, lineno=1029)
           2	RESUME(arg=0, lineno=1029)
           4	LOAD_FAST(arg=0, lineno=1032)
           6	LOAD_CONST(arg=1, lineno=1032)
           8	BINARY_SUBSCR(arg=None, lineno=1032)
          12	LOAD_FAST(arg=0, lineno=1032)
          14	LOAD_CONST(arg=2, lineno=1032)
          16	BINARY_SUBSCR(arg=None, lineno=1032)
          20	COMPARE_OP(arg=2, lineno=1032)
          24	LOAD_FAST(arg=0, lineno=1032)
          26	LOAD_CONST(arg=1, lineno=1032)
          28	BINARY_SUBSCR(arg=None, lineno=1032)
          32	LOAD_FAST(arg=0, lineno=1032)
          34	LOAD_CONST(arg=3, lineno=1032)
          36	BINARY_SUBSCR(arg=None, lineno=1032)
          40	COMPARE_OP(arg=42, lineno=1032)
          44	BINARY_OP(arg=1, lineno=1032)
          48	RETURN_VALUE(arg=None, lineno=1032)
2025-08-27 14:20:27,232 [DEBUG] numba.core.byteflow: pending: deque([State(pc_initial=0 nstack_initial=0)])
2025-08-27 14:20:27,233 [DEBUG] numba.core.byteflow: stack: []
2025-08-27 14:20:27,233 [DEBUG] numba.core.byteflow: state.pc_initial: State(pc_initial=0 nstack_initial=0)
2025-08-27 14:20:27,233 [DEBUG] numba.core.byteflow: dispatch pc=0, inst=NOP(arg=None, lineno=1029)
2025-08-27 14:20:27,233 [DEBUG] numba.core.byteflow: stack []
2025-08-27 14:20:27,233 [DEBUG] numba.core.byteflow: dispatch pc=2, inst=RESUME(arg=0, lineno=1029)
2025-08-27 14:20:27,233 [DEBUG] numba.core.byteflow: stack []
2025-08-27 14:20:27,233 [DEBUG] numba.core.byteflow: dispatch pc=4, inst=LOAD_FAST(arg=0, lineno=1032)
2025-08-27 14:20:27,233 [DEBUG] numba.core.byteflow: stack []
2025-08-27 14:20:27,233 [DEBUG] numba.core.byteflow: dispatch pc=6, inst=LOAD_CONST(arg=1, lineno=1032)
2025-08-27 14:20:27,233 [DEBUG] numba.core.byteflow: stack ['$x4.0']
2025-08-27 14:20:27,233 [DEBUG] numba.core.byteflow: dispatch pc=8, inst=BINARY_SUBSCR(arg=None, lineno=1032)
2025-08-27 14:20:27,233 [DEBUG] numba.core.byteflow: stack ['$x4.0', '$const6.1.1']
2025-08-27 14:20:27,233 [DEBUG] numba.core.byteflow: dispatch pc=12, inst=LOAD_FAST(arg=0, lineno=1032)
2025-08-27 14:20:27,233 [DEBUG] numba.core.byteflow: stack ['$8binary_subscr.2']
2025-08-27 14:20:27,233 [DEBUG] numba.core.byteflow: dispatch pc=14, inst=LOAD_CONST(arg=2, lineno=1032)
2025-08-27 14:20:27,233 [DEBUG] numba.core.byteflow: stack ['$8binary_subscr.2', '$x12.3']
2025-08-27 14:20:27,233 [DEBUG] numba.core.byteflow: dispatch pc=16, inst=BINARY_SUBSCR(arg=None, lineno=1032)
2025-08-27 14:20:27,233 [DEBUG] numba.core.byteflow: stack ['$8binary_subscr.2', '$x12.3', '$const14.4.2']
2025-08-27 14:20:27,233 [DEBUG] numba.core.byteflow: dispatch pc=20, inst=COMPARE_OP(arg=2, lineno=1032)
2025-08-27 14:20:27,233 [DEBUG] numba.core.byteflow: stack ['$8binary_subscr.2', '$16binary_subscr.5']
2025-08-27 14:20:27,233 [DEBUG] numba.core.byteflow: dispatch pc=24, inst=LOAD_FAST(arg=0, lineno=1032)
2025-08-27 14:20:27,233 [DEBUG] numba.core.byteflow: stack ['$20compare_op.6']
2025-08-27 14:20:27,233 [DEBUG] numba.core.byteflow: dispatch pc=26, inst=LOAD_CONST(arg=1, lineno=1032)
2025-08-27 14:20:27,233 [DEBUG] numba.core.byteflow: stack ['$20compare_op.6', '$x24.7']
2025-08-27 14:20:27,233 [DEBUG] numba.core.byteflow: dispatch pc=28, inst=BINARY_SUBSCR(arg=None, lineno=1032)
2025-08-27 14:20:27,233 [DEBUG] numba.core.byteflow: stack ['$20compare_op.6', '$x24.7', '$const26.8.1']
2025-08-27 14:20:27,233 [DEBUG] numba.core.byteflow: dispatch pc=32, inst=LOAD_FAST(arg=0, lineno=1032)
2025-08-27 14:20:27,233 [DEBUG] numba.core.byteflow: stack ['$20compare_op.6', '$28binary_subscr.9']
2025-08-27 14:20:27,233 [DEBUG] numba.core.byteflow: dispatch pc=34, inst=LOAD_CONST(arg=3, lineno=1032)
2025-08-27 14:20:27,233 [DEBUG] numba.core.byteflow: stack ['$20compare_op.6', '$28binary_subscr.9', '$x32.10']
2025-08-27 14:20:27,233 [DEBUG] numba.core.byteflow: dispatch pc=36, inst=BINARY_SUBSCR(arg=None, lineno=1032)
2025-08-27 14:20:27,233 [DEBUG] numba.core.byteflow: stack ['$20compare_op.6', '$28binary_subscr.9', '$x32.10', '$const34.11.3']
2025-08-27 14:20:27,233 [DEBUG] numba.core.byteflow: dispatch pc=40, inst=COMPARE_OP(arg=42, lineno=1032)
2025-08-27 14:20:27,233 [DEBUG] numba.core.byteflow: stack ['$20compare_op.6', '$28binary_subscr.9', '$36binary_subscr.12']
2025-08-27 14:20:27,233 [DEBUG] numba.core.byteflow: dispatch pc=44, inst=BINARY_OP(arg=1, lineno=1032)
2025-08-27 14:20:27,233 [DEBUG] numba.core.byteflow: stack ['$20compare_op.6', '$40compare_op.13']
2025-08-27 14:20:27,233 [DEBUG] numba.core.byteflow: dispatch pc=48, inst=RETURN_VALUE(arg=None, lineno=1032)
2025-08-27 14:20:27,233 [DEBUG] numba.core.byteflow: stack ['$binop_and_44.14']
2025-08-27 14:20:27,233 [DEBUG] numba.core.byteflow: end state. edges=[]
2025-08-27 14:20:27,233 [DEBUG] numba.core.byteflow: -------------------------Prune PHIs-------------------------
2025-08-27 14:20:27,233 [DEBUG] numba.core.byteflow: Used_phis: defaultdict(<class 'set'>, {State(pc_initial=0 nstack_initial=0): set()})
2025-08-27 14:20:27,233 [DEBUG] numba.core.byteflow: defmap: {}
2025-08-27 14:20:27,233 [DEBUG] numba.core.byteflow: phismap: defaultdict(<class 'set'>, {})
2025-08-27 14:20:27,233 [DEBUG] numba.core.byteflow: changing phismap: defaultdict(<class 'set'>, {})
2025-08-27 14:20:27,233 [DEBUG] numba.core.byteflow: keep phismap: {}
2025-08-27 14:20:27,233 [DEBUG] numba.core.byteflow: new_out: defaultdict(<class 'dict'>, {})
2025-08-27 14:20:27,233 [DEBUG] numba.core.byteflow: ----------------------DONE Prune PHIs-----------------------
2025-08-27 14:20:27,233 [DEBUG] numba.core.byteflow: block_infos State(pc_initial=0 nstack_initial=0):
AdaptBlockInfo(insts=((0, {}), (2, {}), (4, {'res': '$x4.0'}), (6, {'res': '$const6.1.1'}), (8, {'index': '$const6.1.1', 'target': '$x4.0', 'res': '$8binary_subscr.2'}), (12, {'res': '$x12.3'}), (14, {'res': '$const14.4.2'}), (16, {'index': '$const14.4.2', 'target': '$x12.3', 'res': '$16binary_subscr.5'}), (20, {'lhs': '$8binary_subscr.2', 'rhs': '$16binary_subscr.5', 'res': '$20compare_op.6'}), (24, {'res': '$x24.7'}), (26, {'res': '$const26.8.1'}), (28, {'index': '$const26.8.1', 'target': '$x24.7', 'res': '$28binary_subscr.9'}), (32, {'res': '$x32.10'}), (34, {'res': '$const34.11.3'}), (36, {'index': '$const34.11.3', 'target': '$x32.10', 'res': '$36binary_subscr.12'}), (40, {'lhs': '$28binary_subscr.9', 'rhs': '$36binary_subscr.12', 'res': '$40compare_op.13'}), (44, {'op': '&', 'lhs': '$20compare_op.6', 'rhs': '$40compare_op.13', 'res': '$binop_and_44.14'}), (48, {'retval': '$binop_and_44.14', 'castval': '$48return_value.15'})), outgoing_phis={}, blockstack=(), active_try_block=None, outgoing_edgepushed={})
2025-08-27 14:20:27,233 [DEBUG] numba.core.interpreter: label 0:
    x = arg(0, name=x)                       ['x']
    $const6.1.1 = const(int, 0)              ['$const6.1.1']
    $8binary_subscr.2 = getitem(value=x, index=$const6.1.1, fn=<built-in function getitem>) ['$8binary_subscr.2', '$const6.1.1', 'x']
    $const14.4.2 = const(int, -1)            ['$const14.4.2']
    $16binary_subscr.5 = getitem(value=x, index=$const14.4.2, fn=<built-in function getitem>) ['$16binary_subscr.5', '$const14.4.2', 'x']
    $20compare_op.6 = $8binary_subscr.2 < $16binary_subscr.5 ['$16binary_subscr.5', '$20compare_op.6', '$8binary_subscr.2']
    $const26.8.1 = const(int, 0)             ['$const26.8.1']
    $28binary_subscr.9 = getitem(value=x, index=$const26.8.1, fn=<built-in function getitem>) ['$28binary_subscr.9', '$const26.8.1', 'x']
    $const34.11.3 = const(int, 1)            ['$const34.11.3']
    $36binary_subscr.12 = getitem(value=x, index=$const34.11.3, fn=<built-in function getitem>) ['$36binary_subscr.12', '$const34.11.3', 'x']
    $40compare_op.13 = $28binary_subscr.9 <= $36binary_subscr.12 ['$28binary_subscr.9', '$36binary_subscr.12', '$40compare_op.13']
    $binop_and_44.14 = $20compare_op.6 & $40compare_op.13 ['$20compare_op.6', '$40compare_op.13', '$binop_and_44.14']
    $48return_value.15 = cast(value=$binop_and_44.14) ['$48return_value.15', '$binop_and_44.14']
    return $48return_value.15                ['$48return_value.15']

2025-08-27 14:20:27,329 [DEBUG] numba.core.byteflow: bytecode dump:
>          0	NOP(arg=None, lineno=81)
           2	RESUME(arg=0, lineno=81)
           4	LOAD_GLOBAL(arg=1, lineno=86)
          14	LOAD_FAST_LOAD_FAST(arg=1, lineno=86)
          16	LOAD_FAST_LOAD_FAST(arg=35, lineno=86)
          18	LOAD_FAST_LOAD_FAST(arg=69, lineno=86)
          20	LOAD_FAST(arg=6, lineno=86)
          22	CALL(arg=7, lineno=86)
          30	POP_TOP(arg=None, lineno=86)
          32	RETURN_CONST(arg=0, lineno=86)
2025-08-27 14:20:27,329 [DEBUG] numba.core.byteflow: pending: deque([State(pc_initial=0 nstack_initial=0)])
2025-08-27 14:20:27,329 [DEBUG] numba.core.byteflow: stack: []
2025-08-27 14:20:27,329 [DEBUG] numba.core.byteflow: state.pc_initial: State(pc_initial=0 nstack_initial=0)
2025-08-27 14:20:27,329 [DEBUG] numba.core.byteflow: dispatch pc=0, inst=NOP(arg=None, lineno=81)
2025-08-27 14:20:27,329 [DEBUG] numba.core.byteflow: stack []
2025-08-27 14:20:27,329 [DEBUG] numba.core.byteflow: dispatch pc=2, inst=RESUME(arg=0, lineno=81)
2025-08-27 14:20:27,329 [DEBUG] numba.core.byteflow: stack []
2025-08-27 14:20:27,329 [DEBUG] numba.core.byteflow: dispatch pc=4, inst=LOAD_GLOBAL(arg=1, lineno=86)
2025-08-27 14:20:27,329 [DEBUG] numba.core.byteflow: stack []
2025-08-27 14:20:27,329 [DEBUG] numba.core.byteflow: dispatch pc=14, inst=LOAD_FAST_LOAD_FAST(arg=1, lineno=86)
2025-08-27 14:20:27,329 [DEBUG] numba.core.byteflow: stack ['$4load_global.0', '$null$4.1']
2025-08-27 14:20:27,329 [DEBUG] numba.core.byteflow: dispatch pc=16, inst=LOAD_FAST_LOAD_FAST(arg=35, lineno=86)
2025-08-27 14:20:27,329 [DEBUG] numba.core.byteflow: stack ['$4load_global.0', '$null$4.1', '$x14.2', '$t_out14.3']
2025-08-27 14:20:27,329 [DEBUG] numba.core.byteflow: dispatch pc=18, inst=LOAD_FAST_LOAD_FAST(arg=69, lineno=86)
2025-08-27 14:20:27,329 [DEBUG] numba.core.byteflow: stack ['$4load_global.0', '$null$4.1', '$x14.2', '$t_out14.3', '$interp_win16.4', '$interp_delta16.5']
2025-08-27 14:20:27,329 [DEBUG] numba.core.byteflow: dispatch pc=20, inst=LOAD_FAST(arg=6, lineno=86)
2025-08-27 14:20:27,329 [DEBUG] numba.core.byteflow: stack ['$4load_global.0', '$null$4.1', '$x14.2', '$t_out14.3', '$interp_win16.4', '$interp_delta16.5', '$num_table18.6', '$scale18.7']
2025-08-27 14:20:27,329 [DEBUG] numba.core.byteflow: dispatch pc=22, inst=CALL(arg=7, lineno=86)
2025-08-27 14:20:27,329 [DEBUG] numba.core.byteflow: stack ['$4load_global.0', '$null$4.1', '$x14.2', '$t_out14.3', '$interp_win16.4', '$interp_delta16.5', '$num_table18.6', '$scale18.7', '$y20.8']
2025-08-27 14:20:27,329 [DEBUG] numba.core.byteflow: dispatch pc=30, inst=POP_TOP(arg=None, lineno=86)
2025-08-27 14:20:27,329 [DEBUG] numba.core.byteflow: stack ['$22call.9']
2025-08-27 14:20:27,329 [DEBUG] numba.core.byteflow: dispatch pc=32, inst=RETURN_CONST(arg=0, lineno=86)
2025-08-27 14:20:27,329 [DEBUG] numba.core.byteflow: stack []
2025-08-27 14:20:27,329 [DEBUG] numba.core.byteflow: end state. edges=[]
2025-08-27 14:20:27,329 [DEBUG] numba.core.byteflow: -------------------------Prune PHIs-------------------------
2025-08-27 14:20:27,329 [DEBUG] numba.core.byteflow: Used_phis: defaultdict(<class 'set'>, {State(pc_initial=0 nstack_initial=0): set()})
2025-08-27 14:20:27,329 [DEBUG] numba.core.byteflow: defmap: {}
2025-08-27 14:20:27,329 [DEBUG] numba.core.byteflow: phismap: defaultdict(<class 'set'>, {})
2025-08-27 14:20:27,329 [DEBUG] numba.core.byteflow: changing phismap: defaultdict(<class 'set'>, {})
2025-08-27 14:20:27,329 [DEBUG] numba.core.byteflow: keep phismap: {}
2025-08-27 14:20:27,330 [DEBUG] numba.core.byteflow: new_out: defaultdict(<class 'dict'>, {})
2025-08-27 14:20:27,330 [DEBUG] numba.core.byteflow: ----------------------DONE Prune PHIs-----------------------
2025-08-27 14:20:27,330 [DEBUG] numba.core.byteflow: block_infos State(pc_initial=0 nstack_initial=0):
AdaptBlockInfo(insts=((0, {}), (2, {}), (4, {'idx': 0, 'res': '$4load_global.0'}), (14, {'res1': '$x14.2', 'res2': '$t_out14.3'}), (16, {'res1': '$interp_win16.4', 'res2': '$interp_delta16.5'}), (18, {'res1': '$num_table18.6', 'res2': '$scale18.7'}), (20, {'res': '$y20.8'}), (22, {'func': '$4load_global.0', 'args': ['$x14.2', '$t_out14.3', '$interp_win16.4', '$interp_delta16.5', '$num_table18.6', '$scale18.7', '$y20.8'], 'kw_names': None, 'res': '$22call.9'}), (32, {'retval': '$const32.10', 'castval': '$32return_const.11'})), outgoing_phis={}, blockstack=(), active_try_block=None, outgoing_edgepushed={})
2025-08-27 14:20:27,330 [DEBUG] numba.core.interpreter: label 0:
    x = arg(0, name=x)                       ['x']
    t_out = arg(1, name=t_out)               ['t_out']
    interp_win = arg(2, name=interp_win)     ['interp_win']
    interp_delta = arg(3, name=interp_delta) ['interp_delta']
    num_table = arg(4, name=num_table)       ['num_table']
    scale = arg(5, name=scale)               ['scale']
    y = arg(6, name=y)                       ['y']
    $4load_global.0 = global(_resample_loop_s: CPUDispatcher(<function _resample_loop at 0x32e96d080>)) ['$4load_global.0']
    $22call.9 = call $4load_global.0(x, t_out, interp_win, interp_delta, num_table, scale, y, func=$4load_global.0, args=[Var(x, interpn.py:81), Var(t_out, interpn.py:81), Var(interp_win, interpn.py:81), Var(interp_delta, interpn.py:81), Var(num_table, interpn.py:81), Var(scale, interpn.py:81), Var(y, interpn.py:81)], kws=(), vararg=None, varkwarg=None, target=None) ['$22call.9', '$4load_global.0', 'interp_delta', 'interp_win', 'num_table', 'scale', 't_out', 'x', 'y']
    $const32.10 = const(NoneType, None)      ['$const32.10']
    $32return_const.11 = cast(value=$const32.10) ['$32return_const.11', '$const32.10']
    return $32return_const.11                ['$32return_const.11']

2025-08-27 14:20:27,331 [DEBUG] numba.core.ssa: ==== SSA block analysis pass on 0
2025-08-27 14:20:27,331 [DEBUG] numba.core.ssa: Running <numba.core.ssa._GatherDefsHandler object at 0x32e99c1a0>
2025-08-27 14:20:27,331 [DEBUG] numba.core.ssa: on stmt: x = arg(0, name=x)
2025-08-27 14:20:27,331 [DEBUG] numba.core.ssa: on stmt: t_out = arg(1, name=t_out)
2025-08-27 14:20:27,331 [DEBUG] numba.core.ssa: on stmt: interp_win = arg(2, name=interp_win)
2025-08-27 14:20:27,331 [DEBUG] numba.core.ssa: on stmt: interp_delta = arg(3, name=interp_delta)
2025-08-27 14:20:27,331 [DEBUG] numba.core.ssa: on stmt: num_table = arg(4, name=num_table)
2025-08-27 14:20:27,331 [DEBUG] numba.core.ssa: on stmt: scale = arg(5, name=scale)
2025-08-27 14:20:27,331 [DEBUG] numba.core.ssa: on stmt: y = arg(6, name=y)
2025-08-27 14:20:27,331 [DEBUG] numba.core.ssa: on stmt: $4load_global.0 = global(_resample_loop_s: CPUDispatcher(<function _resample_loop at 0x32e96d080>))
2025-08-27 14:20:27,331 [DEBUG] numba.core.ssa: on stmt: $22call.9 = call $4load_global.0(x, t_out, interp_win, interp_delta, num_table, scale, y, func=$4load_global.0, args=[Var(x, interpn.py:81), Var(t_out, interpn.py:81), Var(interp_win, interpn.py:81), Var(interp_delta, interpn.py:81), Var(num_table, interpn.py:81), Var(scale, interpn.py:81), Var(y, interpn.py:81)], kws=(), vararg=None, varkwarg=None, target=None)
2025-08-27 14:20:27,331 [DEBUG] numba.core.ssa: on stmt: $const32.10 = const(NoneType, None)
2025-08-27 14:20:27,331 [DEBUG] numba.core.ssa: on stmt: $32return_const.11 = cast(value=$const32.10)
2025-08-27 14:20:27,331 [DEBUG] numba.core.ssa: on stmt: return $32return_const.11
2025-08-27 14:20:27,331 [DEBUG] numba.core.ssa: defs defaultdict(<class 'list'>,
            {'$22call.9': [(<numba.core.ir.Assign object at 0x32e966b10>, 0)],
             '$32return_const.11': [(<numba.core.ir.Assign object at 0x32e966cf0>,
                                     0)],
             '$4load_global.0': [(<numba.core.ir.Assign object at 0x32e966330>,
                                  0)],
             '$const32.10': [(<numba.core.ir.Assign object at 0x32e966bd0>, 0)],
             'interp_delta': [(<numba.core.ir.Assign object at 0x32e966210>,
                               0)],
             'interp_win': [(<numba.core.ir.Assign object at 0x32e9663f0>, 0)],
             'num_table': [(<numba.core.ir.Assign object at 0x32e966270>, 0)],
             'scale': [(<numba.core.ir.Assign object at 0x32e9661b0>, 0)],
             't_out': [(<numba.core.ir.Assign object at 0x32e9664b0>, 0)],
             'x': [(<numba.core.ir.Assign object at 0x32e966570>, 0)],
             'y': [(<numba.core.ir.Assign object at 0x32e9660f0>, 0)]})
2025-08-27 14:20:27,331 [DEBUG] numba.core.ssa: SSA violators <numba.core.utils.OrderedSet object at 0x32e99c050>
2025-08-27 14:20:27,332 [DEBUG] numba.core.byteflow: bytecode dump:
>          0	NOP(arg=None, lineno=7)
           2	RESUME(arg=0, lineno=7)
           4	LOAD_GLOBAL(arg=1, lineno=9)
          14	LOAD_FAST_LOAD_FAST(arg=84, lineno=9)
          16	BINARY_OP(arg=5, lineno=9)
          20	CALL(arg=1, lineno=9)
          28	STORE_FAST(arg=7, lineno=9)
          30	LOAD_CONST(arg=1, lineno=10)
          32	STORE_FAST(arg=8, lineno=10)
          34	LOAD_CONST(arg=2, lineno=12)
          36	STORE_FAST(arg=9, lineno=12)
          38	LOAD_CONST(arg=1, lineno=13)
          40	STORE_FAST(arg=10, lineno=13)
          42	LOAD_CONST(arg=1, lineno=14)
          44	STORE_FAST(arg=11, lineno=14)
          46	LOAD_CONST(arg=2, lineno=15)
          48	STORE_FAST(arg=12, lineno=15)
          50	LOAD_CONST(arg=1, lineno=16)
          52	STORE_FAST(arg=13, lineno=16)
          54	LOAD_CONST(arg=1, lineno=17)
          56	STORE_FAST(arg=14, lineno=17)
          58	LOAD_FAST(arg=2, lineno=19)
          60	LOAD_ATTR(arg=2, lineno=19)
          80	LOAD_CONST(arg=2, lineno=19)
          82	BINARY_SUBSCR(arg=None, lineno=19)
          86	STORE_FAST(arg=15, lineno=19)
          88	LOAD_FAST(arg=0, lineno=20)
          90	LOAD_ATTR(arg=2, lineno=20)
         110	LOAD_CONST(arg=2, lineno=20)
         112	BINARY_SUBSCR(arg=None, lineno=20)
         116	STORE_FAST(arg=16, lineno=20)
         118	LOAD_FAST(arg=1, lineno=21)
         120	LOAD_ATTR(arg=2, lineno=21)
         140	LOAD_CONST(arg=2, lineno=21)
         142	BINARY_SUBSCR(arg=None, lineno=21)
         146	STORE_FAST(arg=17, lineno=21)
         148	LOAD_GLOBAL(arg=5, lineno=23)
         158	LOAD_FAST(arg=17, lineno=23)
         160	CALL(arg=1, lineno=23)
         168	GET_ITER(arg=None, lineno=23)
>        170	FOR_ITER(arg=240, lineno=23)
         174	STORE_FAST(arg=18, lineno=23)
         176	LOAD_FAST(arg=1, lineno=24)
         178	LOAD_FAST(arg=18, lineno=24)
         180	BINARY_SUBSCR(arg=None, lineno=24)
         184	STORE_FAST(arg=8, lineno=24)
         186	LOAD_GLOBAL(arg=1, lineno=27)
         196	LOAD_FAST(arg=8, lineno=27)
         198	CALL(arg=1, lineno=27)
         206	STORE_FAST(arg=9, lineno=27)
         208	LOAD_FAST_LOAD_FAST(arg=88, lineno=30)
         210	LOAD_FAST(arg=9, lineno=30)
         212	BINARY_OP(arg=10, lineno=30)
         216	BINARY_OP(arg=5, lineno=30)
         220	STORE_FAST(arg=10, lineno=30)
         222	LOAD_FAST_LOAD_FAST(arg=164, lineno=33)
         224	BINARY_OP(arg=5, lineno=33)
         228	STORE_FAST(arg=11, lineno=33)
         230	LOAD_GLOBAL(arg=1, lineno=34)
         240	LOAD_FAST(arg=11, lineno=34)
         242	CALL(arg=1, lineno=34)
         250	STORE_FAST(arg=12, lineno=34)
         252	LOAD_FAST_LOAD_FAST(arg=188, lineno=37)
         254	BINARY_OP(arg=10, lineno=37)
         258	STORE_FAST(arg=13, lineno=37)
         260	LOAD_GLOBAL(arg=7, lineno=40)
         270	LOAD_FAST(arg=9, lineno=40)
         272	LOAD_CONST(arg=3, lineno=40)
         274	BINARY_OP(arg=0, lineno=40)
         278	LOAD_FAST_LOAD_FAST(arg=252, lineno=40)
         280	BINARY_OP(arg=10, lineno=40)
         284	LOAD_FAST(arg=7, lineno=40)
         286	BINARY_OP(arg=2, lineno=40)
         290	CALL(arg=2, lineno=40)
         298	STORE_FAST(arg=19, lineno=40)
         300	LOAD_GLOBAL(arg=9, lineno=41)
         310	LOAD_FAST(arg=19, lineno=41)
         312	CALL(arg=1, lineno=41)
         320	GET_ITER(arg=None, lineno=41)
>        322	FOR_ITER(arg=48, lineno=41)
         326	STORE_FAST(arg=20, lineno=41)
         328	LOAD_FAST_LOAD_FAST(arg=44, lineno=44)
         330	LOAD_FAST(arg=20, lineno=44)
         332	LOAD_FAST(arg=7, lineno=44)
         334	BINARY_OP(arg=5, lineno=44)
         338	BINARY_OP(arg=0, lineno=44)
         342	BINARY_SUBSCR(arg=None, lineno=44)
         346	LOAD_FAST_LOAD_FAST(arg=211, lineno=45)
         348	LOAD_FAST(arg=12, lineno=45)
         350	LOAD_FAST(arg=20, lineno=45)
         352	LOAD_FAST(arg=7, lineno=45)
         354	BINARY_OP(arg=5, lineno=45)
         358	BINARY_OP(arg=0, lineno=45)
         362	BINARY_SUBSCR(arg=None, lineno=45)
         366	BINARY_OP(arg=5, lineno=45)
         370	BINARY_OP(arg=0, lineno=44)
         374	STORE_FAST(arg=14, lineno=43)
         376	LOAD_FAST(arg=6, lineno=47)
         378	LOAD_FAST(arg=18, lineno=47)
         380	COPY(arg=2, lineno=47)
         382	COPY(arg=2, lineno=47)
         384	BINARY_SUBSCR(arg=None, lineno=47)
         388	LOAD_FAST_LOAD_FAST(arg=224, lineno=47)
         390	LOAD_FAST(arg=9, lineno=47)
         392	LOAD_FAST(arg=20, lineno=47)
         394	BINARY_OP(arg=10, lineno=47)
         398	BINARY_SUBSCR(arg=None, lineno=47)
         402	BINARY_OP(arg=5, lineno=47)
         406	BINARY_OP(arg=13, lineno=47)
         410	SWAP(arg=3, lineno=47)
         412	SWAP(arg=2, lineno=47)
         414	STORE_SUBSCR(arg=None, lineno=47)
         418	JUMP_BACKWARD(arg=50, lineno=47)
>        422	END_FOR(arg=None, lineno=41)
         424	POP_TOP(arg=None, lineno=41)
         426	LOAD_FAST_LOAD_FAST(arg=90, lineno=50)
         428	BINARY_OP(arg=10, lineno=50)
         432	STORE_FAST(arg=10, lineno=50)
         434	LOAD_FAST_LOAD_FAST(arg=164, lineno=53)
         436	BINARY_OP(arg=5, lineno=53)
         440	STORE_FAST(arg=11, lineno=53)
         442	LOAD_GLOBAL(arg=1, lineno=54)
         452	LOAD_FAST(arg=11, lineno=54)
         454	CALL(arg=1, lineno=54)
         462	STORE_FAST(arg=12, lineno=54)
         464	LOAD_FAST_LOAD_FAST(arg=188, lineno=57)
         466	BINARY_OP(arg=10, lineno=57)
         470	STORE_FAST(arg=13, lineno=57)
         472	LOAD_GLOBAL(arg=7, lineno=60)
         482	LOAD_FAST(arg=16, lineno=60)
         484	LOAD_FAST(arg=9, lineno=60)
         486	BINARY_OP(arg=10, lineno=60)
         490	LOAD_CONST(arg=3, lineno=60)
         492	BINARY_OP(arg=10, lineno=60)
         496	LOAD_FAST_LOAD_FAST(arg=252, lineno=60)
         498	BINARY_OP(arg=10, lineno=60)
         502	LOAD_FAST(arg=7, lineno=60)
         504	BINARY_OP(arg=2, lineno=60)
         508	CALL(arg=2, lineno=60)
         516	STORE_FAST(arg=21, lineno=60)
         518	LOAD_GLOBAL(arg=9, lineno=61)
         528	LOAD_FAST(arg=21, lineno=61)
         530	CALL(arg=1, lineno=61)
         538	GET_ITER(arg=None, lineno=61)
>        540	FOR_ITER(arg=51, lineno=61)
         544	STORE_FAST(arg=22, lineno=61)
         546	LOAD_FAST_LOAD_FAST(arg=44, lineno=63)
         548	LOAD_FAST(arg=22, lineno=63)
         550	LOAD_FAST(arg=7, lineno=63)
         552	BINARY_OP(arg=5, lineno=63)
         556	BINARY_OP(arg=0, lineno=63)
         560	BINARY_SUBSCR(arg=None, lineno=63)
         564	LOAD_FAST_LOAD_FAST(arg=211, lineno=64)
         566	LOAD_FAST(arg=12, lineno=64)
         568	LOAD_FAST(arg=22, lineno=64)
         570	LOAD_FAST(arg=7, lineno=64)
         572	BINARY_OP(arg=5, lineno=64)
         576	BINARY_OP(arg=0, lineno=64)
         580	BINARY_SUBSCR(arg=None, lineno=64)
         584	BINARY_OP(arg=5, lineno=64)
         588	BINARY_OP(arg=0, lineno=63)
         592	STORE_FAST(arg=14, lineno=62)
         594	LOAD_FAST(arg=6, lineno=66)
         596	LOAD_FAST(arg=18, lineno=66)
         598	COPY(arg=2, lineno=66)
         600	COPY(arg=2, lineno=66)
         602	BINARY_SUBSCR(arg=None, lineno=66)
         606	LOAD_FAST_LOAD_FAST(arg=224, lineno=66)
         608	LOAD_FAST(arg=9, lineno=66)
         610	LOAD_FAST(arg=22, lineno=66)
         612	BINARY_OP(arg=0, lineno=66)
         616	LOAD_CONST(arg=3, lineno=66)
         618	BINARY_OP(arg=0, lineno=66)
         622	BINARY_SUBSCR(arg=None, lineno=66)
         626	BINARY_OP(arg=5, lineno=66)
         630	BINARY_OP(arg=13, lineno=66)
         634	SWAP(arg=3, lineno=66)
         636	SWAP(arg=2, lineno=66)
         638	STORE_SUBSCR(arg=None, lineno=66)
         642	JUMP_BACKWARD(arg=53, lineno=66)
>        646	END_FOR(arg=None, lineno=61)
         648	POP_TOP(arg=None, lineno=61)
         650	JUMP_BACKWARD(arg=242, lineno=61)
>        654	END_FOR(arg=None, lineno=23)
         656	POP_TOP(arg=None, lineno=23)
         658	RETURN_CONST(arg=0, lineno=23)
2025-08-27 14:20:27,332 [DEBUG] numba.core.byteflow: pending: deque([State(pc_initial=0 nstack_initial=0)])
2025-08-27 14:20:27,332 [DEBUG] numba.core.byteflow: stack: []
2025-08-27 14:20:27,332 [DEBUG] numba.core.byteflow: state.pc_initial: State(pc_initial=0 nstack_initial=0)
2025-08-27 14:20:27,332 [DEBUG] numba.core.byteflow: dispatch pc=0, inst=NOP(arg=None, lineno=7)
2025-08-27 14:20:27,332 [DEBUG] numba.core.byteflow: stack []
2025-08-27 14:20:27,332 [DEBUG] numba.core.byteflow: dispatch pc=2, inst=RESUME(arg=0, lineno=7)
2025-08-27 14:20:27,333 [DEBUG] numba.core.byteflow: stack []
2025-08-27 14:20:27,333 [DEBUG] numba.core.byteflow: dispatch pc=4, inst=LOAD_GLOBAL(arg=1, lineno=9)
2025-08-27 14:20:27,333 [DEBUG] numba.core.byteflow: stack []
2025-08-27 14:20:27,333 [DEBUG] numba.core.byteflow: dispatch pc=14, inst=LOAD_FAST_LOAD_FAST(arg=84, lineno=9)
2025-08-27 14:20:27,333 [DEBUG] numba.core.byteflow: stack ['$4load_global.0', '$null$4.1']
2025-08-27 14:20:27,333 [DEBUG] numba.core.byteflow: dispatch pc=16, inst=BINARY_OP(arg=5, lineno=9)
2025-08-27 14:20:27,333 [DEBUG] numba.core.byteflow: stack ['$4load_global.0', '$null$4.1', '$scale14.2', '$num_table14.3']
2025-08-27 14:20:27,333 [DEBUG] numba.core.byteflow: dispatch pc=20, inst=CALL(arg=1, lineno=9)
2025-08-27 14:20:27,333 [DEBUG] numba.core.byteflow: stack ['$4load_global.0', '$null$4.1', '$binop_mul16.4']
2025-08-27 14:20:27,333 [DEBUG] numba.core.byteflow: dispatch pc=28, inst=STORE_FAST(arg=7, lineno=9)
2025-08-27 14:20:27,333 [DEBUG] numba.core.byteflow: stack ['$20call.5']
2025-08-27 14:20:27,333 [DEBUG] numba.core.byteflow: dispatch pc=30, inst=LOAD_CONST(arg=1, lineno=10)
2025-08-27 14:20:27,333 [DEBUG] numba.core.byteflow: stack []
2025-08-27 14:20:27,333 [DEBUG] numba.core.byteflow: dispatch pc=32, inst=STORE_FAST(arg=8, lineno=10)
2025-08-27 14:20:27,333 [DEBUG] numba.core.byteflow: stack ['$const30.6.1']
2025-08-27 14:20:27,333 [DEBUG] numba.core.byteflow: dispatch pc=34, inst=LOAD_CONST(arg=2, lineno=12)
2025-08-27 14:20:27,333 [DEBUG] numba.core.byteflow: stack []
2025-08-27 14:20:27,333 [DEBUG] numba.core.byteflow: dispatch pc=36, inst=STORE_FAST(arg=9, lineno=12)
2025-08-27 14:20:27,333 [DEBUG] numba.core.byteflow: stack ['$const34.7.2']
2025-08-27 14:20:27,333 [DEBUG] numba.core.byteflow: dispatch pc=38, inst=LOAD_CONST(arg=1, lineno=13)
2025-08-27 14:20:27,333 [DEBUG] numba.core.byteflow: stack []
2025-08-27 14:20:27,333 [DEBUG] numba.core.byteflow: dispatch pc=40, inst=STORE_FAST(arg=10, lineno=13)
2025-08-27 14:20:27,333 [DEBUG] numba.core.byteflow: stack ['$const38.8.1']
2025-08-27 14:20:27,333 [DEBUG] numba.core.byteflow: dispatch pc=42, inst=LOAD_CONST(arg=1, lineno=14)
2025-08-27 14:20:27,333 [DEBUG] numba.core.byteflow: stack []
2025-08-27 14:20:27,333 [DEBUG] numba.core.byteflow: dispatch pc=44, inst=STORE_FAST(arg=11, lineno=14)
2025-08-27 14:20:27,333 [DEBUG] numba.core.byteflow: stack ['$const42.9.1']
2025-08-27 14:20:27,333 [DEBUG] numba.core.byteflow: dispatch pc=46, inst=LOAD_CONST(arg=2, lineno=15)
2025-08-27 14:20:27,333 [DEBUG] numba.core.byteflow: stack []
2025-08-27 14:20:27,333 [DEBUG] numba.core.byteflow: dispatch pc=48, inst=STORE_FAST(arg=12, lineno=15)
2025-08-27 14:20:27,333 [DEBUG] numba.core.byteflow: stack ['$const46.10.2']
2025-08-27 14:20:27,333 [DEBUG] numba.core.byteflow: dispatch pc=50, inst=LOAD_CONST(arg=1, lineno=16)
2025-08-27 14:20:27,333 [DEBUG] numba.core.byteflow: stack []
2025-08-27 14:20:27,333 [DEBUG] numba.core.byteflow: dispatch pc=52, inst=STORE_FAST(arg=13, lineno=16)
2025-08-27 14:20:27,333 [DEBUG] numba.core.byteflow: stack ['$const50.11.1']
2025-08-27 14:20:27,333 [DEBUG] numba.core.byteflow: dispatch pc=54, inst=LOAD_CONST(arg=1, lineno=17)
2025-08-27 14:20:27,333 [DEBUG] numba.core.byteflow: stack []
2025-08-27 14:20:27,333 [DEBUG] numba.core.byteflow: dispatch pc=56, inst=STORE_FAST(arg=14, lineno=17)
2025-08-27 14:20:27,333 [DEBUG] numba.core.byteflow: stack ['$const54.12.1']
2025-08-27 14:20:27,333 [DEBUG] numba.core.byteflow: dispatch pc=58, inst=LOAD_FAST(arg=2, lineno=19)
2025-08-27 14:20:27,333 [DEBUG] numba.core.byteflow: stack []
2025-08-27 14:20:27,333 [DEBUG] numba.core.byteflow: dispatch pc=60, inst=LOAD_ATTR(arg=2, lineno=19)
2025-08-27 14:20:27,333 [DEBUG] numba.core.byteflow: stack ['$interp_win58.13']
2025-08-27 14:20:27,333 [DEBUG] numba.core.byteflow: dispatch pc=80, inst=LOAD_CONST(arg=2, lineno=19)
2025-08-27 14:20:27,333 [DEBUG] numba.core.byteflow: stack ['$60load_attr.14']
2025-08-27 14:20:27,333 [DEBUG] numba.core.byteflow: dispatch pc=82, inst=BINARY_SUBSCR(arg=None, lineno=19)
2025-08-27 14:20:27,333 [DEBUG] numba.core.byteflow: stack ['$60load_attr.14', '$const80.15.2']
2025-08-27 14:20:27,333 [DEBUG] numba.core.byteflow: dispatch pc=86, inst=STORE_FAST(arg=15, lineno=19)
2025-08-27 14:20:27,333 [DEBUG] numba.core.byteflow: stack ['$82binary_subscr.16']
2025-08-27 14:20:27,333 [DEBUG] numba.core.byteflow: dispatch pc=88, inst=LOAD_FAST(arg=0, lineno=20)
2025-08-27 14:20:27,333 [DEBUG] numba.core.byteflow: stack []
2025-08-27 14:20:27,333 [DEBUG] numba.core.byteflow: dispatch pc=90, inst=LOAD_ATTR(arg=2, lineno=20)
2025-08-27 14:20:27,333 [DEBUG] numba.core.byteflow: stack ['$x88.17']
2025-08-27 14:20:27,333 [DEBUG] numba.core.byteflow: dispatch pc=110, inst=LOAD_CONST(arg=2, lineno=20)
2025-08-27 14:20:27,333 [DEBUG] numba.core.byteflow: stack ['$90load_attr.18']
2025-08-27 14:20:27,333 [DEBUG] numba.core.byteflow: dispatch pc=112, inst=BINARY_SUBSCR(arg=None, lineno=20)
2025-08-27 14:20:27,333 [DEBUG] numba.core.byteflow: stack ['$90load_attr.18', '$const110.19.2']
2025-08-27 14:20:27,333 [DEBUG] numba.core.byteflow: dispatch pc=116, inst=STORE_FAST(arg=16, lineno=20)
2025-08-27 14:20:27,333 [DEBUG] numba.core.byteflow: stack ['$112binary_subscr.20']
2025-08-27 14:20:27,333 [DEBUG] numba.core.byteflow: dispatch pc=118, inst=LOAD_FAST(arg=1, lineno=21)
2025-08-27 14:20:27,333 [DEBUG] numba.core.byteflow: stack []
2025-08-27 14:20:27,333 [DEBUG] numba.core.byteflow: dispatch pc=120, inst=LOAD_ATTR(arg=2, lineno=21)
2025-08-27 14:20:27,333 [DEBUG] numba.core.byteflow: stack ['$t_out118.21']
2025-08-27 14:20:27,333 [DEBUG] numba.core.byteflow: dispatch pc=140, inst=LOAD_CONST(arg=2, lineno=21)
2025-08-27 14:20:27,333 [DEBUG] numba.core.byteflow: stack ['$120load_attr.22']
2025-08-27 14:20:27,333 [DEBUG] numba.core.byteflow: dispatch pc=142, inst=BINARY_SUBSCR(arg=None, lineno=21)
2025-08-27 14:20:27,333 [DEBUG] numba.core.byteflow: stack ['$120load_attr.22', '$const140.23.2']
2025-08-27 14:20:27,333 [DEBUG] numba.core.byteflow: dispatch pc=146, inst=STORE_FAST(arg=17, lineno=21)
2025-08-27 14:20:27,333 [DEBUG] numba.core.byteflow: stack ['$142binary_subscr.24']
2025-08-27 14:20:27,334 [DEBUG] numba.core.byteflow: dispatch pc=148, inst=LOAD_GLOBAL(arg=5, lineno=23)
2025-08-27 14:20:27,334 [DEBUG] numba.core.byteflow: stack []
2025-08-27 14:20:27,334 [DEBUG] numba.core.byteflow: dispatch pc=158, inst=LOAD_FAST(arg=17, lineno=23)
2025-08-27 14:20:27,334 [DEBUG] numba.core.byteflow: stack ['$148load_global.25', '$null$148.26']
2025-08-27 14:20:27,334 [DEBUG] numba.core.byteflow: dispatch pc=160, inst=CALL(arg=1, lineno=23)
2025-08-27 14:20:27,334 [DEBUG] numba.core.byteflow: stack ['$148load_global.25', '$null$148.26', '$n_out158.27']
2025-08-27 14:20:27,334 [DEBUG] numba.core.byteflow: dispatch pc=168, inst=GET_ITER(arg=None, lineno=23)
2025-08-27 14:20:27,334 [DEBUG] numba.core.byteflow: stack ['$160call.28']
2025-08-27 14:20:27,334 [DEBUG] numba.core.byteflow: end state. edges=[Edge(pc=170, stack=('$168get_iter.29',), blockstack=(), npush=0)]
2025-08-27 14:20:27,334 [DEBUG] numba.core.byteflow: pending: deque([State(pc_initial=170 nstack_initial=1)])
2025-08-27 14:20:27,334 [DEBUG] numba.core.byteflow: stack: ['$phi170.0']
2025-08-27 14:20:27,334 [DEBUG] numba.core.byteflow: state.pc_initial: State(pc_initial=170 nstack_initial=1)
2025-08-27 14:20:27,334 [DEBUG] numba.core.byteflow: dispatch pc=170, inst=FOR_ITER(arg=240, lineno=23)
2025-08-27 14:20:27,334 [DEBUG] numba.core.byteflow: stack ['$phi170.0']
2025-08-27 14:20:27,334 [DEBUG] numba.core.byteflow: end state. edges=[Edge(pc=654, stack=('$phi170.0', '$170for_iter.2'), blockstack=(), npush=0), Edge(pc=174, stack=('$phi170.0', '$170for_iter.2'), blockstack=(), npush=0)]
2025-08-27 14:20:27,334 [DEBUG] numba.core.byteflow: pending: deque([State(pc_initial=654 nstack_initial=2), State(pc_initial=174 nstack_initial=2)])
2025-08-27 14:20:27,334 [DEBUG] numba.core.byteflow: stack: ['$phi654.0', '$phi654.1']
2025-08-27 14:20:27,334 [DEBUG] numba.core.byteflow: state.pc_initial: State(pc_initial=654 nstack_initial=2)
2025-08-27 14:20:27,334 [DEBUG] numba.core.byteflow: dispatch pc=654, inst=END_FOR(arg=None, lineno=23)
2025-08-27 14:20:27,334 [DEBUG] numba.core.byteflow: stack ['$phi654.0', '$phi654.1']
2025-08-27 14:20:27,334 [DEBUG] numba.core.byteflow: dispatch pc=656, inst=POP_TOP(arg=None, lineno=23)
2025-08-27 14:20:27,334 [DEBUG] numba.core.byteflow: stack ['$phi654.0']
2025-08-27 14:20:27,334 [DEBUG] numba.core.byteflow: dispatch pc=658, inst=RETURN_CONST(arg=0, lineno=23)
2025-08-27 14:20:27,334 [DEBUG] numba.core.byteflow: stack []
2025-08-27 14:20:27,334 [DEBUG] numba.core.byteflow: end state. edges=[]
2025-08-27 14:20:27,334 [DEBUG] numba.core.byteflow: pending: deque([State(pc_initial=174 nstack_initial=2)])
2025-08-27 14:20:27,334 [DEBUG] numba.core.byteflow: stack: ['$phi174.0', '$phi174.1']
2025-08-27 14:20:27,334 [DEBUG] numba.core.byteflow: state.pc_initial: State(pc_initial=174 nstack_initial=2)
2025-08-27 14:20:27,334 [DEBUG] numba.core.byteflow: dispatch pc=174, inst=STORE_FAST(arg=18, lineno=23)
2025-08-27 14:20:27,334 [DEBUG] numba.core.byteflow: stack ['$phi174.0', '$phi174.1']
2025-08-27 14:20:27,334 [DEBUG] numba.core.byteflow: dispatch pc=176, inst=LOAD_FAST(arg=1, lineno=24)
2025-08-27 14:20:27,334 [DEBUG] numba.core.byteflow: stack ['$phi174.0']
2025-08-27 14:20:27,334 [DEBUG] numba.core.byteflow: dispatch pc=178, inst=LOAD_FAST(arg=18, lineno=24)
2025-08-27 14:20:27,334 [DEBUG] numba.core.byteflow: stack ['$phi174.0', '$t_out176.2']
2025-08-27 14:20:27,334 [DEBUG] numba.core.byteflow: dispatch pc=180, inst=BINARY_SUBSCR(arg=None, lineno=24)
2025-08-27 14:20:27,334 [DEBUG] numba.core.byteflow: stack ['$phi174.0', '$t_out176.2', '$t178.3']
2025-08-27 14:20:27,334 [DEBUG] numba.core.byteflow: dispatch pc=184, inst=STORE_FAST(arg=8, lineno=24)
2025-08-27 14:20:27,334 [DEBUG] numba.core.byteflow: stack ['$phi174.0', '$180binary_subscr.4']
2025-08-27 14:20:27,334 [DEBUG] numba.core.byteflow: dispatch pc=186, inst=LOAD_GLOBAL(arg=1, lineno=27)
2025-08-27 14:20:27,334 [DEBUG] numba.core.byteflow: stack ['$phi174.0']
2025-08-27 14:20:27,334 [DEBUG] numba.core.byteflow: dispatch pc=196, inst=LOAD_FAST(arg=8, lineno=27)
2025-08-27 14:20:27,334 [DEBUG] numba.core.byteflow: stack ['$phi174.0', '$186load_global.5', '$null$186.6']
2025-08-27 14:20:27,334 [DEBUG] numba.core.byteflow: dispatch pc=198, inst=CALL(arg=1, lineno=27)
2025-08-27 14:20:27,334 [DEBUG] numba.core.byteflow: stack ['$phi174.0', '$186load_global.5', '$null$186.6', '$time_register196.7']
2025-08-27 14:20:27,334 [DEBUG] numba.core.byteflow: dispatch pc=206, inst=STORE_FAST(arg=9, lineno=27)
2025-08-27 14:20:27,334 [DEBUG] numba.core.byteflow: stack ['$phi174.0', '$198call.8']
2025-08-27 14:20:27,334 [DEBUG] numba.core.byteflow: dispatch pc=208, inst=LOAD_FAST_LOAD_FAST(arg=88, lineno=30)
2025-08-27 14:20:27,334 [DEBUG] numba.core.byteflow: stack ['$phi174.0']
2025-08-27 14:20:27,334 [DEBUG] numba.core.byteflow: dispatch pc=210, inst=LOAD_FAST(arg=9, lineno=30)
2025-08-27 14:20:27,334 [DEBUG] numba.core.byteflow: stack ['$phi174.0', '$scale208.9', '$time_register208.10']
2025-08-27 14:20:27,334 [DEBUG] numba.core.byteflow: dispatch pc=212, inst=BINARY_OP(arg=10, lineno=30)
2025-08-27 14:20:27,334 [DEBUG] numba.core.byteflow: stack ['$phi174.0', '$scale208.9', '$time_register208.10', '$n210.11']
2025-08-27 14:20:27,334 [DEBUG] numba.core.byteflow: dispatch pc=216, inst=BINARY_OP(arg=5, lineno=30)
2025-08-27 14:20:27,334 [DEBUG] numba.core.byteflow: stack ['$phi174.0', '$scale208.9', '$binop_sub212.12']
2025-08-27 14:20:27,334 [DEBUG] numba.core.byteflow: dispatch pc=220, inst=STORE_FAST(arg=10, lineno=30)
2025-08-27 14:20:27,334 [DEBUG] numba.core.byteflow: stack ['$phi174.0', '$binop_mul216.13']
2025-08-27 14:20:27,334 [DEBUG] numba.core.byteflow: dispatch pc=222, inst=LOAD_FAST_LOAD_FAST(arg=164, lineno=33)
2025-08-27 14:20:27,334 [DEBUG] numba.core.byteflow: stack ['$phi174.0']
2025-08-27 14:20:27,334 [DEBUG] numba.core.byteflow: dispatch pc=224, inst=BINARY_OP(arg=5, lineno=33)
2025-08-27 14:20:27,334 [DEBUG] numba.core.byteflow: stack ['$phi174.0', '$frac222.14', '$num_table222.15']
2025-08-27 14:20:27,334 [DEBUG] numba.core.byteflow: dispatch pc=228, inst=STORE_FAST(arg=11, lineno=33)
2025-08-27 14:20:27,334 [DEBUG] numba.core.byteflow: stack ['$phi174.0', '$binop_mul224.16']
2025-08-27 14:20:27,334 [DEBUG] numba.core.byteflow: dispatch pc=230, inst=LOAD_GLOBAL(arg=1, lineno=34)
2025-08-27 14:20:27,334 [DEBUG] numba.core.byteflow: stack ['$phi174.0']
2025-08-27 14:20:27,334 [DEBUG] numba.core.byteflow: dispatch pc=240, inst=LOAD_FAST(arg=11, lineno=34)
2025-08-27 14:20:27,334 [DEBUG] numba.core.byteflow: stack ['$phi174.0', '$230load_global.17', '$null$230.18']
2025-08-27 14:20:27,334 [DEBUG] numba.core.byteflow: dispatch pc=242, inst=CALL(arg=1, lineno=34)
2025-08-27 14:20:27,334 [DEBUG] numba.core.byteflow: stack ['$phi174.0', '$230load_global.17', '$null$230.18', '$index_frac240.19']
2025-08-27 14:20:27,334 [DEBUG] numba.core.byteflow: dispatch pc=250, inst=STORE_FAST(arg=12, lineno=34)
2025-08-27 14:20:27,335 [DEBUG] numba.core.byteflow: stack ['$phi174.0', '$242call.20']
2025-08-27 14:20:27,335 [DEBUG] numba.core.byteflow: dispatch pc=252, inst=LOAD_FAST_LOAD_FAST(arg=188, lineno=37)
2025-08-27 14:20:27,335 [DEBUG] numba.core.byteflow: stack ['$phi174.0']
2025-08-27 14:20:27,335 [DEBUG] numba.core.byteflow: dispatch pc=254, inst=BINARY_OP(arg=10, lineno=37)
2025-08-27 14:20:27,335 [DEBUG] numba.core.byteflow: stack ['$phi174.0', '$index_frac252.21', '$offset252.22']
2025-08-27 14:20:27,335 [DEBUG] numba.core.byteflow: dispatch pc=258, inst=STORE_FAST(arg=13, lineno=37)
2025-08-27 14:20:27,335 [DEBUG] numba.core.byteflow: stack ['$phi174.0', '$binop_sub254.23']
2025-08-27 14:20:27,335 [DEBUG] numba.core.byteflow: dispatch pc=260, inst=LOAD_GLOBAL(arg=7, lineno=40)
2025-08-27 14:20:27,335 [DEBUG] numba.core.byteflow: stack ['$phi174.0']
2025-08-27 14:20:27,335 [DEBUG] numba.core.byteflow: dispatch pc=270, inst=LOAD_FAST(arg=9, lineno=40)
2025-08-27 14:20:27,335 [DEBUG] numba.core.byteflow: stack ['$phi174.0', '$260load_global.24', '$null$260.25']
2025-08-27 14:20:27,335 [DEBUG] numba.core.byteflow: dispatch pc=272, inst=LOAD_CONST(arg=3, lineno=40)
2025-08-27 14:20:27,335 [DEBUG] numba.core.byteflow: stack ['$phi174.0', '$260load_global.24', '$null$260.25', '$n270.26']
2025-08-27 14:20:27,335 [DEBUG] numba.core.byteflow: dispatch pc=274, inst=BINARY_OP(arg=0, lineno=40)
2025-08-27 14:20:27,335 [DEBUG] numba.core.byteflow: stack ['$phi174.0', '$260load_global.24', '$null$260.25', '$n270.26', '$const272.27.3']
2025-08-27 14:20:27,335 [DEBUG] numba.core.byteflow: dispatch pc=278, inst=LOAD_FAST_LOAD_FAST(arg=252, lineno=40)
2025-08-27 14:20:27,335 [DEBUG] numba.core.byteflow: stack ['$phi174.0', '$260load_global.24', '$null$260.25', '$binop_add274.28']
2025-08-27 14:20:27,335 [DEBUG] numba.core.byteflow: dispatch pc=280, inst=BINARY_OP(arg=10, lineno=40)
2025-08-27 14:20:27,335 [DEBUG] numba.core.byteflow: stack ['$phi174.0', '$260load_global.24', '$null$260.25', '$binop_add274.28', '$nwin278.29', '$offset278.30']
2025-08-27 14:20:27,335 [DEBUG] numba.core.byteflow: dispatch pc=284, inst=LOAD_FAST(arg=7, lineno=40)
2025-08-27 14:20:27,335 [DEBUG] numba.core.byteflow: stack ['$phi174.0', '$260load_global.24', '$null$260.25', '$binop_add274.28', '$binop_sub280.31']
2025-08-27 14:20:27,335 [DEBUG] numba.core.byteflow: dispatch pc=286, inst=BINARY_OP(arg=2, lineno=40)
2025-08-27 14:20:27,335 [DEBUG] numba.core.byteflow: stack ['$phi174.0', '$260load_global.24', '$null$260.25', '$binop_add274.28', '$binop_sub280.31', '$index_step284.32']
2025-08-27 14:20:27,335 [DEBUG] numba.core.byteflow: dispatch pc=290, inst=CALL(arg=2, lineno=40)
2025-08-27 14:20:27,335 [DEBUG] numba.core.byteflow: stack ['$phi174.0', '$260load_global.24', '$null$260.25', '$binop_add274.28', '$binop_floordiv286.33']
2025-08-27 14:20:27,335 [DEBUG] numba.core.byteflow: dispatch pc=298, inst=STORE_FAST(arg=19, lineno=40)
2025-08-27 14:20:27,335 [DEBUG] numba.core.byteflow: stack ['$phi174.0', '$290call.34']
2025-08-27 14:20:27,335 [DEBUG] numba.core.byteflow: dispatch pc=300, inst=LOAD_GLOBAL(arg=9, lineno=41)
2025-08-27 14:20:27,335 [DEBUG] numba.core.byteflow: stack ['$phi174.0']
2025-08-27 14:20:27,335 [DEBUG] numba.core.byteflow: dispatch pc=310, inst=LOAD_FAST(arg=19, lineno=41)
2025-08-27 14:20:27,335 [DEBUG] numba.core.byteflow: stack ['$phi174.0', '$300load_global.35', '$null$300.36']
2025-08-27 14:20:27,335 [DEBUG] numba.core.byteflow: dispatch pc=312, inst=CALL(arg=1, lineno=41)
2025-08-27 14:20:27,335 [DEBUG] numba.core.byteflow: stack ['$phi174.0', '$300load_global.35', '$null$300.36', '$i_max310.37']
2025-08-27 14:20:27,335 [DEBUG] numba.core.byteflow: dispatch pc=320, inst=GET_ITER(arg=None, lineno=41)
2025-08-27 14:20:27,335 [DEBUG] numba.core.byteflow: stack ['$phi174.0', '$312call.38']
2025-08-27 14:20:27,335 [DEBUG] numba.core.byteflow: end state. edges=[Edge(pc=322, stack=('$phi174.0', '$320get_iter.39'), blockstack=(), npush=0)]
2025-08-27 14:20:27,335 [DEBUG] numba.core.byteflow: pending: deque([State(pc_initial=322 nstack_initial=2)])
2025-08-27 14:20:27,335 [DEBUG] numba.core.byteflow: stack: ['$phi322.0', '$phi322.1']
2025-08-27 14:20:27,335 [DEBUG] numba.core.byteflow: state.pc_initial: State(pc_initial=322 nstack_initial=2)
2025-08-27 14:20:27,335 [DEBUG] numba.core.byteflow: dispatch pc=322, inst=FOR_ITER(arg=48, lineno=41)
2025-08-27 14:20:27,335 [DEBUG] numba.core.byteflow: stack ['$phi322.0', '$phi322.1']
2025-08-27 14:20:27,335 [DEBUG] numba.core.byteflow: end state. edges=[Edge(pc=422, stack=('$phi322.0', '$phi322.1', '$322for_iter.3'), blockstack=(), npush=0), Edge(pc=326, stack=('$phi322.0', '$phi322.1', '$322for_iter.3'), blockstack=(), npush=0)]
2025-08-27 14:20:27,335 [DEBUG] numba.core.byteflow: pending: deque([State(pc_initial=422 nstack_initial=3), State(pc_initial=326 nstack_initial=3)])
2025-08-27 14:20:27,335 [DEBUG] numba.core.byteflow: stack: ['$phi422.0', '$phi422.1', '$phi422.2']
2025-08-27 14:20:27,335 [DEBUG] numba.core.byteflow: state.pc_initial: State(pc_initial=422 nstack_initial=3)
2025-08-27 14:20:27,335 [DEBUG] numba.core.byteflow: dispatch pc=422, inst=END_FOR(arg=None, lineno=41)
2025-08-27 14:20:27,335 [DEBUG] numba.core.byteflow: stack ['$phi422.0', '$phi422.1', '$phi422.2']
2025-08-27 14:20:27,335 [DEBUG] numba.core.byteflow: dispatch pc=424, inst=POP_TOP(arg=None, lineno=41)
2025-08-27 14:20:27,335 [DEBUG] numba.core.byteflow: stack ['$phi422.0', '$phi422.1']
2025-08-27 14:20:27,335 [DEBUG] numba.core.byteflow: dispatch pc=426, inst=LOAD_FAST_LOAD_FAST(arg=90, lineno=50)
2025-08-27 14:20:27,335 [DEBUG] numba.core.byteflow: stack ['$phi422.0']
2025-08-27 14:20:27,335 [DEBUG] numba.core.byteflow: dispatch pc=428, inst=BINARY_OP(arg=10, lineno=50)
2025-08-27 14:20:27,335 [DEBUG] numba.core.byteflow: stack ['$phi422.0', '$scale426.3', '$frac426.4']
2025-08-27 14:20:27,335 [DEBUG] numba.core.byteflow: dispatch pc=432, inst=STORE_FAST(arg=10, lineno=50)
2025-08-27 14:20:27,335 [DEBUG] numba.core.byteflow: stack ['$phi422.0', '$binop_sub428.5']
2025-08-27 14:20:27,335 [DEBUG] numba.core.byteflow: dispatch pc=434, inst=LOAD_FAST_LOAD_FAST(arg=164, lineno=53)
2025-08-27 14:20:27,335 [DEBUG] numba.core.byteflow: stack ['$phi422.0']
2025-08-27 14:20:27,335 [DEBUG] numba.core.byteflow: dispatch pc=436, inst=BINARY_OP(arg=5, lineno=53)
2025-08-27 14:20:27,335 [DEBUG] numba.core.byteflow: stack ['$phi422.0', '$frac434.6', '$num_table434.7']
2025-08-27 14:20:27,335 [DEBUG] numba.core.byteflow: dispatch pc=440, inst=STORE_FAST(arg=11, lineno=53)
2025-08-27 14:20:27,335 [DEBUG] numba.core.byteflow: stack ['$phi422.0', '$binop_mul436.8']
2025-08-27 14:20:27,335 [DEBUG] numba.core.byteflow: dispatch pc=442, inst=LOAD_GLOBAL(arg=1, lineno=54)
2025-08-27 14:20:27,335 [DEBUG] numba.core.byteflow: stack ['$phi422.0']
2025-08-27 14:20:27,335 [DEBUG] numba.core.byteflow: dispatch pc=452, inst=LOAD_FAST(arg=11, lineno=54)
2025-08-27 14:20:27,335 [DEBUG] numba.core.byteflow: stack ['$phi422.0', '$442load_global.9', '$null$442.10']
2025-08-27 14:20:27,335 [DEBUG] numba.core.byteflow: dispatch pc=454, inst=CALL(arg=1, lineno=54)
2025-08-27 14:20:27,335 [DEBUG] numba.core.byteflow: stack ['$phi422.0', '$442load_global.9', '$null$442.10', '$index_frac452.11']
2025-08-27 14:20:27,335 [DEBUG] numba.core.byteflow: dispatch pc=462, inst=STORE_FAST(arg=12, lineno=54)
2025-08-27 14:20:27,335 [DEBUG] numba.core.byteflow: stack ['$phi422.0', '$454call.12']
2025-08-27 14:20:27,336 [DEBUG] numba.core.byteflow: dispatch pc=464, inst=LOAD_FAST_LOAD_FAST(arg=188, lineno=57)
2025-08-27 14:20:27,336 [DEBUG] numba.core.byteflow: stack ['$phi422.0']
2025-08-27 14:20:27,336 [DEBUG] numba.core.byteflow: dispatch pc=466, inst=BINARY_OP(arg=10, lineno=57)
2025-08-27 14:20:27,336 [DEBUG] numba.core.byteflow: stack ['$phi422.0', '$index_frac464.13', '$offset464.14']
2025-08-27 14:20:27,336 [DEBUG] numba.core.byteflow: dispatch pc=470, inst=STORE_FAST(arg=13, lineno=57)
2025-08-27 14:20:27,336 [DEBUG] numba.core.byteflow: stack ['$phi422.0', '$binop_sub466.15']
2025-08-27 14:20:27,336 [DEBUG] numba.core.byteflow: dispatch pc=472, inst=LOAD_GLOBAL(arg=7, lineno=60)
2025-08-27 14:20:27,336 [DEBUG] numba.core.byteflow: stack ['$phi422.0']
2025-08-27 14:20:27,336 [DEBUG] numba.core.byteflow: dispatch pc=482, inst=LOAD_FAST(arg=16, lineno=60)
2025-08-27 14:20:27,336 [DEBUG] numba.core.byteflow: stack ['$phi422.0', '$472load_global.16', '$null$472.17']
2025-08-27 14:20:27,336 [DEBUG] numba.core.byteflow: dispatch pc=484, inst=LOAD_FAST(arg=9, lineno=60)
2025-08-27 14:20:27,336 [DEBUG] numba.core.byteflow: stack ['$phi422.0', '$472load_global.16', '$null$472.17', '$n_orig482.18']
2025-08-27 14:20:27,336 [DEBUG] numba.core.byteflow: dispatch pc=486, inst=BINARY_OP(arg=10, lineno=60)
2025-08-27 14:20:27,336 [DEBUG] numba.core.byteflow: stack ['$phi422.0', '$472load_global.16', '$null$472.17', '$n_orig482.18', '$n484.19']
2025-08-27 14:20:27,336 [DEBUG] numba.core.byteflow: dispatch pc=490, inst=LOAD_CONST(arg=3, lineno=60)
2025-08-27 14:20:27,336 [DEBUG] numba.core.byteflow: stack ['$phi422.0', '$472load_global.16', '$null$472.17', '$binop_sub486.20']
2025-08-27 14:20:27,336 [DEBUG] numba.core.byteflow: dispatch pc=492, inst=BINARY_OP(arg=10, lineno=60)
2025-08-27 14:20:27,336 [DEBUG] numba.core.byteflow: stack ['$phi422.0', '$472load_global.16', '$null$472.17', '$binop_sub486.20', '$const490.21.3']
2025-08-27 14:20:27,336 [DEBUG] numba.core.byteflow: dispatch pc=496, inst=LOAD_FAST_LOAD_FAST(arg=252, lineno=60)
2025-08-27 14:20:27,336 [DEBUG] numba.core.byteflow: stack ['$phi422.0', '$472load_global.16', '$null$472.17', '$binop_sub492.22']
2025-08-27 14:20:27,336 [DEBUG] numba.core.byteflow: dispatch pc=498, inst=BINARY_OP(arg=10, lineno=60)
2025-08-27 14:20:27,336 [DEBUG] numba.core.byteflow: stack ['$phi422.0', '$472load_global.16', '$null$472.17', '$binop_sub492.22', '$nwin496.23', '$offset496.24']
2025-08-27 14:20:27,336 [DEBUG] numba.core.byteflow: dispatch pc=502, inst=LOAD_FAST(arg=7, lineno=60)
2025-08-27 14:20:27,336 [DEBUG] numba.core.byteflow: stack ['$phi422.0', '$472load_global.16', '$null$472.17', '$binop_sub492.22', '$binop_sub498.25']
2025-08-27 14:20:27,336 [DEBUG] numba.core.byteflow: dispatch pc=504, inst=BINARY_OP(arg=2, lineno=60)
2025-08-27 14:20:27,336 [DEBUG] numba.core.byteflow: stack ['$phi422.0', '$472load_global.16', '$null$472.17', '$binop_sub492.22', '$binop_sub498.25', '$index_step502.26']
2025-08-27 14:20:27,336 [DEBUG] numba.core.byteflow: dispatch pc=508, inst=CALL(arg=2, lineno=60)
2025-08-27 14:20:27,336 [DEBUG] numba.core.byteflow: stack ['$phi422.0', '$472load_global.16', '$null$472.17', '$binop_sub492.22', '$binop_floordiv504.27']
2025-08-27 14:20:27,336 [DEBUG] numba.core.byteflow: dispatch pc=516, inst=STORE_FAST(arg=21, lineno=60)
2025-08-27 14:20:27,336 [DEBUG] numba.core.byteflow: stack ['$phi422.0', '$508call.28']
2025-08-27 14:20:27,336 [DEBUG] numba.core.byteflow: dispatch pc=518, inst=LOAD_GLOBAL(arg=9, lineno=61)
2025-08-27 14:20:27,336 [DEBUG] numba.core.byteflow: stack ['$phi422.0']
2025-08-27 14:20:27,336 [DEBUG] numba.core.byteflow: dispatch pc=528, inst=LOAD_FAST(arg=21, lineno=61)
2025-08-27 14:20:27,336 [DEBUG] numba.core.byteflow: stack ['$phi422.0', '$518load_global.29', '$null$518.30']
2025-08-27 14:20:27,336 [DEBUG] numba.core.byteflow: dispatch pc=530, inst=CALL(arg=1, lineno=61)
2025-08-27 14:20:27,336 [DEBUG] numba.core.byteflow: stack ['$phi422.0', '$518load_global.29', '$null$518.30', '$k_max528.31']
2025-08-27 14:20:27,336 [DEBUG] numba.core.byteflow: dispatch pc=538, inst=GET_ITER(arg=None, lineno=61)
2025-08-27 14:20:27,336 [DEBUG] numba.core.byteflow: stack ['$phi422.0', '$530call.32']
2025-08-27 14:20:27,336 [DEBUG] numba.core.byteflow: end state. edges=[Edge(pc=540, stack=('$phi422.0', '$538get_iter.33'), blockstack=(), npush=0)]
2025-08-27 14:20:27,336 [DEBUG] numba.core.byteflow: pending: deque([State(pc_initial=326 nstack_initial=3), State(pc_initial=540 nstack_initial=2)])
2025-08-27 14:20:27,336 [DEBUG] numba.core.byteflow: stack: ['$phi326.0', '$phi326.1', '$phi326.2']
2025-08-27 14:20:27,336 [DEBUG] numba.core.byteflow: state.pc_initial: State(pc_initial=326 nstack_initial=3)
2025-08-27 14:20:27,336 [DEBUG] numba.core.byteflow: dispatch pc=326, inst=STORE_FAST(arg=20, lineno=41)
2025-08-27 14:20:27,336 [DEBUG] numba.core.byteflow: stack ['$phi326.0', '$phi326.1', '$phi326.2']
2025-08-27 14:20:27,336 [DEBUG] numba.core.byteflow: dispatch pc=328, inst=LOAD_FAST_LOAD_FAST(arg=44, lineno=44)
2025-08-27 14:20:27,336 [DEBUG] numba.core.byteflow: stack ['$phi326.0', '$phi326.1']
2025-08-27 14:20:27,336 [DEBUG] numba.core.byteflow: dispatch pc=330, inst=LOAD_FAST(arg=20, lineno=44)
2025-08-27 14:20:27,336 [DEBUG] numba.core.byteflow: stack ['$phi326.0', '$phi326.1', '$interp_win328.3', '$offset328.4']
2025-08-27 14:20:27,336 [DEBUG] numba.core.byteflow: dispatch pc=332, inst=LOAD_FAST(arg=7, lineno=44)
2025-08-27 14:20:27,336 [DEBUG] numba.core.byteflow: stack ['$phi326.0', '$phi326.1', '$interp_win328.3', '$offset328.4', '$i330.5']
2025-08-27 14:20:27,336 [DEBUG] numba.core.byteflow: dispatch pc=334, inst=BINARY_OP(arg=5, lineno=44)
2025-08-27 14:20:27,336 [DEBUG] numba.core.byteflow: stack ['$phi326.0', '$phi326.1', '$interp_win328.3', '$offset328.4', '$i330.5', '$index_step332.6']
2025-08-27 14:20:27,336 [DEBUG] numba.core.byteflow: dispatch pc=338, inst=BINARY_OP(arg=0, lineno=44)
2025-08-27 14:20:27,336 [DEBUG] numba.core.byteflow: stack ['$phi326.0', '$phi326.1', '$interp_win328.3', '$offset328.4', '$binop_mul334.7']
2025-08-27 14:20:27,336 [DEBUG] numba.core.byteflow: dispatch pc=342, inst=BINARY_SUBSCR(arg=None, lineno=44)
2025-08-27 14:20:27,336 [DEBUG] numba.core.byteflow: stack ['$phi326.0', '$phi326.1', '$interp_win328.3', '$binop_add338.8']
2025-08-27 14:20:27,336 [DEBUG] numba.core.byteflow: dispatch pc=346, inst=LOAD_FAST_LOAD_FAST(arg=211, lineno=45)
2025-08-27 14:20:27,336 [DEBUG] numba.core.byteflow: stack ['$phi326.0', '$phi326.1', '$342binary_subscr.9']
2025-08-27 14:20:27,336 [DEBUG] numba.core.byteflow: dispatch pc=348, inst=LOAD_FAST(arg=12, lineno=45)
2025-08-27 14:20:27,336 [DEBUG] numba.core.byteflow: stack ['$phi326.0', '$phi326.1', '$342binary_subscr.9', '$eta346.10', '$interp_delta346.11']
2025-08-27 14:20:27,336 [DEBUG] numba.core.byteflow: dispatch pc=350, inst=LOAD_FAST(arg=20, lineno=45)
2025-08-27 14:20:27,336 [DEBUG] numba.core.byteflow: stack ['$phi326.0', '$phi326.1', '$342binary_subscr.9', '$eta346.10', '$interp_delta346.11', '$offset348.12']
2025-08-27 14:20:27,336 [DEBUG] numba.core.byteflow: dispatch pc=352, inst=LOAD_FAST(arg=7, lineno=45)
2025-08-27 14:20:27,336 [DEBUG] numba.core.byteflow: stack ['$phi326.0', '$phi326.1', '$342binary_subscr.9', '$eta346.10', '$interp_delta346.11', '$offset348.12', '$i350.13']
2025-08-27 14:20:27,336 [DEBUG] numba.core.byteflow: dispatch pc=354, inst=BINARY_OP(arg=5, lineno=45)
2025-08-27 14:20:27,336 [DEBUG] numba.core.byteflow: stack ['$phi326.0', '$phi326.1', '$342binary_subscr.9', '$eta346.10', '$interp_delta346.11', '$offset348.12', '$i350.13', '$index_step352.14']
2025-08-27 14:20:27,336 [DEBUG] numba.core.byteflow: dispatch pc=358, inst=BINARY_OP(arg=0, lineno=45)
2025-08-27 14:20:27,336 [DEBUG] numba.core.byteflow: stack ['$phi326.0', '$phi326.1', '$342binary_subscr.9', '$eta346.10', '$interp_delta346.11', '$offset348.12', '$binop_mul354.15']
2025-08-27 14:20:27,337 [DEBUG] numba.core.byteflow: dispatch pc=362, inst=BINARY_SUBSCR(arg=None, lineno=45)
2025-08-27 14:20:27,337 [DEBUG] numba.core.byteflow: stack ['$phi326.0', '$phi326.1', '$342binary_subscr.9', '$eta346.10', '$interp_delta346.11', '$binop_add358.16']
2025-08-27 14:20:27,337 [DEBUG] numba.core.byteflow: dispatch pc=366, inst=BINARY_OP(arg=5, lineno=45)
2025-08-27 14:20:27,337 [DEBUG] numba.core.byteflow: stack ['$phi326.0', '$phi326.1', '$342binary_subscr.9', '$eta346.10', '$362binary_subscr.17']
2025-08-27 14:20:27,337 [DEBUG] numba.core.byteflow: dispatch pc=370, inst=BINARY_OP(arg=0, lineno=44)
2025-08-27 14:20:27,337 [DEBUG] numba.core.byteflow: stack ['$phi326.0', '$phi326.1', '$342binary_subscr.9', '$binop_mul366.18']
2025-08-27 14:20:27,337 [DEBUG] numba.core.byteflow: dispatch pc=374, inst=STORE_FAST(arg=14, lineno=43)
2025-08-27 14:20:27,337 [DEBUG] numba.core.byteflow: stack ['$phi326.0', '$phi326.1', '$binop_add370.19']
2025-08-27 14:20:27,337 [DEBUG] numba.core.byteflow: dispatch pc=376, inst=LOAD_FAST(arg=6, lineno=47)
2025-08-27 14:20:27,337 [DEBUG] numba.core.byteflow: stack ['$phi326.0', '$phi326.1']
2025-08-27 14:20:27,337 [DEBUG] numba.core.byteflow: dispatch pc=378, inst=LOAD_FAST(arg=18, lineno=47)
2025-08-27 14:20:27,337 [DEBUG] numba.core.byteflow: stack ['$phi326.0', '$phi326.1', '$y376.20']
2025-08-27 14:20:27,337 [DEBUG] numba.core.byteflow: dispatch pc=380, inst=COPY(arg=2, lineno=47)
2025-08-27 14:20:27,337 [DEBUG] numba.core.byteflow: stack ['$phi326.0', '$phi326.1', '$y376.20', '$t378.21']
2025-08-27 14:20:27,337 [DEBUG] numba.core.byteflow: dispatch pc=382, inst=COPY(arg=2, lineno=47)
2025-08-27 14:20:27,337 [DEBUG] numba.core.byteflow: stack ['$phi326.0', '$phi326.1', '$y376.20', '$t378.21', '$y376.20']
2025-08-27 14:20:27,337 [DEBUG] numba.core.byteflow: dispatch pc=384, inst=BINARY_SUBSCR(arg=None, lineno=47)
2025-08-27 14:20:27,337 [DEBUG] numba.core.byteflow: stack ['$phi326.0', '$phi326.1', '$y376.20', '$t378.21', '$y376.20', '$t378.21']
2025-08-27 14:20:27,337 [DEBUG] numba.core.byteflow: dispatch pc=388, inst=LOAD_FAST_LOAD_FAST(arg=224, lineno=47)
2025-08-27 14:20:27,337 [DEBUG] numba.core.byteflow: stack ['$phi326.0', '$phi326.1', '$y376.20', '$t378.21', '$384binary_subscr.22']
2025-08-27 14:20:27,337 [DEBUG] numba.core.byteflow: dispatch pc=390, inst=LOAD_FAST(arg=9, lineno=47)
2025-08-27 14:20:27,337 [DEBUG] numba.core.byteflow: stack ['$phi326.0', '$phi326.1', '$y376.20', '$t378.21', '$384binary_subscr.22', '$weight388.23', '$x388.24']
2025-08-27 14:20:27,337 [DEBUG] numba.core.byteflow: dispatch pc=392, inst=LOAD_FAST(arg=20, lineno=47)
2025-08-27 14:20:27,337 [DEBUG] numba.core.byteflow: stack ['$phi326.0', '$phi326.1', '$y376.20', '$t378.21', '$384binary_subscr.22', '$weight388.23', '$x388.24', '$n390.25']
2025-08-27 14:20:27,337 [DEBUG] numba.core.byteflow: dispatch pc=394, inst=BINARY_OP(arg=10, lineno=47)
2025-08-27 14:20:27,337 [DEBUG] numba.core.byteflow: stack ['$phi326.0', '$phi326.1', '$y376.20', '$t378.21', '$384binary_subscr.22', '$weight388.23', '$x388.24', '$n390.25', '$i392.26']
2025-08-27 14:20:27,337 [DEBUG] numba.core.byteflow: dispatch pc=398, inst=BINARY_SUBSCR(arg=None, lineno=47)
2025-08-27 14:20:27,337 [DEBUG] numba.core.byteflow: stack ['$phi326.0', '$phi326.1', '$y376.20', '$t378.21', '$384binary_subscr.22', '$weight388.23', '$x388.24', '$binop_sub394.27']
2025-08-27 14:20:27,337 [DEBUG] numba.core.byteflow: dispatch pc=402, inst=BINARY_OP(arg=5, lineno=47)
2025-08-27 14:20:27,337 [DEBUG] numba.core.byteflow: stack ['$phi326.0', '$phi326.1', '$y376.20', '$t378.21', '$384binary_subscr.22', '$weight388.23', '$398binary_subscr.28']
2025-08-27 14:20:27,337 [DEBUG] numba.core.byteflow: dispatch pc=406, inst=BINARY_OP(arg=13, lineno=47)
2025-08-27 14:20:27,337 [DEBUG] numba.core.byteflow: stack ['$phi326.0', '$phi326.1', '$y376.20', '$t378.21', '$384binary_subscr.22', '$binop_mul402.29']
2025-08-27 14:20:27,337 [DEBUG] numba.core.byteflow: dispatch pc=410, inst=SWAP(arg=3, lineno=47)
2025-08-27 14:20:27,337 [DEBUG] numba.core.byteflow: stack ['$phi326.0', '$phi326.1', '$y376.20', '$t378.21', '$binop_iadd406.30']
2025-08-27 14:20:27,337 [DEBUG] numba.core.byteflow: dispatch pc=412, inst=SWAP(arg=2, lineno=47)
2025-08-27 14:20:27,337 [DEBUG] numba.core.byteflow: stack ['$phi326.0', '$phi326.1', '$binop_iadd406.30', '$t378.21', '$y376.20']
2025-08-27 14:20:27,337 [DEBUG] numba.core.byteflow: dispatch pc=414, inst=STORE_SUBSCR(arg=None, lineno=47)
2025-08-27 14:20:27,337 [DEBUG] numba.core.byteflow: stack ['$phi326.0', '$phi326.1', '$binop_iadd406.30', '$y376.20', '$t378.21']
2025-08-27 14:20:27,337 [DEBUG] numba.core.byteflow: dispatch pc=418, inst=JUMP_BACKWARD(arg=50, lineno=47)
2025-08-27 14:20:27,337 [DEBUG] numba.core.byteflow: stack ['$phi326.0', '$phi326.1']
2025-08-27 14:20:27,337 [DEBUG] numba.core.byteflow: end state. edges=[Edge(pc=322, stack=('$phi326.0', '$phi326.1'), blockstack=(), npush=0)]
2025-08-27 14:20:27,337 [DEBUG] numba.core.byteflow: pending: deque([State(pc_initial=540 nstack_initial=2), State(pc_initial=322 nstack_initial=2)])
2025-08-27 14:20:27,337 [DEBUG] numba.core.byteflow: stack: ['$phi540.0', '$phi540.1']
2025-08-27 14:20:27,337 [DEBUG] numba.core.byteflow: state.pc_initial: State(pc_initial=540 nstack_initial=2)
2025-08-27 14:20:27,337 [DEBUG] numba.core.byteflow: dispatch pc=540, inst=FOR_ITER(arg=51, lineno=61)
2025-08-27 14:20:27,337 [DEBUG] numba.core.byteflow: stack ['$phi540.0', '$phi540.1']
2025-08-27 14:20:27,337 [DEBUG] numba.core.byteflow: end state. edges=[Edge(pc=646, stack=('$phi540.0', '$phi540.1', '$540for_iter.3'), blockstack=(), npush=0), Edge(pc=544, stack=('$phi540.0', '$phi540.1', '$540for_iter.3'), blockstack=(), npush=0)]
2025-08-27 14:20:27,337 [DEBUG] numba.core.byteflow: pending: deque([State(pc_initial=322 nstack_initial=2), State(pc_initial=646 nstack_initial=3), State(pc_initial=544 nstack_initial=3)])
2025-08-27 14:20:27,337 [DEBUG] numba.core.byteflow: pending: deque([State(pc_initial=646 nstack_initial=3), State(pc_initial=544 nstack_initial=3)])
2025-08-27 14:20:27,337 [DEBUG] numba.core.byteflow: stack: ['$phi646.0', '$phi646.1', '$phi646.2']
2025-08-27 14:20:27,337 [DEBUG] numba.core.byteflow: state.pc_initial: State(pc_initial=646 nstack_initial=3)
2025-08-27 14:20:27,337 [DEBUG] numba.core.byteflow: dispatch pc=646, inst=END_FOR(arg=None, lineno=61)
2025-08-27 14:20:27,337 [DEBUG] numba.core.byteflow: stack ['$phi646.0', '$phi646.1', '$phi646.2']
2025-08-27 14:20:27,337 [DEBUG] numba.core.byteflow: dispatch pc=648, inst=POP_TOP(arg=None, lineno=61)
2025-08-27 14:20:27,337 [DEBUG] numba.core.byteflow: stack ['$phi646.0', '$phi646.1']
2025-08-27 14:20:27,337 [DEBUG] numba.core.byteflow: dispatch pc=650, inst=JUMP_BACKWARD(arg=242, lineno=61)
2025-08-27 14:20:27,337 [DEBUG] numba.core.byteflow: stack ['$phi646.0']
2025-08-27 14:20:27,337 [DEBUG] numba.core.byteflow: end state. edges=[Edge(pc=170, stack=('$phi646.0',), blockstack=(), npush=0)]
2025-08-27 14:20:27,337 [DEBUG] numba.core.byteflow: pending: deque([State(pc_initial=544 nstack_initial=3), State(pc_initial=170 nstack_initial=1)])
2025-08-27 14:20:27,337 [DEBUG] numba.core.byteflow: stack: ['$phi544.0', '$phi544.1', '$phi544.2']
2025-08-27 14:20:27,337 [DEBUG] numba.core.byteflow: state.pc_initial: State(pc_initial=544 nstack_initial=3)
2025-08-27 14:20:27,337 [DEBUG] numba.core.byteflow: dispatch pc=544, inst=STORE_FAST(arg=22, lineno=61)
2025-08-27 14:20:27,337 [DEBUG] numba.core.byteflow: stack ['$phi544.0', '$phi544.1', '$phi544.2']
2025-08-27 14:20:27,337 [DEBUG] numba.core.byteflow: dispatch pc=546, inst=LOAD_FAST_LOAD_FAST(arg=44, lineno=63)
2025-08-27 14:20:27,337 [DEBUG] numba.core.byteflow: stack ['$phi544.0', '$phi544.1']
2025-08-27 14:20:27,337 [DEBUG] numba.core.byteflow: dispatch pc=548, inst=LOAD_FAST(arg=22, lineno=63)
2025-08-27 14:20:27,337 [DEBUG] numba.core.byteflow: stack ['$phi544.0', '$phi544.1', '$interp_win546.3', '$offset546.4']
2025-08-27 14:20:27,337 [DEBUG] numba.core.byteflow: dispatch pc=550, inst=LOAD_FAST(arg=7, lineno=63)
2025-08-27 14:20:27,337 [DEBUG] numba.core.byteflow: stack ['$phi544.0', '$phi544.1', '$interp_win546.3', '$offset546.4', '$k548.5']
2025-08-27 14:20:27,338 [DEBUG] numba.core.byteflow: dispatch pc=552, inst=BINARY_OP(arg=5, lineno=63)
2025-08-27 14:20:27,338 [DEBUG] numba.core.byteflow: stack ['$phi544.0', '$phi544.1', '$interp_win546.3', '$offset546.4', '$k548.5', '$index_step550.6']
2025-08-27 14:20:27,338 [DEBUG] numba.core.byteflow: dispatch pc=556, inst=BINARY_OP(arg=0, lineno=63)
2025-08-27 14:20:27,338 [DEBUG] numba.core.byteflow: stack ['$phi544.0', '$phi544.1', '$interp_win546.3', '$offset546.4', '$binop_mul552.7']
2025-08-27 14:20:27,338 [DEBUG] numba.core.byteflow: dispatch pc=560, inst=BINARY_SUBSCR(arg=None, lineno=63)
2025-08-27 14:20:27,338 [DEBUG] numba.core.byteflow: stack ['$phi544.0', '$phi544.1', '$interp_win546.3', '$binop_add556.8']
2025-08-27 14:20:27,338 [DEBUG] numba.core.byteflow: dispatch pc=564, inst=LOAD_FAST_LOAD_FAST(arg=211, lineno=64)
2025-08-27 14:20:27,338 [DEBUG] numba.core.byteflow: stack ['$phi544.0', '$phi544.1', '$560binary_subscr.9']
2025-08-27 14:20:27,338 [DEBUG] numba.core.byteflow: dispatch pc=566, inst=LOAD_FAST(arg=12, lineno=64)
2025-08-27 14:20:27,338 [DEBUG] numba.core.byteflow: stack ['$phi544.0', '$phi544.1', '$560binary_subscr.9', '$eta564.10', '$interp_delta564.11']
2025-08-27 14:20:27,338 [DEBUG] numba.core.byteflow: dispatch pc=568, inst=LOAD_FAST(arg=22, lineno=64)
2025-08-27 14:20:27,338 [DEBUG] numba.core.byteflow: stack ['$phi544.0', '$phi544.1', '$560binary_subscr.9', '$eta564.10', '$interp_delta564.11', '$offset566.12']
2025-08-27 14:20:27,338 [DEBUG] numba.core.byteflow: dispatch pc=570, inst=LOAD_FAST(arg=7, lineno=64)
2025-08-27 14:20:27,338 [DEBUG] numba.core.byteflow: stack ['$phi544.0', '$phi544.1', '$560binary_subscr.9', '$eta564.10', '$interp_delta564.11', '$offset566.12', '$k568.13']
2025-08-27 14:20:27,338 [DEBUG] numba.core.byteflow: dispatch pc=572, inst=BINARY_OP(arg=5, lineno=64)
2025-08-27 14:20:27,338 [DEBUG] numba.core.byteflow: stack ['$phi544.0', '$phi544.1', '$560binary_subscr.9', '$eta564.10', '$interp_delta564.11', '$offset566.12', '$k568.13', '$index_step570.14']
2025-08-27 14:20:27,338 [DEBUG] numba.core.byteflow: dispatch pc=576, inst=BINARY_OP(arg=0, lineno=64)
2025-08-27 14:20:27,338 [DEBUG] numba.core.byteflow: stack ['$phi544.0', '$phi544.1', '$560binary_subscr.9', '$eta564.10', '$interp_delta564.11', '$offset566.12', '$binop_mul572.15']
2025-08-27 14:20:27,338 [DEBUG] numba.core.byteflow: dispatch pc=580, inst=BINARY_SUBSCR(arg=None, lineno=64)
2025-08-27 14:20:27,338 [DEBUG] numba.core.byteflow: stack ['$phi544.0', '$phi544.1', '$560binary_subscr.9', '$eta564.10', '$interp_delta564.11', '$binop_add576.16']
2025-08-27 14:20:27,338 [DEBUG] numba.core.byteflow: dispatch pc=584, inst=BINARY_OP(arg=5, lineno=64)
2025-08-27 14:20:27,338 [DEBUG] numba.core.byteflow: stack ['$phi544.0', '$phi544.1', '$560binary_subscr.9', '$eta564.10', '$580binary_subscr.17']
2025-08-27 14:20:27,338 [DEBUG] numba.core.byteflow: dispatch pc=588, inst=BINARY_OP(arg=0, lineno=63)
2025-08-27 14:20:27,338 [DEBUG] numba.core.byteflow: stack ['$phi544.0', '$phi544.1', '$560binary_subscr.9', '$binop_mul584.18']
2025-08-27 14:20:27,338 [DEBUG] numba.core.byteflow: dispatch pc=592, inst=STORE_FAST(arg=14, lineno=62)
2025-08-27 14:20:27,338 [DEBUG] numba.core.byteflow: stack ['$phi544.0', '$phi544.1', '$binop_add588.19']
2025-08-27 14:20:27,338 [DEBUG] numba.core.byteflow: dispatch pc=594, inst=LOAD_FAST(arg=6, lineno=66)
2025-08-27 14:20:27,338 [DEBUG] numba.core.byteflow: stack ['$phi544.0', '$phi544.1']
2025-08-27 14:20:27,338 [DEBUG] numba.core.byteflow: dispatch pc=596, inst=LOAD_FAST(arg=18, lineno=66)
2025-08-27 14:20:27,338 [DEBUG] numba.core.byteflow: stack ['$phi544.0', '$phi544.1', '$y594.20']
2025-08-27 14:20:27,338 [DEBUG] numba.core.byteflow: dispatch pc=598, inst=COPY(arg=2, lineno=66)
2025-08-27 14:20:27,338 [DEBUG] numba.core.byteflow: stack ['$phi544.0', '$phi544.1', '$y594.20', '$t596.21']
2025-08-27 14:20:27,338 [DEBUG] numba.core.byteflow: dispatch pc=600, inst=COPY(arg=2, lineno=66)
2025-08-27 14:20:27,338 [DEBUG] numba.core.byteflow: stack ['$phi544.0', '$phi544.1', '$y594.20', '$t596.21', '$y594.20']
2025-08-27 14:20:27,338 [DEBUG] numba.core.byteflow: dispatch pc=602, inst=BINARY_SUBSCR(arg=None, lineno=66)
2025-08-27 14:20:27,338 [DEBUG] numba.core.byteflow: stack ['$phi544.0', '$phi544.1', '$y594.20', '$t596.21', '$y594.20', '$t596.21']
2025-08-27 14:20:27,338 [DEBUG] numba.core.byteflow: dispatch pc=606, inst=LOAD_FAST_LOAD_FAST(arg=224, lineno=66)
2025-08-27 14:20:27,338 [DEBUG] numba.core.byteflow: stack ['$phi544.0', '$phi544.1', '$y594.20', '$t596.21', '$602binary_subscr.22']
2025-08-27 14:20:27,338 [DEBUG] numba.core.byteflow: dispatch pc=608, inst=LOAD_FAST(arg=9, lineno=66)
2025-08-27 14:20:27,338 [DEBUG] numba.core.byteflow: stack ['$phi544.0', '$phi544.1', '$y594.20', '$t596.21', '$602binary_subscr.22', '$weight606.23', '$x606.24']
2025-08-27 14:20:27,338 [DEBUG] numba.core.byteflow: dispatch pc=610, inst=LOAD_FAST(arg=22, lineno=66)
2025-08-27 14:20:27,338 [DEBUG] numba.core.byteflow: stack ['$phi544.0', '$phi544.1', '$y594.20', '$t596.21', '$602binary_subscr.22', '$weight606.23', '$x606.24', '$n608.25']
2025-08-27 14:20:27,338 [DEBUG] numba.core.byteflow: dispatch pc=612, inst=BINARY_OP(arg=0, lineno=66)
2025-08-27 14:20:27,338 [DEBUG] numba.core.byteflow: stack ['$phi544.0', '$phi544.1', '$y594.20', '$t596.21', '$602binary_subscr.22', '$weight606.23', '$x606.24', '$n608.25', '$k610.26']
2025-08-27 14:20:27,338 [DEBUG] numba.core.byteflow: dispatch pc=616, inst=LOAD_CONST(arg=3, lineno=66)
2025-08-27 14:20:27,338 [DEBUG] numba.core.byteflow: stack ['$phi544.0', '$phi544.1', '$y594.20', '$t596.21', '$602binary_subscr.22', '$weight606.23', '$x606.24', '$binop_add612.27']
2025-08-27 14:20:27,338 [DEBUG] numba.core.byteflow: dispatch pc=618, inst=BINARY_OP(arg=0, lineno=66)
2025-08-27 14:20:27,338 [DEBUG] numba.core.byteflow: stack ['$phi544.0', '$phi544.1', '$y594.20', '$t596.21', '$602binary_subscr.22', '$weight606.23', '$x606.24', '$binop_add612.27', '$const616.28.3']
2025-08-27 14:20:27,338 [DEBUG] numba.core.byteflow: dispatch pc=622, inst=BINARY_SUBSCR(arg=None, lineno=66)
2025-08-27 14:20:27,338 [DEBUG] numba.core.byteflow: stack ['$phi544.0', '$phi544.1', '$y594.20', '$t596.21', '$602binary_subscr.22', '$weight606.23', '$x606.24', '$binop_add618.29']
2025-08-27 14:20:27,338 [DEBUG] numba.core.byteflow: dispatch pc=626, inst=BINARY_OP(arg=5, lineno=66)
2025-08-27 14:20:27,338 [DEBUG] numba.core.byteflow: stack ['$phi544.0', '$phi544.1', '$y594.20', '$t596.21', '$602binary_subscr.22', '$weight606.23', '$622binary_subscr.30']
2025-08-27 14:20:27,338 [DEBUG] numba.core.byteflow: dispatch pc=630, inst=BINARY_OP(arg=13, lineno=66)
2025-08-27 14:20:27,338 [DEBUG] numba.core.byteflow: stack ['$phi544.0', '$phi544.1', '$y594.20', '$t596.21', '$602binary_subscr.22', '$binop_mul626.31']
2025-08-27 14:20:27,338 [DEBUG] numba.core.byteflow: dispatch pc=634, inst=SWAP(arg=3, lineno=66)
2025-08-27 14:20:27,338 [DEBUG] numba.core.byteflow: stack ['$phi544.0', '$phi544.1', '$y594.20', '$t596.21', '$binop_iadd630.32']
2025-08-27 14:20:27,338 [DEBUG] numba.core.byteflow: dispatch pc=636, inst=SWAP(arg=2, lineno=66)
2025-08-27 14:20:27,338 [DEBUG] numba.core.byteflow: stack ['$phi544.0', '$phi544.1', '$binop_iadd630.32', '$t596.21', '$y594.20']
2025-08-27 14:20:27,338 [DEBUG] numba.core.byteflow: dispatch pc=638, inst=STORE_SUBSCR(arg=None, lineno=66)
2025-08-27 14:20:27,338 [DEBUG] numba.core.byteflow: stack ['$phi544.0', '$phi544.1', '$binop_iadd630.32', '$y594.20', '$t596.21']
2025-08-27 14:20:27,338 [DEBUG] numba.core.byteflow: dispatch pc=642, inst=JUMP_BACKWARD(arg=53, lineno=66)
2025-08-27 14:20:27,338 [DEBUG] numba.core.byteflow: stack ['$phi544.0', '$phi544.1']
2025-08-27 14:20:27,338 [DEBUG] numba.core.byteflow: end state. edges=[Edge(pc=540, stack=('$phi544.0', '$phi544.1'), blockstack=(), npush=0)]
2025-08-27 14:20:27,339 [DEBUG] numba.core.byteflow: pending: deque([State(pc_initial=170 nstack_initial=1), State(pc_initial=540 nstack_initial=2)])
2025-08-27 14:20:27,339 [DEBUG] numba.core.byteflow: pending: deque([State(pc_initial=540 nstack_initial=2)])
2025-08-27 14:20:27,339 [DEBUG] numba.core.byteflow: -------------------------Prune PHIs-------------------------
2025-08-27 14:20:27,339 [DEBUG] numba.core.byteflow: Used_phis: defaultdict(<class 'set'>,
            {State(pc_initial=0 nstack_initial=0): set(),
             State(pc_initial=170 nstack_initial=1): {'$phi170.0'},
             State(pc_initial=174 nstack_initial=2): {'$phi174.1'},
             State(pc_initial=322 nstack_initial=2): {'$phi322.1'},
             State(pc_initial=326 nstack_initial=3): {'$phi326.2'},
             State(pc_initial=422 nstack_initial=3): set(),
             State(pc_initial=540 nstack_initial=2): {'$phi540.1'},
             State(pc_initial=544 nstack_initial=3): {'$phi544.2'},
             State(pc_initial=646 nstack_initial=3): set(),
             State(pc_initial=654 nstack_initial=2): set()})
2025-08-27 14:20:27,339 [DEBUG] numba.core.byteflow: defmap: {'$phi170.0': State(pc_initial=0 nstack_initial=0),
 '$phi174.1': State(pc_initial=170 nstack_initial=1),
 '$phi322.1': State(pc_initial=174 nstack_initial=2),
 '$phi326.2': State(pc_initial=322 nstack_initial=2),
 '$phi422.2': State(pc_initial=322 nstack_initial=2),
 '$phi540.1': State(pc_initial=422 nstack_initial=3),
 '$phi544.2': State(pc_initial=540 nstack_initial=2),
 '$phi646.2': State(pc_initial=540 nstack_initial=2),
 '$phi654.1': State(pc_initial=170 nstack_initial=1)}
2025-08-27 14:20:27,339 [DEBUG] numba.core.byteflow: phismap: defaultdict(<class 'set'>,
            {'$phi170.0': {('$168get_iter.29',
                            State(pc_initial=0 nstack_initial=0)),
                           ('$phi646.0',
                            State(pc_initial=646 nstack_initial=3))},
             '$phi174.0': {('$phi170.0',
                            State(pc_initial=170 nstack_initial=1))},
             '$phi174.1': {('$170for_iter.2',
                            State(pc_initial=170 nstack_initial=1))},
             '$phi322.0': {('$phi174.0',
                            State(pc_initial=174 nstack_initial=2)),
                           ('$phi326.0',
                            State(pc_initial=326 nstack_initial=3))},
             '$phi322.1': {('$320get_iter.39',
                            State(pc_initial=174 nstack_initial=2)),
                           ('$phi326.1',
                            State(pc_initial=326 nstack_initial=3))},
             '$phi326.0': {('$phi322.0',
                            State(pc_initial=322 nstack_initial=2))},
             '$phi326.1': {('$phi322.1',
                            State(pc_initial=322 nstack_initial=2))},
             '$phi326.2': {('$322for_iter.3',
                            State(pc_initial=322 nstack_initial=2))},
             '$phi422.0': {('$phi322.0',
                            State(pc_initial=322 nstack_initial=2))},
             '$phi422.1': {('$phi322.1',
                            State(pc_initial=322 nstack_initial=2))},
             '$phi422.2': {('$322for_iter.3',
                            State(pc_initial=322 nstack_initial=2))},
             '$phi540.0': {('$phi422.0',
                            State(pc_initial=422 nstack_initial=3)),
                           ('$phi544.0',
                            State(pc_initial=544 nstack_initial=3))},
             '$phi540.1': {('$538get_iter.33',
                            State(pc_initial=422 nstack_initial=3)),
                           ('$phi544.1',
                            State(pc_initial=544 nstack_initial=3))},
             '$phi544.0': {('$phi540.0',
                            State(pc_initial=540 nstack_initial=2))},
             '$phi544.1': {('$phi540.1',
                            State(pc_initial=540 nstack_initial=2))},
             '$phi544.2': {('$540for_iter.3',
                            State(pc_initial=540 nstack_initial=2))},
             '$phi646.0': {('$phi540.0',
                            State(pc_initial=540 nstack_initial=2))},
             '$phi646.1': {('$phi540.1',
                            State(pc_initial=540 nstack_initial=2))},
             '$phi646.2': {('$540for_iter.3',
                            State(pc_initial=540 nstack_initial=2))},
             '$phi654.0': {('$phi170.0',
                            State(pc_initial=170 nstack_initial=1))},
             '$phi654.1': {('$170for_iter.2',
                            State(pc_initial=170 nstack_initial=1))}})
2025-08-27 14:20:27,339 [DEBUG] numba.core.byteflow: changing phismap: defaultdict(<class 'set'>,
            {'$phi170.0': {('$168get_iter.29',
                            State(pc_initial=0 nstack_initial=0)),
                           ('$phi540.0',
                            State(pc_initial=540 nstack_initial=2))},
             '$phi174.0': {('$168get_iter.29',
                            State(pc_initial=0 nstack_initial=0)),
                           ('$phi540.0',
                            State(pc_initial=540 nstack_initial=2))},
             '$phi174.1': {('$170for_iter.2',
                            State(pc_initial=170 nstack_initial=1))},
             '$phi322.0': {('$168get_iter.29',
                            State(pc_initial=0 nstack_initial=0)),
                           ('$phi322.0',
                            State(pc_initial=322 nstack_initial=2)),
                           ('$phi540.0',
                            State(pc_initial=540 nstack_initial=2))},
             '$phi322.1': {('$320get_iter.39',
                            State(pc_initial=174 nstack_initial=2)),
                           ('$phi322.1',
                            State(pc_initial=322 nstack_initial=2))},
             '$phi326.0': {('$168get_iter.29',
                            State(pc_initial=0 nstack_initial=0)),
                           ('$phi540.0',
                            State(pc_initial=540 nstack_initial=2))},
             '$phi326.1': {('$320get_iter.39',
                            State(pc_initial=174 nstack_initial=2))},
             '$phi326.2': {('$322for_iter.3',
                            State(pc_initial=322 nstack_initial=2))},
             '$phi422.0': {('$168get_iter.29',
                            State(pc_initial=0 nstack_initial=0)),
                           ('$phi540.0',
                            State(pc_initial=540 nstack_initial=2))},
             '$phi422.1': {('$320get_iter.39',
                            State(pc_initial=174 nstack_initial=2))},
             '$phi422.2': {('$322for_iter.3',
                            State(pc_initial=322 nstack_initial=2))},
             '$phi540.0': {('$168get_iter.29',
                            State(pc_initial=0 nstack_initial=0)),
                           ('$phi540.0',
                            State(pc_initial=540 nstack_initial=2))},
             '$phi540.1': {('$538get_iter.33',
                            State(pc_initial=422 nstack_initial=3)),
                           ('$phi540.1',
                            State(pc_initial=540 nstack_initial=2))},
             '$phi544.0': {('$168get_iter.29',
                            State(pc_initial=0 nstack_initial=0))},
             '$phi544.1': {('$538get_iter.33',
                            State(pc_initial=422 nstack_initial=3))},
             '$phi544.2': {('$540for_iter.3',
                            State(pc_initial=540 nstack_initial=2))},
             '$phi646.0': {('$168get_iter.29',
                            State(pc_initial=0 nstack_initial=0))},
             '$phi646.1': {('$538get_iter.33',
                            State(pc_initial=422 nstack_initial=3))},
             '$phi646.2': {('$540for_iter.3',
                            State(pc_initial=540 nstack_initial=2))},
             '$phi654.0': {('$168get_iter.29',
                            State(pc_initial=0 nstack_initial=0)),
                           ('$phi540.0',
                            State(pc_initial=540 nstack_initial=2))},
             '$phi654.1': {('$170for_iter.2',
                            State(pc_initial=170 nstack_initial=1))}})
2025-08-27 14:20:27,340 [DEBUG] numba.core.byteflow: changing phismap: defaultdict(<class 'set'>,
            {'$phi170.0': {('$168get_iter.29',
                            State(pc_initial=0 nstack_initial=0))},
             '$phi174.0': {('$168get_iter.29',
                            State(pc_initial=0 nstack_initial=0))},
             '$phi174.1': {('$170for_iter.2',
                            State(pc_initial=170 nstack_initial=1))},
             '$phi322.0': {('$168get_iter.29',
                            State(pc_initial=0 nstack_initial=0))},
             '$phi322.1': {('$320get_iter.39',
                            State(pc_initial=174 nstack_initial=2))},
             '$phi326.0': {('$168get_iter.29',
                            State(pc_initial=0 nstack_initial=0))},
             '$phi326.1': {('$320get_iter.39',
                            State(pc_initial=174 nstack_initial=2))},
             '$phi326.2': {('$322for_iter.3',
                            State(pc_initial=322 nstack_initial=2))},
             '$phi422.0': {('$168get_iter.29',
                            State(pc_initial=0 nstack_initial=0))},
             '$phi422.1': {('$320get_iter.39',
                            State(pc_initial=174 nstack_initial=2))},
             '$phi422.2': {('$322for_iter.3',
                            State(pc_initial=322 nstack_initial=2))},
             '$phi540.0': {('$168get_iter.29',
                            State(pc_initial=0 nstack_initial=0))},
             '$phi540.1': {('$538get_iter.33',
                            State(pc_initial=422 nstack_initial=3))},
             '$phi544.0': {('$168get_iter.29',
                            State(pc_initial=0 nstack_initial=0))},
             '$phi544.1': {('$538get_iter.33',
                            State(pc_initial=422 nstack_initial=3))},
             '$phi544.2': {('$540for_iter.3',
                            State(pc_initial=540 nstack_initial=2))},
             '$phi646.0': {('$168get_iter.29',
                            State(pc_initial=0 nstack_initial=0))},
             '$phi646.1': {('$538get_iter.33',
                            State(pc_initial=422 nstack_initial=3))},
             '$phi646.2': {('$540for_iter.3',
                            State(pc_initial=540 nstack_initial=2))},
             '$phi654.0': {('$168get_iter.29',
                            State(pc_initial=0 nstack_initial=0))},
             '$phi654.1': {('$170for_iter.2',
                            State(pc_initial=170 nstack_initial=1))}})
2025-08-27 14:20:27,340 [DEBUG] numba.core.byteflow: changing phismap: defaultdict(<class 'set'>,
            {'$phi170.0': {('$168get_iter.29',
                            State(pc_initial=0 nstack_initial=0))},
             '$phi174.0': {('$168get_iter.29',
                            State(pc_initial=0 nstack_initial=0))},
             '$phi174.1': {('$170for_iter.2',
                            State(pc_initial=170 nstack_initial=1))},
             '$phi322.0': {('$168get_iter.29',
                            State(pc_initial=0 nstack_initial=0))},
             '$phi322.1': {('$320get_iter.39',
                            State(pc_initial=174 nstack_initial=2))},
             '$phi326.0': {('$168get_iter.29',
                            State(pc_initial=0 nstack_initial=0))},
             '$phi326.1': {('$320get_iter.39',
                            State(pc_initial=174 nstack_initial=2))},
             '$phi326.2': {('$322for_iter.3',
                            State(pc_initial=322 nstack_initial=2))},
             '$phi422.0': {('$168get_iter.29',
                            State(pc_initial=0 nstack_initial=0))},
             '$phi422.1': {('$320get_iter.39',
                            State(pc_initial=174 nstack_initial=2))},
             '$phi422.2': {('$322for_iter.3',
                            State(pc_initial=322 nstack_initial=2))},
             '$phi540.0': {('$168get_iter.29',
                            State(pc_initial=0 nstack_initial=0))},
             '$phi540.1': {('$538get_iter.33',
                            State(pc_initial=422 nstack_initial=3))},
             '$phi544.0': {('$168get_iter.29',
                            State(pc_initial=0 nstack_initial=0))},
             '$phi544.1': {('$538get_iter.33',
                            State(pc_initial=422 nstack_initial=3))},
             '$phi544.2': {('$540for_iter.3',
                            State(pc_initial=540 nstack_initial=2))},
             '$phi646.0': {('$168get_iter.29',
                            State(pc_initial=0 nstack_initial=0))},
             '$phi646.1': {('$538get_iter.33',
                            State(pc_initial=422 nstack_initial=3))},
             '$phi646.2': {('$540for_iter.3',
                            State(pc_initial=540 nstack_initial=2))},
             '$phi654.0': {('$168get_iter.29',
                            State(pc_initial=0 nstack_initial=0))},
             '$phi654.1': {('$170for_iter.2',
                            State(pc_initial=170 nstack_initial=1))}})
2025-08-27 14:20:27,340 [DEBUG] numba.core.byteflow: keep phismap: {'$phi170.0': {('$168get_iter.29', State(pc_initial=0 nstack_initial=0))},
 '$phi174.1': {('$170for_iter.2', State(pc_initial=170 nstack_initial=1))},
 '$phi322.1': {('$320get_iter.39', State(pc_initial=174 nstack_initial=2))},
 '$phi326.2': {('$322for_iter.3', State(pc_initial=322 nstack_initial=2))},
 '$phi540.1': {('$538get_iter.33', State(pc_initial=422 nstack_initial=3))},
 '$phi544.2': {('$540for_iter.3', State(pc_initial=540 nstack_initial=2))}}
2025-08-27 14:20:27,340 [DEBUG] numba.core.byteflow: new_out: defaultdict(<class 'dict'>,
            {State(pc_initial=0 nstack_initial=0): {'$phi170.0': '$168get_iter.29'},
             State(pc_initial=170 nstack_initial=1): {'$phi174.1': '$170for_iter.2'},
             State(pc_initial=174 nstack_initial=2): {'$phi322.1': '$320get_iter.39'},
             State(pc_initial=322 nstack_initial=2): {'$phi326.2': '$322for_iter.3'},
             State(pc_initial=422 nstack_initial=3): {'$phi540.1': '$538get_iter.33'},
             State(pc_initial=540 nstack_initial=2): {'$phi544.2': '$540for_iter.3'}})
2025-08-27 14:20:27,340 [DEBUG] numba.core.byteflow: ----------------------DONE Prune PHIs-----------------------
2025-08-27 14:20:27,340 [DEBUG] numba.core.byteflow: block_infos State(pc_initial=0 nstack_initial=0):
AdaptBlockInfo(insts=((0, {}), (2, {}), (4, {'idx': 0, 'res': '$4load_global.0'}), (14, {'res1': '$scale14.2', 'res2': '$num_table14.3'}), (16, {'op': '*', 'lhs': '$scale14.2', 'rhs': '$num_table14.3', 'res': '$binop_mul16.4'}), (20, {'func': '$4load_global.0', 'args': ['$binop_mul16.4'], 'kw_names': None, 'res': '$20call.5'}), (28, {'value': '$20call.5'}), (30, {'res': '$const30.6.1'}), (32, {'value': '$const30.6.1'}), (34, {'res': '$const34.7.2'}), (36, {'value': '$const34.7.2'}), (38, {'res': '$const38.8.1'}), (40, {'value': '$const38.8.1'}), (42, {'res': '$const42.9.1'}), (44, {'value': '$const42.9.1'}), (46, {'res': '$const46.10.2'}), (48, {'value': '$const46.10.2'}), (50, {'res': '$const50.11.1'}), (52, {'value': '$const50.11.1'}), (54, {'res': '$const54.12.1'}), (56, {'value': '$const54.12.1'}), (58, {'res': '$interp_win58.13'}), (60, {'item': '$interp_win58.13', 'res': '$60load_attr.14'}), (80, {'res': '$const80.15.2'}), (82, {'index': '$const80.15.2', 'target': '$60load_attr.14', 'res': '$82binary_subscr.16'}), (86, {'value': '$82binary_subscr.16'}), (88, {'res': '$x88.17'}), (90, {'item': '$x88.17', 'res': '$90load_attr.18'}), (110, {'res': '$const110.19.2'}), (112, {'index': '$const110.19.2', 'target': '$90load_attr.18', 'res': '$112binary_subscr.20'}), (116, {'value': '$112binary_subscr.20'}), (118, {'res': '$t_out118.21'}), (120, {'item': '$t_out118.21', 'res': '$120load_attr.22'}), (140, {'res': '$const140.23.2'}), (142, {'index': '$const140.23.2', 'target': '$120load_attr.22', 'res': '$142binary_subscr.24'}), (146, {'value': '$142binary_subscr.24'}), (148, {'idx': 2, 'res': '$148load_global.25'}), (158, {'res': '$n_out158.27'}), (160, {'func': '$148load_global.25', 'args': ['$n_out158.27'], 'kw_names': None, 'res': '$160call.28'}), (168, {'value': '$160call.28', 'res': '$168get_iter.29'})), outgoing_phis={'$phi170.0': '$168get_iter.29'}, blockstack=(), active_try_block=None, outgoing_edgepushed={170: ('$168get_iter.29',)})
2025-08-27 14:20:27,341 [DEBUG] numba.core.byteflow: block_infos State(pc_initial=170 nstack_initial=1):
AdaptBlockInfo(insts=((170, {'iterator': '$phi170.0', 'pair': '$170for_iter.1', 'indval': '$170for_iter.2', 'pred': '$170for_iter.3'}),), outgoing_phis={'$phi174.1': '$170for_iter.2'}, blockstack=(), active_try_block=None, outgoing_edgepushed={654: ('$phi170.0', '$170for_iter.2'), 174: ('$phi170.0', '$170for_iter.2')})
2025-08-27 14:20:27,341 [DEBUG] numba.core.byteflow: block_infos State(pc_initial=174 nstack_initial=2):
AdaptBlockInfo(insts=((174, {'value': '$phi174.1'}), (176, {'res': '$t_out176.2'}), (178, {'res': '$t178.3'}), (180, {'index': '$t178.3', 'target': '$t_out176.2', 'res': '$180binary_subscr.4'}), (184, {'value': '$180binary_subscr.4'}), (186, {'idx': 0, 'res': '$186load_global.5'}), (196, {'res': '$time_register196.7'}), (198, {'func': '$186load_global.5', 'args': ['$time_register196.7'], 'kw_names': None, 'res': '$198call.8'}), (206, {'value': '$198call.8'}), (208, {'res1': '$scale208.9', 'res2': '$time_register208.10'}), (210, {'res': '$n210.11'}), (212, {'op': '-', 'lhs': '$time_register208.10', 'rhs': '$n210.11', 'res': '$binop_sub212.12'}), (216, {'op': '*', 'lhs': '$scale208.9', 'rhs': '$binop_sub212.12', 'res': '$binop_mul216.13'}), (220, {'value': '$binop_mul216.13'}), (222, {'res1': '$frac222.14', 'res2': '$num_table222.15'}), (224, {'op': '*', 'lhs': '$frac222.14', 'rhs': '$num_table222.15', 'res': '$binop_mul224.16'}), (228, {'value': '$binop_mul224.16'}), (230, {'idx': 0, 'res': '$230load_global.17'}), (240, {'res': '$index_frac240.19'}), (242, {'func': '$230load_global.17', 'args': ['$index_frac240.19'], 'kw_names': None, 'res': '$242call.20'}), (250, {'value': '$242call.20'}), (252, {'res1': '$index_frac252.21', 'res2': '$offset252.22'}), (254, {'op': '-', 'lhs': '$index_frac252.21', 'rhs': '$offset252.22', 'res': '$binop_sub254.23'}), (258, {'value': '$binop_sub254.23'}), (260, {'idx': 3, 'res': '$260load_global.24'}), (270, {'res': '$n270.26'}), (272, {'res': '$const272.27.3'}), (274, {'op': '+', 'lhs': '$n270.26', 'rhs': '$const272.27.3', 'res': '$binop_add274.28'}), (278, {'res1': '$nwin278.29', 'res2': '$offset278.30'}), (280, {'op': '-', 'lhs': '$nwin278.29', 'rhs': '$offset278.30', 'res': '$binop_sub280.31'}), (284, {'res': '$index_step284.32'}), (286, {'op': '//', 'lhs': '$binop_sub280.31', 'rhs': '$index_step284.32', 'res': '$binop_floordiv286.33'}), (290, {'func': '$260load_global.24', 'args': ['$binop_add274.28', '$binop_floordiv286.33'], 'kw_names': None, 'res': '$290call.34'}), (298, {'value': '$290call.34'}), (300, {'idx': 4, 'res': '$300load_global.35'}), (310, {'res': '$i_max310.37'}), (312, {'func': '$300load_global.35', 'args': ['$i_max310.37'], 'kw_names': None, 'res': '$312call.38'}), (320, {'value': '$312call.38', 'res': '$320get_iter.39'})), outgoing_phis={'$phi322.1': '$320get_iter.39'}, blockstack=(), active_try_block=None, outgoing_edgepushed={322: ('$phi174.0', '$320get_iter.39')})
2025-08-27 14:20:27,341 [DEBUG] numba.core.byteflow: block_infos State(pc_initial=322 nstack_initial=2):
AdaptBlockInfo(insts=((322, {'iterator': '$phi322.1', 'pair': '$322for_iter.2', 'indval': '$322for_iter.3', 'pred': '$322for_iter.4'}),), outgoing_phis={'$phi326.2': '$322for_iter.3'}, blockstack=(), active_try_block=None, outgoing_edgepushed={422: ('$phi322.0', '$phi322.1', '$322for_iter.3'), 326: ('$phi322.0', '$phi322.1', '$322for_iter.3')})
2025-08-27 14:20:27,341 [DEBUG] numba.core.byteflow: block_infos State(pc_initial=326 nstack_initial=3):
AdaptBlockInfo(insts=((326, {'value': '$phi326.2'}), (328, {'res1': '$interp_win328.3', 'res2': '$offset328.4'}), (330, {'res': '$i330.5'}), (332, {'res': '$index_step332.6'}), (334, {'op': '*', 'lhs': '$i330.5', 'rhs': '$index_step332.6', 'res': '$binop_mul334.7'}), (338, {'op': '+', 'lhs': '$offset328.4', 'rhs': '$binop_mul334.7', 'res': '$binop_add338.8'}), (342, {'index': '$binop_add338.8', 'target': '$interp_win328.3', 'res': '$342binary_subscr.9'}), (346, {'res1': '$eta346.10', 'res2': '$interp_delta346.11'}), (348, {'res': '$offset348.12'}), (350, {'res': '$i350.13'}), (352, {'res': '$index_step352.14'}), (354, {'op': '*', 'lhs': '$i350.13', 'rhs': '$index_step352.14', 'res': '$binop_mul354.15'}), (358, {'op': '+', 'lhs': '$offset348.12', 'rhs': '$binop_mul354.15', 'res': '$binop_add358.16'}), (362, {'index': '$binop_add358.16', 'target': '$interp_delta346.11', 'res': '$362binary_subscr.17'}), (366, {'op': '*', 'lhs': '$eta346.10', 'rhs': '$362binary_subscr.17', 'res': '$binop_mul366.18'}), (370, {'op': '+', 'lhs': '$342binary_subscr.9', 'rhs': '$binop_mul366.18', 'res': '$binop_add370.19'}), (374, {'value': '$binop_add370.19'}), (376, {'res': '$y376.20'}), (378, {'res': '$t378.21'}), (384, {'index': '$t378.21', 'target': '$y376.20', 'res': '$384binary_subscr.22'}), (388, {'res1': '$weight388.23', 'res2': '$x388.24'}), (390, {'res': '$n390.25'}), (392, {'res': '$i392.26'}), (394, {'op': '-', 'lhs': '$n390.25', 'rhs': '$i392.26', 'res': '$binop_sub394.27'}), (398, {'index': '$binop_sub394.27', 'target': '$x388.24', 'res': '$398binary_subscr.28'}), (402, {'op': '*', 'lhs': '$weight388.23', 'rhs': '$398binary_subscr.28', 'res': '$binop_mul402.29'}), (406, {'op': '+=', 'lhs': '$384binary_subscr.22', 'rhs': '$binop_mul402.29', 'res': '$binop_iadd406.30'}), (414, {'target': '$y376.20', 'index': '$t378.21', 'value': '$binop_iadd406.30'}), (418, {})), outgoing_phis={}, blockstack=(), active_try_block=None, outgoing_edgepushed={322: ('$phi326.0', '$phi326.1')})
2025-08-27 14:20:27,341 [DEBUG] numba.core.byteflow: block_infos State(pc_initial=422 nstack_initial=3):
AdaptBlockInfo(insts=((426, {'res1': '$scale426.3', 'res2': '$frac426.4'}), (428, {'op': '-', 'lhs': '$scale426.3', 'rhs': '$frac426.4', 'res': '$binop_sub428.5'}), (432, {'value': '$binop_sub428.5'}), (434, {'res1': '$frac434.6', 'res2': '$num_table434.7'}), (436, {'op': '*', 'lhs': '$frac434.6', 'rhs': '$num_table434.7', 'res': '$binop_mul436.8'}), (440, {'value': '$binop_mul436.8'}), (442, {'idx': 0, 'res': '$442load_global.9'}), (452, {'res': '$index_frac452.11'}), (454, {'func': '$442load_global.9', 'args': ['$index_frac452.11'], 'kw_names': None, 'res': '$454call.12'}), (462, {'value': '$454call.12'}), (464, {'res1': '$index_frac464.13', 'res2': '$offset464.14'}), (466, {'op': '-', 'lhs': '$index_frac464.13', 'rhs': '$offset464.14', 'res': '$binop_sub466.15'}), (470, {'value': '$binop_sub466.15'}), (472, {'idx': 3, 'res': '$472load_global.16'}), (482, {'res': '$n_orig482.18'}), (484, {'res': '$n484.19'}), (486, {'op': '-', 'lhs': '$n_orig482.18', 'rhs': '$n484.19', 'res': '$binop_sub486.20'}), (490, {'res': '$const490.21.3'}), (492, {'op': '-', 'lhs': '$binop_sub486.20', 'rhs': '$const490.21.3', 'res': '$binop_sub492.22'}), (496, {'res1': '$nwin496.23', 'res2': '$offset496.24'}), (498, {'op': '-', 'lhs': '$nwin496.23', 'rhs': '$offset496.24', 'res': '$binop_sub498.25'}), (502, {'res': '$index_step502.26'}), (504, {'op': '//', 'lhs': '$binop_sub498.25', 'rhs': '$index_step502.26', 'res': '$binop_floordiv504.27'}), (508, {'func': '$472load_global.16', 'args': ['$binop_sub492.22', '$binop_floordiv504.27'], 'kw_names': None, 'res': '$508call.28'}), (516, {'value': '$508call.28'}), (518, {'idx': 4, 'res': '$518load_global.29'}), (528, {'res': '$k_max528.31'}), (530, {'func': '$518load_global.29', 'args': ['$k_max528.31'], 'kw_names': None, 'res': '$530call.32'}), (538, {'value': '$530call.32', 'res': '$538get_iter.33'})), outgoing_phis={'$phi540.1': '$538get_iter.33'}, blockstack=(), active_try_block=None, outgoing_edgepushed={540: ('$phi422.0', '$538get_iter.33')})
2025-08-27 14:20:27,341 [DEBUG] numba.core.byteflow: block_infos State(pc_initial=540 nstack_initial=2):
AdaptBlockInfo(insts=((540, {'iterator': '$phi540.1', 'pair': '$540for_iter.2', 'indval': '$540for_iter.3', 'pred': '$540for_iter.4'}),), outgoing_phis={'$phi544.2': '$540for_iter.3'}, blockstack=(), active_try_block=None, outgoing_edgepushed={646: ('$phi540.0', '$phi540.1', '$540for_iter.3'), 544: ('$phi540.0', '$phi540.1', '$540for_iter.3')})
2025-08-27 14:20:27,341 [DEBUG] numba.core.byteflow: block_infos State(pc_initial=544 nstack_initial=3):
AdaptBlockInfo(insts=((544, {'value': '$phi544.2'}), (546, {'res1': '$interp_win546.3', 'res2': '$offset546.4'}), (548, {'res': '$k548.5'}), (550, {'res': '$index_step550.6'}), (552, {'op': '*', 'lhs': '$k548.5', 'rhs': '$index_step550.6', 'res': '$binop_mul552.7'}), (556, {'op': '+', 'lhs': '$offset546.4', 'rhs': '$binop_mul552.7', 'res': '$binop_add556.8'}), (560, {'index': '$binop_add556.8', 'target': '$interp_win546.3', 'res': '$560binary_subscr.9'}), (564, {'res1': '$eta564.10', 'res2': '$interp_delta564.11'}), (566, {'res': '$offset566.12'}), (568, {'res': '$k568.13'}), (570, {'res': '$index_step570.14'}), (572, {'op': '*', 'lhs': '$k568.13', 'rhs': '$index_step570.14', 'res': '$binop_mul572.15'}), (576, {'op': '+', 'lhs': '$offset566.12', 'rhs': '$binop_mul572.15', 'res': '$binop_add576.16'}), (580, {'index': '$binop_add576.16', 'target': '$interp_delta564.11', 'res': '$580binary_subscr.17'}), (584, {'op': '*', 'lhs': '$eta564.10', 'rhs': '$580binary_subscr.17', 'res': '$binop_mul584.18'}), (588, {'op': '+', 'lhs': '$560binary_subscr.9', 'rhs': '$binop_mul584.18', 'res': '$binop_add588.19'}), (592, {'value': '$binop_add588.19'}), (594, {'res': '$y594.20'}), (596, {'res': '$t596.21'}), (602, {'index': '$t596.21', 'target': '$y594.20', 'res': '$602binary_subscr.22'}), (606, {'res1': '$weight606.23', 'res2': '$x606.24'}), (608, {'res': '$n608.25'}), (610, {'res': '$k610.26'}), (612, {'op': '+', 'lhs': '$n608.25', 'rhs': '$k610.26', 'res': '$binop_add612.27'}), (616, {'res': '$const616.28.3'}), (618, {'op': '+', 'lhs': '$binop_add612.27', 'rhs': '$const616.28.3', 'res': '$binop_add618.29'}), (622, {'index': '$binop_add618.29', 'target': '$x606.24', 'res': '$622binary_subscr.30'}), (626, {'op': '*', 'lhs': '$weight606.23', 'rhs': '$622binary_subscr.30', 'res': '$binop_mul626.31'}), (630, {'op': '+=', 'lhs': '$602binary_subscr.22', 'rhs': '$binop_mul626.31', 'res': '$binop_iadd630.32'}), (638, {'target': '$y594.20', 'index': '$t596.21', 'value': '$binop_iadd630.32'}), (642, {})), outgoing_phis={}, blockstack=(), active_try_block=None, outgoing_edgepushed={540: ('$phi544.0', '$phi544.1')})
2025-08-27 14:20:27,341 [DEBUG] numba.core.byteflow: block_infos State(pc_initial=646 nstack_initial=3):
AdaptBlockInfo(insts=((650, {}),), outgoing_phis={}, blockstack=(), active_try_block=None, outgoing_edgepushed={170: ('$phi646.0',)})
2025-08-27 14:20:27,341 [DEBUG] numba.core.byteflow: block_infos State(pc_initial=654 nstack_initial=2):
AdaptBlockInfo(insts=((658, {'retval': '$const658.2', 'castval': '$658return_const.3'}),), outgoing_phis={}, blockstack=(), active_try_block=None, outgoing_edgepushed={})
2025-08-27 14:20:27,342 [DEBUG] numba.core.interpreter: label 0:
    x = arg(0, name=x)                       ['x']
    t_out = arg(1, name=t_out)               ['t_out']
    interp_win = arg(2, name=interp_win)     ['interp_win']
    interp_delta = arg(3, name=interp_delta) ['interp_delta']
    num_table = arg(4, name=num_table)       ['num_table']
    scale = arg(5, name=scale)               ['scale']
    y = arg(6, name=y)                       ['y']
    $4load_global.0 = global(int: <class 'int'>) ['$4load_global.0']
    $binop_mul16.4 = scale * num_table       ['$binop_mul16.4', 'num_table', 'scale']
    index_step = call $4load_global.0($binop_mul16.4, func=$4load_global.0, args=[Var($binop_mul16.4, interpn.py:9)], kws=(), vararg=None, varkwarg=None, target=None) ['$4load_global.0', '$binop_mul16.4', 'index_step']
    time_register = const(float, 0.0)        ['time_register']
    n = const(int, 0)                        ['n']
    frac = const(float, 0.0)                 ['frac']
    index_frac = const(float, 0.0)           ['index_frac']
    offset = const(int, 0)                   ['offset']
    eta = const(float, 0.0)                  ['eta']
    weight = const(float, 0.0)               ['weight']
    $60load_attr.14 = getattr(value=interp_win, attr=shape) ['$60load_attr.14', 'interp_win']
    $const80.15.2 = const(int, 0)            ['$const80.15.2']
    nwin = getitem(value=$60load_attr.14, index=$const80.15.2, fn=<built-in function getitem>) ['$60load_attr.14', '$const80.15.2', 'nwin']
    $90load_attr.18 = getattr(value=x, attr=shape) ['$90load_attr.18', 'x']
    $const110.19.2 = const(int, 0)           ['$const110.19.2']
    n_orig = getitem(value=$90load_attr.18, index=$const110.19.2, fn=<built-in function getitem>) ['$90load_attr.18', '$const110.19.2', 'n_orig']
    $120load_attr.22 = getattr(value=t_out, attr=shape) ['$120load_attr.22', 't_out']
    $const140.23.2 = const(int, 0)           ['$const140.23.2']
    n_out = getitem(value=$120load_attr.22, index=$const140.23.2, fn=<built-in function getitem>) ['$120load_attr.22', '$const140.23.2', 'n_out']
    $148load_global.25 = global(prange: <class 'numba.misc.special.prange'>) ['$148load_global.25']
    $160call.28 = call $148load_global.25(n_out, func=$148load_global.25, args=[Var(n_out, interpn.py:21)], kws=(), vararg=None, varkwarg=None, target=None) ['$148load_global.25', '$160call.28', 'n_out']
    $168get_iter.29 = getiter(value=$160call.28) ['$160call.28', '$168get_iter.29']
    $phi170.0 = $168get_iter.29              ['$168get_iter.29', '$phi170.0']
    jump 170                                 []
label 170:
    $170for_iter.1 = iternext(value=$phi170.0) ['$170for_iter.1', '$phi170.0']
    $170for_iter.2 = pair_first(value=$170for_iter.1) ['$170for_iter.1', '$170for_iter.2']
    $170for_iter.3 = pair_second(value=$170for_iter.1) ['$170for_iter.1', '$170for_iter.3']
    $phi174.1 = $170for_iter.2               ['$170for_iter.2', '$phi174.1']
    branch $170for_iter.3, 174, 654          ['$170for_iter.3']
label 174:
    t = $phi174.1                            ['$phi174.1', 't']
    time_register = getitem(value=t_out, index=t, fn=<built-in function getitem>) ['t', 't_out', 'time_register']
    $186load_global.5 = global(int: <class 'int'>) ['$186load_global.5']
    n = call $186load_global.5(time_register, func=$186load_global.5, args=[Var(time_register, interpn.py:10)], kws=(), vararg=None, varkwarg=None, target=None) ['$186load_global.5', 'n', 'time_register']
    $binop_sub212.12 = time_register - n     ['$binop_sub212.12', 'n', 'time_register']
    frac = scale * $binop_sub212.12          ['$binop_sub212.12', 'frac', 'scale']
    index_frac = frac * num_table            ['frac', 'index_frac', 'num_table']
    $230load_global.17 = global(int: <class 'int'>) ['$230load_global.17']
    offset = call $230load_global.17(index_frac, func=$230load_global.17, args=[Var(index_frac, interpn.py:14)], kws=(), vararg=None, varkwarg=None, target=None) ['$230load_global.17', 'index_frac', 'offset']
    eta = index_frac - offset                ['eta', 'index_frac', 'offset']
    $260load_global.24 = global(min: <built-in function min>) ['$260load_global.24']
    $const272.27.3 = const(int, 1)           ['$const272.27.3']
    $binop_add274.28 = n + $const272.27.3    ['$binop_add274.28', '$const272.27.3', 'n']
    $binop_sub280.31 = nwin - offset         ['$binop_sub280.31', 'nwin', 'offset']
    $binop_floordiv286.33 = $binop_sub280.31 // index_step ['$binop_floordiv286.33', '$binop_sub280.31', 'index_step']
    i_max = call $260load_global.24($binop_add274.28, $binop_floordiv286.33, func=$260load_global.24, args=[Var($binop_add274.28, interpn.py:40), Var($binop_floordiv286.33, interpn.py:40)], kws=(), vararg=None, varkwarg=None, target=None) ['$260load_global.24', '$binop_add274.28', '$binop_floordiv286.33', 'i_max']
    $300load_global.35 = global(range: <class 'range'>) ['$300load_global.35']
    $312call.38 = call $300load_global.35(i_max, func=$300load_global.35, args=[Var(i_max, interpn.py:40)], kws=(), vararg=None, varkwarg=None, target=None) ['$300load_global.35', '$312call.38', 'i_max']
    $320get_iter.39 = getiter(value=$312call.38) ['$312call.38', '$320get_iter.39']
    $phi322.1 = $320get_iter.39              ['$320get_iter.39', '$phi322.1']
    jump 322                                 []
label 322:
    $322for_iter.2 = iternext(value=$phi322.1) ['$322for_iter.2', '$phi322.1']
    $322for_iter.3 = pair_first(value=$322for_iter.2) ['$322for_iter.2', '$322for_iter.3']
    $322for_iter.4 = pair_second(value=$322for_iter.2) ['$322for_iter.2', '$322for_iter.4']
    $phi326.2 = $322for_iter.3               ['$322for_iter.3', '$phi326.2']
    branch $322for_iter.4, 326, 422          ['$322for_iter.4']
label 326:
    i = $phi326.2                            ['$phi326.2', 'i']
    $binop_mul334.7 = i * index_step         ['$binop_mul334.7', 'i', 'index_step']
    $binop_add338.8 = offset + $binop_mul334.7 ['$binop_add338.8', '$binop_mul334.7', 'offset']
    $342binary_subscr.9 = getitem(value=interp_win, index=$binop_add338.8, fn=<built-in function getitem>) ['$342binary_subscr.9', '$binop_add338.8', 'interp_win']
    $binop_mul354.15 = i * index_step        ['$binop_mul354.15', 'i', 'index_step']
    $binop_add358.16 = offset + $binop_mul354.15 ['$binop_add358.16', '$binop_mul354.15', 'offset']
    $362binary_subscr.17 = getitem(value=interp_delta, index=$binop_add358.16, fn=<built-in function getitem>) ['$362binary_subscr.17', '$binop_add358.16', 'interp_delta']
    $binop_mul366.18 = eta * $362binary_subscr.17 ['$362binary_subscr.17', '$binop_mul366.18', 'eta']
    weight = $342binary_subscr.9 + $binop_mul366.18 ['$342binary_subscr.9', '$binop_mul366.18', 'weight']
    $384binary_subscr.22 = getitem(value=y, index=t, fn=<built-in function getitem>) ['$384binary_subscr.22', 't', 'y']
    $binop_sub394.27 = n - i                 ['$binop_sub394.27', 'i', 'n']
    $398binary_subscr.28 = getitem(value=x, index=$binop_sub394.27, fn=<built-in function getitem>) ['$398binary_subscr.28', '$binop_sub394.27', 'x']
    $binop_mul402.29 = weight * $398binary_subscr.28 ['$398binary_subscr.28', '$binop_mul402.29', 'weight']
    $binop_iadd406.30 = inplace_binop(fn=<built-in function iadd>, immutable_fn=<built-in function add>, lhs=$384binary_subscr.22, rhs=$binop_mul402.29, static_lhs=Undefined, static_rhs=Undefined) ['$384binary_subscr.22', '$binop_iadd406.30', '$binop_mul402.29']
    y[t] = $binop_iadd406.30                 ['$binop_iadd406.30', 't', 'y']
    jump 322                                 []
label 422:
    $binop_sub428.5 = scale - frac           ['$binop_sub428.5', 'frac', 'scale']
    frac = $binop_sub428.5                   ['$binop_sub428.5', 'frac']
    index_frac = frac * num_table            ['frac', 'index_frac', 'num_table']
    $442load_global.9 = global(int: <class 'int'>) ['$442load_global.9']
    offset = call $442load_global.9(index_frac, func=$442load_global.9, args=[Var(index_frac, interpn.py:14)], kws=(), vararg=None, varkwarg=None, target=None) ['$442load_global.9', 'index_frac', 'offset']
    eta = index_frac - offset                ['eta', 'index_frac', 'offset']
    $472load_global.16 = global(min: <built-in function min>) ['$472load_global.16']
    $binop_sub486.20 = n_orig - n            ['$binop_sub486.20', 'n', 'n_orig']
    $const490.21.3 = const(int, 1)           ['$const490.21.3']
    $binop_sub492.22 = $binop_sub486.20 - $const490.21.3 ['$binop_sub486.20', '$binop_sub492.22', '$const490.21.3']
    $binop_sub498.25 = nwin - offset         ['$binop_sub498.25', 'nwin', 'offset']
    $binop_floordiv504.27 = $binop_sub498.25 // index_step ['$binop_floordiv504.27', '$binop_sub498.25', 'index_step']
    k_max = call $472load_global.16($binop_sub492.22, $binop_floordiv504.27, func=$472load_global.16, args=[Var($binop_sub492.22, interpn.py:60), Var($binop_floordiv504.27, interpn.py:60)], kws=(), vararg=None, varkwarg=None, target=None) ['$472load_global.16', '$binop_floordiv504.27', '$binop_sub492.22', 'k_max']
    $518load_global.29 = global(range: <class 'range'>) ['$518load_global.29']
    $530call.32 = call $518load_global.29(k_max, func=$518load_global.29, args=[Var(k_max, interpn.py:60)], kws=(), vararg=None, varkwarg=None, target=None) ['$518load_global.29', '$530call.32', 'k_max']
    $538get_iter.33 = getiter(value=$530call.32) ['$530call.32', '$538get_iter.33']
    $phi540.1 = $538get_iter.33              ['$538get_iter.33', '$phi540.1']
    jump 540                                 []
label 540:
    $540for_iter.2 = iternext(value=$phi540.1) ['$540for_iter.2', '$phi540.1']
    $540for_iter.3 = pair_first(value=$540for_iter.2) ['$540for_iter.2', '$540for_iter.3']
    $540for_iter.4 = pair_second(value=$540for_iter.2) ['$540for_iter.2', '$540for_iter.4']
    $phi544.2 = $540for_iter.3               ['$540for_iter.3', '$phi544.2']
    branch $540for_iter.4, 544, 646          ['$540for_iter.4']
label 544:
    k = $phi544.2                            ['$phi544.2', 'k']
    $binop_mul552.7 = k * index_step         ['$binop_mul552.7', 'index_step', 'k']
    $binop_add556.8 = offset + $binop_mul552.7 ['$binop_add556.8', '$binop_mul552.7', 'offset']
    $560binary_subscr.9 = getitem(value=interp_win, index=$binop_add556.8, fn=<built-in function getitem>) ['$560binary_subscr.9', '$binop_add556.8', 'interp_win']
    $binop_mul572.15 = k * index_step        ['$binop_mul572.15', 'index_step', 'k']
    $binop_add576.16 = offset + $binop_mul572.15 ['$binop_add576.16', '$binop_mul572.15', 'offset']
    $580binary_subscr.17 = getitem(value=interp_delta, index=$binop_add576.16, fn=<built-in function getitem>) ['$580binary_subscr.17', '$binop_add576.16', 'interp_delta']
    $binop_mul584.18 = eta * $580binary_subscr.17 ['$580binary_subscr.17', '$binop_mul584.18', 'eta']
    weight = $560binary_subscr.9 + $binop_mul584.18 ['$560binary_subscr.9', '$binop_mul584.18', 'weight']
    $602binary_subscr.22 = getitem(value=y, index=t, fn=<built-in function getitem>) ['$602binary_subscr.22', 't', 'y']
    $binop_add612.27 = n + k                 ['$binop_add612.27', 'k', 'n']
    $const616.28.3 = const(int, 1)           ['$const616.28.3']
    $binop_add618.29 = $binop_add612.27 + $const616.28.3 ['$binop_add612.27', '$binop_add618.29', '$const616.28.3']
    $622binary_subscr.30 = getitem(value=x, index=$binop_add618.29, fn=<built-in function getitem>) ['$622binary_subscr.30', '$binop_add618.29', 'x']
    $binop_mul626.31 = weight * $622binary_subscr.30 ['$622binary_subscr.30', '$binop_mul626.31', 'weight']
    $binop_iadd630.32 = inplace_binop(fn=<built-in function iadd>, immutable_fn=<built-in function add>, lhs=$602binary_subscr.22, rhs=$binop_mul626.31, static_lhs=Undefined, static_rhs=Undefined) ['$602binary_subscr.22', '$binop_iadd630.32', '$binop_mul626.31']
    y[t] = $binop_iadd630.32                 ['$binop_iadd630.32', 't', 'y']
    jump 540                                 []
label 646:
    jump 170                                 []
label 654:
    $const658.2 = const(NoneType, None)      ['$const658.2']
    $658return_const.3 = cast(value=$const658.2) ['$658return_const.3', '$const658.2']
    return $658return_const.3                ['$658return_const.3']

2025-08-27 14:20:27,346 [DEBUG] numba.core.ssa: ==== SSA block analysis pass on 0
2025-08-27 14:20:27,346 [DEBUG] numba.core.ssa: Running <numba.core.ssa._GatherDefsHandler object at 0x32e8b1f90>
2025-08-27 14:20:27,346 [DEBUG] numba.core.ssa: on stmt: x = arg(0, name=x)
2025-08-27 14:20:27,346 [DEBUG] numba.core.ssa: on stmt: t_out = arg(1, name=t_out)
2025-08-27 14:20:27,346 [DEBUG] numba.core.ssa: on stmt: interp_win = arg(2, name=interp_win)
2025-08-27 14:20:27,347 [DEBUG] numba.core.ssa: on stmt: interp_delta = arg(3, name=interp_delta)
2025-08-27 14:20:27,347 [DEBUG] numba.core.ssa: on stmt: num_table = arg(4, name=num_table)
2025-08-27 14:20:27,347 [DEBUG] numba.core.ssa: on stmt: scale = arg(5, name=scale)
2025-08-27 14:20:27,347 [DEBUG] numba.core.ssa: on stmt: y = arg(6, name=y)
2025-08-27 14:20:27,347 [DEBUG] numba.core.ssa: on stmt: $4load_global.0 = global(int: <class 'int'>)
2025-08-27 14:20:27,347 [DEBUG] numba.core.ssa: on stmt: $binop_mul16.4 = scale * num_table
2025-08-27 14:20:27,347 [DEBUG] numba.core.ssa: on stmt: index_step = call $4load_global.0($binop_mul16.4, func=$4load_global.0, args=[Var($binop_mul16.4, interpn.py:9)], kws=(), vararg=None, varkwarg=None, target=None)
2025-08-27 14:20:27,347 [DEBUG] numba.core.ssa: on stmt: time_register = const(float, 0.0)
2025-08-27 14:20:27,347 [DEBUG] numba.core.ssa: on stmt: n = const(int, 0)
2025-08-27 14:20:27,347 [DEBUG] numba.core.ssa: on stmt: frac = const(float, 0.0)
2025-08-27 14:20:27,347 [DEBUG] numba.core.ssa: on stmt: index_frac = const(float, 0.0)
2025-08-27 14:20:27,347 [DEBUG] numba.core.ssa: on stmt: offset = const(int, 0)
2025-08-27 14:20:27,347 [DEBUG] numba.core.ssa: on stmt: eta = const(float, 0.0)
2025-08-27 14:20:27,347 [DEBUG] numba.core.ssa: on stmt: weight = const(float, 0.0)
2025-08-27 14:20:27,347 [DEBUG] numba.core.ssa: on stmt: $60load_attr.14 = getattr(value=interp_win, attr=shape)
2025-08-27 14:20:27,347 [DEBUG] numba.core.ssa: on stmt: $const80.15.2 = const(int, 0)
2025-08-27 14:20:27,347 [DEBUG] numba.core.ssa: on stmt: nwin = static_getitem(value=$60load_attr.14, index=0, index_var=$const80.15.2, fn=<built-in function getitem>)
2025-08-27 14:20:27,347 [DEBUG] numba.core.ssa: on stmt: $90load_attr.18 = getattr(value=x, attr=shape)
2025-08-27 14:20:27,347 [DEBUG] numba.core.ssa: on stmt: $const110.19.2 = const(int, 0)
2025-08-27 14:20:27,347 [DEBUG] numba.core.ssa: on stmt: n_orig = static_getitem(value=$90load_attr.18, index=0, index_var=$const110.19.2, fn=<built-in function getitem>)
2025-08-27 14:20:27,347 [DEBUG] numba.core.ssa: on stmt: $120load_attr.22 = getattr(value=t_out, attr=shape)
2025-08-27 14:20:27,347 [DEBUG] numba.core.ssa: on stmt: $const140.23.2 = const(int, 0)
2025-08-27 14:20:27,347 [DEBUG] numba.core.ssa: on stmt: n_out = static_getitem(value=$120load_attr.22, index=0, index_var=$const140.23.2, fn=<built-in function getitem>)
2025-08-27 14:20:27,347 [DEBUG] numba.core.ssa: on stmt: $148load_global.25 = global(prange: <class 'numba.misc.special.prange'>)
2025-08-27 14:20:27,347 [DEBUG] numba.core.ssa: on stmt: $160call.28 = call $148load_global.25(n_out, func=$148load_global.25, args=[Var(n_out, interpn.py:21)], kws=(), vararg=None, varkwarg=None, target=None)
2025-08-27 14:20:27,347 [DEBUG] numba.core.ssa: on stmt: $168get_iter.29 = getiter(value=$160call.28)
2025-08-27 14:20:27,347 [DEBUG] numba.core.ssa: on stmt: $phi170.0 = $168get_iter.29
2025-08-27 14:20:27,347 [DEBUG] numba.core.ssa: on stmt: jump 170
2025-08-27 14:20:27,347 [DEBUG] numba.core.ssa: ==== SSA block analysis pass on 170
2025-08-27 14:20:27,347 [DEBUG] numba.core.ssa: Running <numba.core.ssa._GatherDefsHandler object at 0x32e8b1f90>
2025-08-27 14:20:27,347 [DEBUG] numba.core.ssa: on stmt: $170for_iter.1 = iternext(value=$phi170.0)
2025-08-27 14:20:27,347 [DEBUG] numba.core.ssa: on stmt: $170for_iter.2 = pair_first(value=$170for_iter.1)
2025-08-27 14:20:27,347 [DEBUG] numba.core.ssa: on stmt: $170for_iter.3 = pair_second(value=$170for_iter.1)
2025-08-27 14:20:27,347 [DEBUG] numba.core.ssa: on stmt: $phi174.1 = $170for_iter.2
2025-08-27 14:20:27,347 [DEBUG] numba.core.ssa: on stmt: branch $170for_iter.3, 174, 654
2025-08-27 14:20:27,347 [DEBUG] numba.core.ssa: ==== SSA block analysis pass on 174
2025-08-27 14:20:27,347 [DEBUG] numba.core.ssa: Running <numba.core.ssa._GatherDefsHandler object at 0x32e8b1f90>
2025-08-27 14:20:27,347 [DEBUG] numba.core.ssa: on stmt: t = $phi174.1
2025-08-27 14:20:27,347 [DEBUG] numba.core.ssa: on stmt: time_register = getitem(value=t_out, index=t, fn=<built-in function getitem>)
2025-08-27 14:20:27,347 [DEBUG] numba.core.ssa: on stmt: $186load_global.5 = global(int: <class 'int'>)
2025-08-27 14:20:27,347 [DEBUG] numba.core.ssa: on stmt: n = call $186load_global.5(time_register, func=$186load_global.5, args=[Var(time_register, interpn.py:10)], kws=(), vararg=None, varkwarg=None, target=None)
2025-08-27 14:20:27,347 [DEBUG] numba.core.ssa: on stmt: $binop_sub212.12 = time_register - n
2025-08-27 14:20:27,347 [DEBUG] numba.core.ssa: on stmt: frac = scale * $binop_sub212.12
2025-08-27 14:20:27,347 [DEBUG] numba.core.ssa: on stmt: index_frac = frac * num_table
2025-08-27 14:20:27,347 [DEBUG] numba.core.ssa: on stmt: $230load_global.17 = global(int: <class 'int'>)
2025-08-27 14:20:27,347 [DEBUG] numba.core.ssa: on stmt: offset = call $230load_global.17(index_frac, func=$230load_global.17, args=[Var(index_frac, interpn.py:14)], kws=(), vararg=None, varkwarg=None, target=None)
2025-08-27 14:20:27,347 [DEBUG] numba.core.ssa: on stmt: eta = index_frac - offset
2025-08-27 14:20:27,347 [DEBUG] numba.core.ssa: on stmt: $260load_global.24 = global(min: <built-in function min>)
2025-08-27 14:20:27,347 [DEBUG] numba.core.ssa: on stmt: $const272.27.3 = const(int, 1)
2025-08-27 14:20:27,347 [DEBUG] numba.core.ssa: on stmt: $binop_add274.28 = n + $const272.27.3
2025-08-27 14:20:27,347 [DEBUG] numba.core.ssa: on stmt: $binop_sub280.31 = nwin - offset
2025-08-27 14:20:27,347 [DEBUG] numba.core.ssa: on stmt: $binop_floordiv286.33 = $binop_sub280.31 // index_step
2025-08-27 14:20:27,347 [DEBUG] numba.core.ssa: on stmt: i_max = call $260load_global.24($binop_add274.28, $binop_floordiv286.33, func=$260load_global.24, args=[Var($binop_add274.28, interpn.py:40), Var($binop_floordiv286.33, interpn.py:40)], kws=(), vararg=None, varkwarg=None, target=None)
2025-08-27 14:20:27,347 [DEBUG] numba.core.ssa: on stmt: $300load_global.35 = global(range: <class 'range'>)
2025-08-27 14:20:27,347 [DEBUG] numba.core.ssa: on stmt: $312call.38 = call $300load_global.35(i_max, func=$300load_global.35, args=[Var(i_max, interpn.py:40)], kws=(), vararg=None, varkwarg=None, target=None)
2025-08-27 14:20:27,347 [DEBUG] numba.core.ssa: on stmt: $320get_iter.39 = getiter(value=$312call.38)
2025-08-27 14:20:27,347 [DEBUG] numba.core.ssa: on stmt: $phi322.1 = $320get_iter.39
2025-08-27 14:20:27,347 [DEBUG] numba.core.ssa: on stmt: jump 322
2025-08-27 14:20:27,348 [DEBUG] numba.core.ssa: ==== SSA block analysis pass on 322
2025-08-27 14:20:27,348 [DEBUG] numba.core.ssa: Running <numba.core.ssa._GatherDefsHandler object at 0x32e8b1f90>
2025-08-27 14:20:27,348 [DEBUG] numba.core.ssa: on stmt: $322for_iter.2 = iternext(value=$phi322.1)
2025-08-27 14:20:27,348 [DEBUG] numba.core.ssa: on stmt: $322for_iter.3 = pair_first(value=$322for_iter.2)
2025-08-27 14:20:27,348 [DEBUG] numba.core.ssa: on stmt: $322for_iter.4 = pair_second(value=$322for_iter.2)
2025-08-27 14:20:27,348 [DEBUG] numba.core.ssa: on stmt: $phi326.2 = $322for_iter.3
2025-08-27 14:20:27,348 [DEBUG] numba.core.ssa: on stmt: branch $322for_iter.4, 326, 422
2025-08-27 14:20:27,348 [DEBUG] numba.core.ssa: ==== SSA block analysis pass on 326
2025-08-27 14:20:27,348 [DEBUG] numba.core.ssa: Running <numba.core.ssa._GatherDefsHandler object at 0x32e8b1f90>
2025-08-27 14:20:27,348 [DEBUG] numba.core.ssa: on stmt: i = $phi326.2
2025-08-27 14:20:27,348 [DEBUG] numba.core.ssa: on stmt: $binop_mul334.7 = i * index_step
2025-08-27 14:20:27,348 [DEBUG] numba.core.ssa: on stmt: $binop_add338.8 = offset + $binop_mul334.7
2025-08-27 14:20:27,348 [DEBUG] numba.core.ssa: on stmt: $342binary_subscr.9 = getitem(value=interp_win, index=$binop_add338.8, fn=<built-in function getitem>)
2025-08-27 14:20:27,348 [DEBUG] numba.core.ssa: on stmt: $binop_mul354.15 = i * index_step
2025-08-27 14:20:27,348 [DEBUG] numba.core.ssa: on stmt: $binop_add358.16 = offset + $binop_mul354.15
2025-08-27 14:20:27,348 [DEBUG] numba.core.ssa: on stmt: $362binary_subscr.17 = getitem(value=interp_delta, index=$binop_add358.16, fn=<built-in function getitem>)
2025-08-27 14:20:27,348 [DEBUG] numba.core.ssa: on stmt: $binop_mul366.18 = eta * $362binary_subscr.17
2025-08-27 14:20:27,348 [DEBUG] numba.core.ssa: on stmt: weight = $342binary_subscr.9 + $binop_mul366.18
2025-08-27 14:20:27,348 [DEBUG] numba.core.ssa: on stmt: $384binary_subscr.22 = getitem(value=y, index=t, fn=<built-in function getitem>)
2025-08-27 14:20:27,348 [DEBUG] numba.core.ssa: on stmt: $binop_sub394.27 = n - i
2025-08-27 14:20:27,348 [DEBUG] numba.core.ssa: on stmt: $398binary_subscr.28 = getitem(value=x, index=$binop_sub394.27, fn=<built-in function getitem>)
2025-08-27 14:20:27,348 [DEBUG] numba.core.ssa: on stmt: $binop_mul402.29 = weight * $398binary_subscr.28
2025-08-27 14:20:27,348 [DEBUG] numba.core.ssa: on stmt: $binop_iadd406.30 = inplace_binop(fn=<built-in function iadd>, immutable_fn=<built-in function add>, lhs=$384binary_subscr.22, rhs=$binop_mul402.29, static_lhs=Undefined, static_rhs=Undefined)
2025-08-27 14:20:27,348 [DEBUG] numba.core.ssa: on stmt: y[t] = $binop_iadd406.30
2025-08-27 14:20:27,348 [DEBUG] numba.core.ssa: on stmt: jump 322
2025-08-27 14:20:27,348 [DEBUG] numba.core.ssa: ==== SSA block analysis pass on 422
2025-08-27 14:20:27,348 [DEBUG] numba.core.ssa: Running <numba.core.ssa._GatherDefsHandler object at 0x32e8b1f90>
2025-08-27 14:20:27,348 [DEBUG] numba.core.ssa: on stmt: $binop_sub428.5 = scale - frac
2025-08-27 14:20:27,348 [DEBUG] numba.core.ssa: on stmt: frac = $binop_sub428.5
2025-08-27 14:20:27,348 [DEBUG] numba.core.ssa: on stmt: index_frac = frac * num_table
2025-08-27 14:20:27,348 [DEBUG] numba.core.ssa: on stmt: $442load_global.9 = global(int: <class 'int'>)
2025-08-27 14:20:27,348 [DEBUG] numba.core.ssa: on stmt: offset = call $442load_global.9(index_frac, func=$442load_global.9, args=[Var(index_frac, interpn.py:14)], kws=(), vararg=None, varkwarg=None, target=None)
2025-08-27 14:20:27,348 [DEBUG] numba.core.ssa: on stmt: eta = index_frac - offset
2025-08-27 14:20:27,348 [DEBUG] numba.core.ssa: on stmt: $472load_global.16 = global(min: <built-in function min>)
2025-08-27 14:20:27,348 [DEBUG] numba.core.ssa: on stmt: $binop_sub486.20 = n_orig - n
2025-08-27 14:20:27,348 [DEBUG] numba.core.ssa: on stmt: $const490.21.3 = const(int, 1)
2025-08-27 14:20:27,349 [DEBUG] numba.core.ssa: on stmt: $binop_sub492.22 = $binop_sub486.20 - $const490.21.3
2025-08-27 14:20:27,349 [DEBUG] numba.core.ssa: on stmt: $binop_sub498.25 = nwin - offset
2025-08-27 14:20:27,349 [DEBUG] numba.core.ssa: on stmt: $binop_floordiv504.27 = $binop_sub498.25 // index_step
2025-08-27 14:20:27,349 [DEBUG] numba.core.ssa: on stmt: k_max = call $472load_global.16($binop_sub492.22, $binop_floordiv504.27, func=$472load_global.16, args=[Var($binop_sub492.22, interpn.py:60), Var($binop_floordiv504.27, interpn.py:60)], kws=(), vararg=None, varkwarg=None, target=None)
2025-08-27 14:20:27,349 [DEBUG] numba.core.ssa: on stmt: $518load_global.29 = global(range: <class 'range'>)
2025-08-27 14:20:27,349 [DEBUG] numba.core.ssa: on stmt: $530call.32 = call $518load_global.29(k_max, func=$518load_global.29, args=[Var(k_max, interpn.py:60)], kws=(), vararg=None, varkwarg=None, target=None)
2025-08-27 14:20:27,349 [DEBUG] numba.core.ssa: on stmt: $538get_iter.33 = getiter(value=$530call.32)
2025-08-27 14:20:27,349 [DEBUG] numba.core.ssa: on stmt: $phi540.1 = $538get_iter.33
2025-08-27 14:20:27,349 [DEBUG] numba.core.ssa: on stmt: jump 540
2025-08-27 14:20:27,349 [DEBUG] numba.core.ssa: ==== SSA block analysis pass on 540
2025-08-27 14:20:27,349 [DEBUG] numba.core.ssa: Running <numba.core.ssa._GatherDefsHandler object at 0x32e8b1f90>
2025-08-27 14:20:27,349 [DEBUG] numba.core.ssa: on stmt: $540for_iter.2 = iternext(value=$phi540.1)
2025-08-27 14:20:27,349 [DEBUG] numba.core.ssa: on stmt: $540for_iter.3 = pair_first(value=$540for_iter.2)
2025-08-27 14:20:27,349 [DEBUG] numba.core.ssa: on stmt: $540for_iter.4 = pair_second(value=$540for_iter.2)
2025-08-27 14:20:27,349 [DEBUG] numba.core.ssa: on stmt: $phi544.2 = $540for_iter.3
2025-08-27 14:20:27,349 [DEBUG] numba.core.ssa: on stmt: branch $540for_iter.4, 544, 646
2025-08-27 14:20:27,349 [DEBUG] numba.core.ssa: ==== SSA block analysis pass on 544
2025-08-27 14:20:27,349 [DEBUG] numba.core.ssa: Running <numba.core.ssa._GatherDefsHandler object at 0x32e8b1f90>
2025-08-27 14:20:27,349 [DEBUG] numba.core.ssa: on stmt: k = $phi544.2
2025-08-27 14:20:27,349 [DEBUG] numba.core.ssa: on stmt: $binop_mul552.7 = k * index_step
2025-08-27 14:20:27,349 [DEBUG] numba.core.ssa: on stmt: $binop_add556.8 = offset + $binop_mul552.7
2025-08-27 14:20:27,349 [DEBUG] numba.core.ssa: on stmt: $560binary_subscr.9 = getitem(value=interp_win, index=$binop_add556.8, fn=<built-in function getitem>)
2025-08-27 14:20:27,349 [DEBUG] numba.core.ssa: on stmt: $binop_mul572.15 = k * index_step
2025-08-27 14:20:27,349 [DEBUG] numba.core.ssa: on stmt: $binop_add576.16 = offset + $binop_mul572.15
2025-08-27 14:20:27,349 [DEBUG] numba.core.ssa: on stmt: $580binary_subscr.17 = getitem(value=interp_delta, index=$binop_add576.16, fn=<built-in function getitem>)
2025-08-27 14:20:27,349 [DEBUG] numba.core.ssa: on stmt: $binop_mul584.18 = eta * $580binary_subscr.17
2025-08-27 14:20:27,349 [DEBUG] numba.core.ssa: on stmt: weight = $560binary_subscr.9 + $binop_mul584.18
2025-08-27 14:20:27,349 [DEBUG] numba.core.ssa: on stmt: $602binary_subscr.22 = getitem(value=y, index=t, fn=<built-in function getitem>)
2025-08-27 14:20:27,349 [DEBUG] numba.core.ssa: on stmt: $binop_add612.27 = n + k
2025-08-27 14:20:27,349 [DEBUG] numba.core.ssa: on stmt: $const616.28.3 = const(int, 1)
2025-08-27 14:20:27,349 [DEBUG] numba.core.ssa: on stmt: $binop_add618.29 = $binop_add612.27 + $const616.28.3
2025-08-27 14:20:27,349 [DEBUG] numba.core.ssa: on stmt: $622binary_subscr.30 = getitem(value=x, index=$binop_add618.29, fn=<built-in function getitem>)
2025-08-27 14:20:27,349 [DEBUG] numba.core.ssa: on stmt: $binop_mul626.31 = weight * $622binary_subscr.30
2025-08-27 14:20:27,349 [DEBUG] numba.core.ssa: on stmt: $binop_iadd630.32 = inplace_binop(fn=<built-in function iadd>, immutable_fn=<built-in function add>, lhs=$602binary_subscr.22, rhs=$binop_mul626.31, static_lhs=Undefined, static_rhs=Undefined)
2025-08-27 14:20:27,349 [DEBUG] numba.core.ssa: on stmt: y[t] = $binop_iadd630.32
2025-08-27 14:20:27,349 [DEBUG] numba.core.ssa: on stmt: jump 540
2025-08-27 14:20:27,349 [DEBUG] numba.core.ssa: ==== SSA block analysis pass on 646
2025-08-27 14:20:27,349 [DEBUG] numba.core.ssa: Running <numba.core.ssa._GatherDefsHandler object at 0x32e8b1f90>
2025-08-27 14:20:27,349 [DEBUG] numba.core.ssa: on stmt: jump 170
2025-08-27 14:20:27,349 [DEBUG] numba.core.ssa: ==== SSA block analysis pass on 654
2025-08-27 14:20:27,349 [DEBUG] numba.core.ssa: Running <numba.core.ssa._GatherDefsHandler object at 0x32e8b1f90>
2025-08-27 14:20:27,349 [DEBUG] numba.core.ssa: on stmt: $const658.2 = const(NoneType, None)
2025-08-27 14:20:27,349 [DEBUG] numba.core.ssa: on stmt: $658return_const.3 = cast(value=$const658.2)
2025-08-27 14:20:27,349 [DEBUG] numba.core.ssa: on stmt: return $658return_const.3
2025-08-27 14:20:27,349 [DEBUG] numba.core.ssa: defs defaultdict(<class 'list'>,
            {'$120load_attr.22': [(<numba.core.ir.Assign object at 0x32e9ed250>,
                                   0)],
             '$148load_global.25': [(<numba.core.ir.Assign object at 0x32e9ed5b0>,
                                     0)],
             '$160call.28': [(<numba.core.ir.Assign object at 0x32e9ed790>, 0)],
             '$168get_iter.29': [(<numba.core.ir.Assign object at 0x32e9ed8b0>,
                                  0)],
             '$170for_iter.1': [(<numba.core.ir.Assign object at 0x32e967fb0>,
                                 170)],
             '$170for_iter.2': [(<numba.core.ir.Assign object at 0x32e9ec470>,
                                 170)],
             '$170for_iter.3': [(<numba.core.ir.Assign object at 0x32e9ec830>,
                                 170)],
             '$186load_global.5': [(<numba.core.ir.Assign object at 0x32e9edbb0>,
                                    174)],
             '$230load_global.17': [(<numba.core.ir.Assign object at 0x32e9ee690>,
                                     174)],
             '$260load_global.24': [(<numba.core.ir.Assign object at 0x32e9eec90>,
                                     174)],
             '$300load_global.35': [(<numba.core.ir.Assign object at 0x32e9ef650>,
                                     174)],
             '$312call.38': [(<numba.core.ir.Assign object at 0x32e9ef830>,
                              174)],
             '$320get_iter.39': [(<numba.core.ir.Assign object at 0x32e9ef950>,
                                  174)],
             '$322for_iter.2': [(<numba.core.ir.Assign object at 0x32e9eddf0>,
                                 322)],
             '$322for_iter.3': [(<numba.core.ir.Assign object at 0x32e9ee030>,
                                 322)],
             '$322for_iter.4': [(<numba.core.ir.Assign object at 0x32e9ee450>,
                                 322)],
             '$342binary_subscr.9': [(<numba.core.ir.Assign object at 0x32e9efcb0>,
                                      326)],
             '$362binary_subscr.17': [(<numba.core.ir.Assign object at 0x32e9f8410>,
                                       326)],
             '$384binary_subscr.22': [(<numba.core.ir.Assign object at 0x32e9f8950>,
                                       326)],
             '$398binary_subscr.28': [(<numba.core.ir.Assign object at 0x32e9f8e90>,
                                       326)],
             '$442load_global.9': [(<numba.core.ir.Assign object at 0x32e9f9190>,
                                    422)],
             '$472load_global.16': [(<numba.core.ir.Assign object at 0x32e9f9790>,
                                     422)],
             '$4load_global.0': [(<numba.core.ir.Assign object at 0x32e967830>,
                                  0)],
             '$518load_global.29': [(<numba.core.ir.Assign object at 0x32e9fa330>,
                                     422)],
             '$530call.32': [(<numba.core.ir.Assign object at 0x32e9fa510>,
                              422)],
             '$538get_iter.33': [(<numba.core.ir.Assign object at 0x32e9fa630>,
                                  422)],
             '$540for_iter.2': [(<numba.core.ir.Assign object at 0x32e9f8c50>,
                                 540)],
             '$540for_iter.3': [(<numba.core.ir.Assign object at 0x32e9f9490>,
                                 540)],
             '$540for_iter.4': [(<numba.core.ir.Assign object at 0x32e9f9850>,
                                 540)],
             '$560binary_subscr.9': [(<numba.core.ir.Assign object at 0x32e9fabd0>,
                                      544)],
             '$580binary_subscr.17': [(<numba.core.ir.Assign object at 0x32e9fb2f0>,
                                       544)],
             '$602binary_subscr.22': [(<numba.core.ir.Assign object at 0x32e9fb830>,
                                       544)],
             '$60load_attr.14': [(<numba.core.ir.Assign object at 0x32e9ec950>,
                                  0)],
             '$622binary_subscr.30': [(<numba.core.ir.Assign object at 0x32e9fbf50>,
                                       544)],
             '$658return_const.3': [(<numba.core.ir.Assign object at 0x32e9fad50>,
                                     654)],
             '$90load_attr.18': [(<numba.core.ir.Assign object at 0x32e9ecdd0>,
                                  0)],
             '$binop_add274.28': [(<numba.core.ir.Assign object at 0x32e9eef30>,
                                   174)],
             '$binop_add338.8': [(<numba.core.ir.Assign object at 0x32e9efb90>,
                                  326)],
             '$binop_add358.16': [(<numba.core.ir.Assign object at 0x32e9f82f0>,
                                   326)],
             '$binop_add556.8': [(<numba.core.ir.Assign object at 0x32e9faab0>,
                                  544)],
             '$binop_add576.16': [(<numba.core.ir.Assign object at 0x32e9fb1d0>,
                                   544)],
             '$binop_add612.27': [(<numba.core.ir.Assign object at 0x32e9fbc50>,
                                   544)],
             '$binop_add618.29': [(<numba.core.ir.Assign object at 0x32e9fbe30>,
                                   544)],
             '$binop_floordiv286.33': [(<numba.core.ir.Assign object at 0x32e9ef3b0>,
                                        174)],
             '$binop_floordiv504.27': [(<numba.core.ir.Assign object at 0x32e9fa090>,
                                        422)],
             '$binop_iadd406.30': [(<numba.core.ir.Assign object at 0x32e9f90d0>,
                                    326)],
             '$binop_iadd630.32': [(<numba.core.ir.Assign object at 0x32ea041d0>,
                                    544)],
             '$binop_mul16.4': [(<numba.core.ir.Assign object at 0x32e967ad0>,
                                 0)],
             '$binop_mul334.7': [(<numba.core.ir.Assign object at 0x32e9efa70>,
                                  326)],
             '$binop_mul354.15': [(<numba.core.ir.Assign object at 0x32e9f81d0>,
                                   326)],
             '$binop_mul366.18': [(<numba.core.ir.Assign object at 0x32e9f8530>,
                                   326)],
             '$binop_mul402.29': [(<numba.core.ir.Assign object at 0x32e9f8fb0>,
                                   326)],
             '$binop_mul552.7': [(<numba.core.ir.Assign object at 0x32e9fa990>,
                                  544)],
             '$binop_mul572.15': [(<numba.core.ir.Assign object at 0x32e9fb0b0>,
                                   544)],
             '$binop_mul584.18': [(<numba.core.ir.Assign object at 0x32e9fb410>,
                                   544)],
             '$binop_mul626.31': [(<numba.core.ir.Assign object at 0x32ea040b0>,
                                   544)],
             '$binop_sub212.12': [(<numba.core.ir.Assign object at 0x32e9ee150>,
                                   174)],
             '$binop_sub280.31': [(<numba.core.ir.Assign object at 0x32e9ef1d0>,
                                   174)],
             '$binop_sub394.27': [(<numba.core.ir.Assign object at 0x32e9f8d70>,
                                   326)],
             '$binop_sub428.5': [(<numba.core.ir.Assign object at 0x32e9efef0>,
                                  422)],
             '$binop_sub486.20': [(<numba.core.ir.Assign object at 0x32e9f9a30>,
                                   422)],
             '$binop_sub492.22': [(<numba.core.ir.Assign object at 0x32e9f9c10>,
                                   422)],
             '$binop_sub498.25': [(<numba.core.ir.Assign object at 0x32e9f9eb0>,
                                   422)],
             '$const110.19.2': [(<numba.core.ir.Assign object at 0x32e9ece90>,
                                 0)],
             '$const140.23.2': [(<numba.core.ir.Assign object at 0x32e9ed310>,
                                 0)],
             '$const272.27.3': [(<numba.core.ir.Assign object at 0x32e9eee10>,
                                 174)],
             '$const490.21.3': [(<numba.core.ir.Assign object at 0x32e9f9af0>,
                                 422)],
             '$const616.28.3': [(<numba.core.ir.Assign object at 0x32e9fbd10>,
                                 544)],
             '$const658.2': [(<numba.core.ir.Assign object at 0x32e9fa7b0>,
                              654)],
             '$const80.15.2': [(<numba.core.ir.Assign object at 0x32e9eca10>,
                                0)],
             '$phi170.0': [(<numba.core.ir.Assign object at 0x32e9679b0>, 0)],
             '$phi174.1': [(<numba.core.ir.Assign object at 0x32e9eccb0>, 170)],
             '$phi322.1': [(<numba.core.ir.Assign object at 0x32e9ed970>, 174)],
             '$phi326.2': [(<numba.core.ir.Assign object at 0x32e9ee750>, 322)],
             '$phi540.1': [(<numba.core.ir.Assign object at 0x32e9ef710>, 422)],
             '$phi544.2': [(<numba.core.ir.Assign object at 0x32e9f9cd0>, 540)],
             'eta': [(<numba.core.ir.Assign object at 0x32e9ec530>, 0),
                     (<numba.core.ir.Assign object at 0x32e9eeb70>, 174),
                     (<numba.core.ir.Assign object at 0x32e9f9670>, 422)],
             'frac': [(<numba.core.ir.Assign object at 0x32e9ec0b0>, 0),
                      (<numba.core.ir.Assign object at 0x32e9ee270>, 174),
                      (<numba.core.ir.Assign object at 0x32e9effb0>, 422)],
             'i': [(<numba.core.ir.Assign object at 0x32e9ee990>, 326)],
             'i_max': [(<numba.core.ir.Assign object at 0x32e9ef4d0>, 174)],
             'index_frac': [(<numba.core.ir.Assign object at 0x32e9ec230>, 0),
                            (<numba.core.ir.Assign object at 0x32e9ee570>, 174),
                            (<numba.core.ir.Assign object at 0x32e9f8b90>,
                             422)],
             'index_step': [(<numba.core.ir.Assign object at 0x32e967bf0>, 0)],
             'interp_delta': [(<numba.core.ir.Assign object at 0x32e967230>,
                               0)],
             'interp_win': [(<numba.core.ir.Assign object at 0x32e966f90>, 0)],
             'k': [(<numba.core.ir.Assign object at 0x32e9f9f70>, 544)],
             'k_max': [(<numba.core.ir.Assign object at 0x32e9fa1b0>, 422)],
             'n': [(<numba.core.ir.Assign object at 0x32e967ef0>, 0),
                   (<numba.core.ir.Assign object at 0x32e9edd90>, 174)],
             'n_orig': [(<numba.core.ir.Assign object at 0x32e966930>, 0)],
             'n_out': [(<numba.core.ir.Assign object at 0x32e967350>, 0)],
             'num_table': [(<numba.core.ir.Assign object at 0x32e967110>, 0)],
             'nwin': [(<numba.core.ir.Assign object at 0x32e967470>, 0)],
             'offset': [(<numba.core.ir.Assign object at 0x32e9ec3b0>, 0),
                        (<numba.core.ir.Assign object at 0x32e9ee870>, 174),
                        (<numba.core.ir.Assign object at 0x32e9f9370>, 422)],
             'scale': [(<numba.core.ir.Assign object at 0x32e9676b0>, 0)],
             't': [(<numba.core.ir.Assign object at 0x32e9ed130>, 174)],
             't_out': [(<numba.core.ir.Assign object at 0x32e966ff0>, 0)],
             'time_register': [(<numba.core.ir.Assign object at 0x32e967d70>,
                                0),
                               (<numba.core.ir.Assign object at 0x32e9eda90>,
                                174)],
             'weight': [(<numba.core.ir.Assign object at 0x32e9ec6b0>, 0),
                        (<numba.core.ir.Assign object at 0x32e9f8650>, 326),
                        (<numba.core.ir.Assign object at 0x32e9fb530>, 544)],
             'x': [(<numba.core.ir.Assign object at 0x32e966f30>, 0)],
             'y': [(<numba.core.ir.Assign object at 0x32e967770>, 0)]})
2025-08-27 14:20:27,351 [DEBUG] numba.core.ssa: SSA violators <numba.core.utils.OrderedSet object at 0x32e8b1f90>
2025-08-27 14:20:27,351 [DEBUG] numba.core.ssa: Fix SSA violator on var time_register
2025-08-27 14:20:27,351 [DEBUG] numba.core.ssa: ==== SSA block rewrite pass on 0
2025-08-27 14:20:27,351 [DEBUG] numba.core.ssa: Running <numba.core.ssa._FreshVarHandler object at 0x32e99cec0>
2025-08-27 14:20:27,352 [DEBUG] numba.core.ssa: on stmt: x = arg(0, name=x)
2025-08-27 14:20:27,352 [DEBUG] numba.core.ssa: on stmt: t_out = arg(1, name=t_out)
2025-08-27 14:20:27,352 [DEBUG] numba.core.ssa: on stmt: interp_win = arg(2, name=interp_win)
2025-08-27 14:20:27,352 [DEBUG] numba.core.ssa: on stmt: interp_delta = arg(3, name=interp_delta)
2025-08-27 14:20:27,352 [DEBUG] numba.core.ssa: on stmt: num_table = arg(4, name=num_table)
2025-08-27 14:20:27,352 [DEBUG] numba.core.ssa: on stmt: scale = arg(5, name=scale)
2025-08-27 14:20:27,352 [DEBUG] numba.core.ssa: on stmt: y = arg(6, name=y)
2025-08-27 14:20:27,352 [DEBUG] numba.core.ssa: on stmt: $4load_global.0 = global(int: <class 'int'>)
2025-08-27 14:20:27,352 [DEBUG] numba.core.ssa: on stmt: $binop_mul16.4 = scale * num_table
2025-08-27 14:20:27,352 [DEBUG] numba.core.ssa: on stmt: index_step = call $4load_global.0($binop_mul16.4, func=$4load_global.0, args=[Var($binop_mul16.4, interpn.py:9)], kws=(), vararg=None, varkwarg=None, target=None)
2025-08-27 14:20:27,352 [DEBUG] numba.core.ssa: on stmt: time_register = const(float, 0.0)
2025-08-27 14:20:27,352 [DEBUG] numba.core.ssa: first assign: time_register
2025-08-27 14:20:27,352 [DEBUG] numba.core.ssa: replaced with: time_register = const(float, 0.0)
2025-08-27 14:20:27,352 [DEBUG] numba.core.ssa: on stmt: n = const(int, 0)
2025-08-27 14:20:27,352 [DEBUG] numba.core.ssa: on stmt: frac = const(float, 0.0)
2025-08-27 14:20:27,352 [DEBUG] numba.core.ssa: on stmt: index_frac = const(float, 0.0)
2025-08-27 14:20:27,352 [DEBUG] numba.core.ssa: on stmt: offset = const(int, 0)
2025-08-27 14:20:27,352 [DEBUG] numba.core.ssa: on stmt: eta = const(float, 0.0)
2025-08-27 14:20:27,352 [DEBUG] numba.core.ssa: on stmt: weight = const(float, 0.0)
2025-08-27 14:20:27,352 [DEBUG] numba.core.ssa: on stmt: $60load_attr.14 = getattr(value=interp_win, attr=shape)
2025-08-27 14:20:27,352 [DEBUG] numba.core.ssa: on stmt: $const80.15.2 = const(int, 0)
2025-08-27 14:20:27,352 [DEBUG] numba.core.ssa: on stmt: nwin = static_getitem(value=$60load_attr.14, index=0, index_var=$const80.15.2, fn=<built-in function getitem>)
2025-08-27 14:20:27,352 [DEBUG] numba.core.ssa: on stmt: $90load_attr.18 = getattr(value=x, attr=shape)
2025-08-27 14:20:27,352 [DEBUG] numba.core.ssa: on stmt: $const110.19.2 = const(int, 0)
2025-08-27 14:20:27,352 [DEBUG] numba.core.ssa: on stmt: n_orig = static_getitem(value=$90load_attr.18, index=0, index_var=$const110.19.2, fn=<built-in function getitem>)
2025-08-27 14:20:27,352 [DEBUG] numba.core.ssa: on stmt: $120load_attr.22 = getattr(value=t_out, attr=shape)
2025-08-27 14:20:27,352 [DEBUG] numba.core.ssa: on stmt: $const140.23.2 = const(int, 0)
2025-08-27 14:20:27,352 [DEBUG] numba.core.ssa: on stmt: n_out = static_getitem(value=$120load_attr.22, index=0, index_var=$const140.23.2, fn=<built-in function getitem>)
2025-08-27 14:20:27,352 [DEBUG] numba.core.ssa: on stmt: $148load_global.25 = global(prange: <class 'numba.misc.special.prange'>)
2025-08-27 14:20:27,352 [DEBUG] numba.core.ssa: on stmt: $160call.28 = call $148load_global.25(n_out, func=$148load_global.25, args=[Var(n_out, interpn.py:21)], kws=(), vararg=None, varkwarg=None, target=None)
2025-08-27 14:20:27,352 [DEBUG] numba.core.ssa: on stmt: $168get_iter.29 = getiter(value=$160call.28)
2025-08-27 14:20:27,352 [DEBUG] numba.core.ssa: on stmt: $phi170.0 = $168get_iter.29
2025-08-27 14:20:27,352 [DEBUG] numba.core.ssa: on stmt: jump 170
2025-08-27 14:20:27,352 [DEBUG] numba.core.ssa: ==== SSA block rewrite pass on 170
2025-08-27 14:20:27,352 [DEBUG] numba.core.ssa: Running <numba.core.ssa._FreshVarHandler object at 0x32e99cec0>
2025-08-27 14:20:27,352 [DEBUG] numba.core.ssa: on stmt: $170for_iter.1 = iternext(value=$phi170.0)
2025-08-27 14:20:27,352 [DEBUG] numba.core.ssa: on stmt: $170for_iter.2 = pair_first(value=$170for_iter.1)
2025-08-27 14:20:27,352 [DEBUG] numba.core.ssa: on stmt: $170for_iter.3 = pair_second(value=$170for_iter.1)
2025-08-27 14:20:27,352 [DEBUG] numba.core.ssa: on stmt: $phi174.1 = $170for_iter.2
2025-08-27 14:20:27,352 [DEBUG] numba.core.ssa: on stmt: branch $170for_iter.3, 174, 654
2025-08-27 14:20:27,352 [DEBUG] numba.core.ssa: ==== SSA block rewrite pass on 174
2025-08-27 14:20:27,352 [DEBUG] numba.core.ssa: Running <numba.core.ssa._FreshVarHandler object at 0x32e99cec0>
2025-08-27 14:20:27,352 [DEBUG] numba.core.ssa: on stmt: t = $phi174.1
2025-08-27 14:20:27,352 [DEBUG] numba.core.ssa: on stmt: time_register = getitem(value=t_out, index=t, fn=<built-in function getitem>)
2025-08-27 14:20:27,352 [DEBUG] numba.core.ssa: replaced with: time_register.1 = getitem(value=t_out, index=t, fn=<built-in function getitem>)
2025-08-27 14:20:27,352 [DEBUG] numba.core.ssa: on stmt: $186load_global.5 = global(int: <class 'int'>)
2025-08-27 14:20:27,352 [DEBUG] numba.core.ssa: on stmt: n = call $186load_global.5(time_register, func=$186load_global.5, args=[Var(time_register, interpn.py:10)], kws=(), vararg=None, varkwarg=None, target=None)
2025-08-27 14:20:27,352 [DEBUG] numba.core.ssa: on stmt: $binop_sub212.12 = time_register - n
2025-08-27 14:20:27,352 [DEBUG] numba.core.ssa: on stmt: frac = scale * $binop_sub212.12
2025-08-27 14:20:27,352 [DEBUG] numba.core.ssa: on stmt: index_frac = frac * num_table
2025-08-27 14:20:27,352 [DEBUG] numba.core.ssa: on stmt: $230load_global.17 = global(int: <class 'int'>)
2025-08-27 14:20:27,352 [DEBUG] numba.core.ssa: on stmt: offset = call $230load_global.17(index_frac, func=$230load_global.17, args=[Var(index_frac, interpn.py:14)], kws=(), vararg=None, varkwarg=None, target=None)
2025-08-27 14:20:27,352 [DEBUG] numba.core.ssa: on stmt: eta = index_frac - offset
2025-08-27 14:20:27,352 [DEBUG] numba.core.ssa: on stmt: $260load_global.24 = global(min: <built-in function min>)
2025-08-27 14:20:27,352 [DEBUG] numba.core.ssa: on stmt: $const272.27.3 = const(int, 1)
2025-08-27 14:20:27,352 [DEBUG] numba.core.ssa: on stmt: $binop_add274.28 = n + $const272.27.3
2025-08-27 14:20:27,352 [DEBUG] numba.core.ssa: on stmt: $binop_sub280.31 = nwin - offset
2025-08-27 14:20:27,352 [DEBUG] numba.core.ssa: on stmt: $binop_floordiv286.33 = $binop_sub280.31 // index_step
2025-08-27 14:20:27,352 [DEBUG] numba.core.ssa: on stmt: i_max = call $260load_global.24($binop_add274.28, $binop_floordiv286.33, func=$260load_global.24, args=[Var($binop_add274.28, interpn.py:40), Var($binop_floordiv286.33, interpn.py:40)], kws=(), vararg=None, varkwarg=None, target=None)
2025-08-27 14:20:27,352 [DEBUG] numba.core.ssa: on stmt: $300load_global.35 = global(range: <class 'range'>)
2025-08-27 14:20:27,352 [DEBUG] numba.core.ssa: on stmt: $312call.38 = call $300load_global.35(i_max, func=$300load_global.35, args=[Var(i_max, interpn.py:40)], kws=(), vararg=None, varkwarg=None, target=None)
2025-08-27 14:20:27,352 [DEBUG] numba.core.ssa: on stmt: $320get_iter.39 = getiter(value=$312call.38)
2025-08-27 14:20:27,352 [DEBUG] numba.core.ssa: on stmt: $phi322.1 = $320get_iter.39
2025-08-27 14:20:27,353 [DEBUG] numba.core.ssa: on stmt: jump 322
2025-08-27 14:20:27,353 [DEBUG] numba.core.ssa: ==== SSA block rewrite pass on 322
2025-08-27 14:20:27,353 [DEBUG] numba.core.ssa: Running <numba.core.ssa._FreshVarHandler object at 0x32e99cec0>
2025-08-27 14:20:27,353 [DEBUG] numba.core.ssa: on stmt: $322for_iter.2 = iternext(value=$phi322.1)
2025-08-27 14:20:27,353 [DEBUG] numba.core.ssa: on stmt: $322for_iter.3 = pair_first(value=$322for_iter.2)
2025-08-27 14:20:27,353 [DEBUG] numba.core.ssa: on stmt: $322for_iter.4 = pair_second(value=$322for_iter.2)
2025-08-27 14:20:27,353 [DEBUG] numba.core.ssa: on stmt: $phi326.2 = $322for_iter.3
2025-08-27 14:20:27,353 [DEBUG] numba.core.ssa: on stmt: branch $322for_iter.4, 326, 422
2025-08-27 14:20:27,353 [DEBUG] numba.core.ssa: ==== SSA block rewrite pass on 326
2025-08-27 14:20:27,353 [DEBUG] numba.core.ssa: Running <numba.core.ssa._FreshVarHandler object at 0x32e99cec0>
2025-08-27 14:20:27,353 [DEBUG] numba.core.ssa: on stmt: i = $phi326.2
2025-08-27 14:20:27,353 [DEBUG] numba.core.ssa: on stmt: $binop_mul334.7 = i * index_step
2025-08-27 14:20:27,353 [DEBUG] numba.core.ssa: on stmt: $binop_add338.8 = offset + $binop_mul334.7
2025-08-27 14:20:27,353 [DEBUG] numba.core.ssa: on stmt: $342binary_subscr.9 = getitem(value=interp_win, index=$binop_add338.8, fn=<built-in function getitem>)
2025-08-27 14:20:27,353 [DEBUG] numba.core.ssa: on stmt: $binop_mul354.15 = i * index_step
2025-08-27 14:20:27,353 [DEBUG] numba.core.ssa: on stmt: $binop_add358.16 = offset + $binop_mul354.15
2025-08-27 14:20:27,353 [DEBUG] numba.core.ssa: on stmt: $362binary_subscr.17 = getitem(value=interp_delta, index=$binop_add358.16, fn=<built-in function getitem>)
2025-08-27 14:20:27,353 [DEBUG] numba.core.ssa: on stmt: $binop_mul366.18 = eta * $362binary_subscr.17
2025-08-27 14:20:27,353 [DEBUG] numba.core.ssa: on stmt: weight = $342binary_subscr.9 + $binop_mul366.18
2025-08-27 14:20:27,353 [DEBUG] numba.core.ssa: on stmt: $384binary_subscr.22 = getitem(value=y, index=t, fn=<built-in function getitem>)
2025-08-27 14:20:27,353 [DEBUG] numba.core.ssa: on stmt: $binop_sub394.27 = n - i
2025-08-27 14:20:27,353 [DEBUG] numba.core.ssa: on stmt: $398binary_subscr.28 = getitem(value=x, index=$binop_sub394.27, fn=<built-in function getitem>)
2025-08-27 14:20:27,353 [DEBUG] numba.core.ssa: on stmt: $binop_mul402.29 = weight * $398binary_subscr.28
2025-08-27 14:20:27,353 [DEBUG] numba.core.ssa: on stmt: $binop_iadd406.30 = inplace_binop(fn=<built-in function iadd>, immutable_fn=<built-in function add>, lhs=$384binary_subscr.22, rhs=$binop_mul402.29, static_lhs=Undefined, static_rhs=Undefined)
2025-08-27 14:20:27,353 [DEBUG] numba.core.ssa: on stmt: y[t] = $binop_iadd406.30
2025-08-27 14:20:27,353 [DEBUG] numba.core.ssa: on stmt: jump 322
2025-08-27 14:20:27,353 [DEBUG] numba.core.ssa: ==== SSA block rewrite pass on 422
2025-08-27 14:20:27,353 [DEBUG] numba.core.ssa: Running <numba.core.ssa._FreshVarHandler object at 0x32e99cec0>
2025-08-27 14:20:27,353 [DEBUG] numba.core.ssa: on stmt: $binop_sub428.5 = scale - frac
2025-08-27 14:20:27,353 [DEBUG] numba.core.ssa: on stmt: frac = $binop_sub428.5
2025-08-27 14:20:27,353 [DEBUG] numba.core.ssa: on stmt: index_frac = frac * num_table
2025-08-27 14:20:27,353 [DEBUG] numba.core.ssa: on stmt: $442load_global.9 = global(int: <class 'int'>)
2025-08-27 14:20:27,353 [DEBUG] numba.core.ssa: on stmt: offset = call $442load_global.9(index_frac, func=$442load_global.9, args=[Var(index_frac, interpn.py:14)], kws=(), vararg=None, varkwarg=None, target=None)
2025-08-27 14:20:27,353 [DEBUG] numba.core.ssa: on stmt: eta = index_frac - offset
2025-08-27 14:20:27,353 [DEBUG] numba.core.ssa: on stmt: $472load_global.16 = global(min: <built-in function min>)
2025-08-27 14:20:27,353 [DEBUG] numba.core.ssa: on stmt: $binop_sub486.20 = n_orig - n
2025-08-27 14:20:27,353 [DEBUG] numba.core.ssa: on stmt: $const490.21.3 = const(int, 1)
2025-08-27 14:20:27,353 [DEBUG] numba.core.ssa: on stmt: $binop_sub492.22 = $binop_sub486.20 - $const490.21.3
2025-08-27 14:20:27,353 [DEBUG] numba.core.ssa: on stmt: $binop_sub498.25 = nwin - offset
2025-08-27 14:20:27,353 [DEBUG] numba.core.ssa: on stmt: $binop_floordiv504.27 = $binop_sub498.25 // index_step
2025-08-27 14:20:27,353 [DEBUG] numba.core.ssa: on stmt: k_max = call $472load_global.16($binop_sub492.22, $binop_floordiv504.27, func=$472load_global.16, args=[Var($binop_sub492.22, interpn.py:60), Var($binop_floordiv504.27, interpn.py:60)], kws=(), vararg=None, varkwarg=None, target=None)
2025-08-27 14:20:27,353 [DEBUG] numba.core.ssa: on stmt: $518load_global.29 = global(range: <class 'range'>)
2025-08-27 14:20:27,353 [DEBUG] numba.core.ssa: on stmt: $530call.32 = call $518load_global.29(k_max, func=$518load_global.29, args=[Var(k_max, interpn.py:60)], kws=(), vararg=None, varkwarg=None, target=None)
2025-08-27 14:20:27,353 [DEBUG] numba.core.ssa: on stmt: $538get_iter.33 = getiter(value=$530call.32)
2025-08-27 14:20:27,353 [DEBUG] numba.core.ssa: on stmt: $phi540.1 = $538get_iter.33
2025-08-27 14:20:27,353 [DEBUG] numba.core.ssa: on stmt: jump 540
2025-08-27 14:20:27,353 [DEBUG] numba.core.ssa: ==== SSA block rewrite pass on 540
2025-08-27 14:20:27,353 [DEBUG] numba.core.ssa: Running <numba.core.ssa._FreshVarHandler object at 0x32e99cec0>
2025-08-27 14:20:27,353 [DEBUG] numba.core.ssa: on stmt: $540for_iter.2 = iternext(value=$phi540.1)
2025-08-27 14:20:27,353 [DEBUG] numba.core.ssa: on stmt: $540for_iter.3 = pair_first(value=$540for_iter.2)
2025-08-27 14:20:27,353 [DEBUG] numba.core.ssa: on stmt: $540for_iter.4 = pair_second(value=$540for_iter.2)
2025-08-27 14:20:27,353 [DEBUG] numba.core.ssa: on stmt: $phi544.2 = $540for_iter.3
2025-08-27 14:20:27,353 [DEBUG] numba.core.ssa: on stmt: branch $540for_iter.4, 544, 646
2025-08-27 14:20:27,353 [DEBUG] numba.core.ssa: ==== SSA block rewrite pass on 544
2025-08-27 14:20:27,353 [DEBUG] numba.core.ssa: Running <numba.core.ssa._FreshVarHandler object at 0x32e99cec0>
2025-08-27 14:20:27,353 [DEBUG] numba.core.ssa: on stmt: k = $phi544.2
2025-08-27 14:20:27,353 [DEBUG] numba.core.ssa: on stmt: $binop_mul552.7 = k * index_step
2025-08-27 14:20:27,353 [DEBUG] numba.core.ssa: on stmt: $binop_add556.8 = offset + $binop_mul552.7
2025-08-27 14:20:27,353 [DEBUG] numba.core.ssa: on stmt: $560binary_subscr.9 = getitem(value=interp_win, index=$binop_add556.8, fn=<built-in function getitem>)
2025-08-27 14:20:27,353 [DEBUG] numba.core.ssa: on stmt: $binop_mul572.15 = k * index_step
2025-08-27 14:20:27,353 [DEBUG] numba.core.ssa: on stmt: $binop_add576.16 = offset + $binop_mul572.15
2025-08-27 14:20:27,353 [DEBUG] numba.core.ssa: on stmt: $580binary_subscr.17 = getitem(value=interp_delta, index=$binop_add576.16, fn=<built-in function getitem>)
2025-08-27 14:20:27,353 [DEBUG] numba.core.ssa: on stmt: $binop_mul584.18 = eta * $580binary_subscr.17
2025-08-27 14:20:27,353 [DEBUG] numba.core.ssa: on stmt: weight = $560binary_subscr.9 + $binop_mul584.18
2025-08-27 14:20:27,353 [DEBUG] numba.core.ssa: on stmt: $602binary_subscr.22 = getitem(value=y, index=t, fn=<built-in function getitem>)
2025-08-27 14:20:27,353 [DEBUG] numba.core.ssa: on stmt: $binop_add612.27 = n + k
2025-08-27 14:20:27,353 [DEBUG] numba.core.ssa: on stmt: $const616.28.3 = const(int, 1)
2025-08-27 14:20:27,353 [DEBUG] numba.core.ssa: on stmt: $binop_add618.29 = $binop_add612.27 + $const616.28.3
2025-08-27 14:20:27,353 [DEBUG] numba.core.ssa: on stmt: $622binary_subscr.30 = getitem(value=x, index=$binop_add618.29, fn=<built-in function getitem>)
2025-08-27 14:20:27,353 [DEBUG] numba.core.ssa: on stmt: $binop_mul626.31 = weight * $622binary_subscr.30
2025-08-27 14:20:27,353 [DEBUG] numba.core.ssa: on stmt: $binop_iadd630.32 = inplace_binop(fn=<built-in function iadd>, immutable_fn=<built-in function add>, lhs=$602binary_subscr.22, rhs=$binop_mul626.31, static_lhs=Undefined, static_rhs=Undefined)
2025-08-27 14:20:27,353 [DEBUG] numba.core.ssa: on stmt: y[t] = $binop_iadd630.32
2025-08-27 14:20:27,354 [DEBUG] numba.core.ssa: on stmt: jump 540
2025-08-27 14:20:27,354 [DEBUG] numba.core.ssa: ==== SSA block rewrite pass on 646
2025-08-27 14:20:27,354 [DEBUG] numba.core.ssa: Running <numba.core.ssa._FreshVarHandler object at 0x32e99cec0>
2025-08-27 14:20:27,354 [DEBUG] numba.core.ssa: on stmt: jump 170
2025-08-27 14:20:27,354 [DEBUG] numba.core.ssa: ==== SSA block rewrite pass on 654
2025-08-27 14:20:27,354 [DEBUG] numba.core.ssa: Running <numba.core.ssa._FreshVarHandler object at 0x32e99cec0>
2025-08-27 14:20:27,354 [DEBUG] numba.core.ssa: on stmt: $const658.2 = const(NoneType, None)
2025-08-27 14:20:27,354 [DEBUG] numba.core.ssa: on stmt: $658return_const.3 = cast(value=$const658.2)
2025-08-27 14:20:27,354 [DEBUG] numba.core.ssa: on stmt: return $658return_const.3
2025-08-27 14:20:27,354 [DEBUG] numba.core.ssa: Replaced assignments: defaultdict(<class 'list'>,
            {0: [<numba.core.ir.Assign object at 0x32ea05f10>],
             174: [<numba.core.ir.Assign object at 0x32ea05eb0>]})
2025-08-27 14:20:27,354 [DEBUG] numba.core.ssa: ==== SSA block rewrite pass on 0
2025-08-27 14:20:27,354 [DEBUG] numba.core.ssa: Running <numba.core.ssa._FixSSAVars object at 0x32e99cec0>
2025-08-27 14:20:27,354 [DEBUG] numba.core.ssa: on stmt: x = arg(0, name=x)
2025-08-27 14:20:27,354 [DEBUG] numba.core.ssa: on stmt: t_out = arg(1, name=t_out)
2025-08-27 14:20:27,354 [DEBUG] numba.core.ssa: on stmt: interp_win = arg(2, name=interp_win)
2025-08-27 14:20:27,354 [DEBUG] numba.core.ssa: on stmt: interp_delta = arg(3, name=interp_delta)
2025-08-27 14:20:27,354 [DEBUG] numba.core.ssa: on stmt: num_table = arg(4, name=num_table)
2025-08-27 14:20:27,354 [DEBUG] numba.core.ssa: on stmt: scale = arg(5, name=scale)
2025-08-27 14:20:27,354 [DEBUG] numba.core.ssa: on stmt: y = arg(6, name=y)
2025-08-27 14:20:27,355 [DEBUG] numba.core.ssa: on stmt: $4load_global.0 = global(int: <class 'int'>)
2025-08-27 14:20:27,355 [DEBUG] numba.core.ssa: on stmt: $binop_mul16.4 = scale * num_table
2025-08-27 14:20:27,355 [DEBUG] numba.core.ssa: on stmt: index_step = call $4load_global.0($binop_mul16.4, func=$4load_global.0, args=[Var($binop_mul16.4, interpn.py:9)], kws=(), vararg=None, varkwarg=None, target=None)
2025-08-27 14:20:27,355 [DEBUG] numba.core.ssa: on stmt: time_register = const(float, 0.0)
2025-08-27 14:20:27,355 [DEBUG] numba.core.ssa: on stmt: n = const(int, 0)
2025-08-27 14:20:27,355 [DEBUG] numba.core.ssa: on stmt: frac = const(float, 0.0)
2025-08-27 14:20:27,355 [DEBUG] numba.core.ssa: on stmt: index_frac = const(float, 0.0)
2025-08-27 14:20:27,355 [DEBUG] numba.core.ssa: on stmt: offset = const(int, 0)
2025-08-27 14:20:27,355 [DEBUG] numba.core.ssa: on stmt: eta = const(float, 0.0)
2025-08-27 14:20:27,355 [DEBUG] numba.core.ssa: on stmt: weight = const(float, 0.0)
2025-08-27 14:20:27,355 [DEBUG] numba.core.ssa: on stmt: $60load_attr.14 = getattr(value=interp_win, attr=shape)
2025-08-27 14:20:27,355 [DEBUG] numba.core.ssa: on stmt: $const80.15.2 = const(int, 0)
2025-08-27 14:20:27,355 [DEBUG] numba.core.ssa: on stmt: nwin = static_getitem(value=$60load_attr.14, index=0, index_var=$const80.15.2, fn=<built-in function getitem>)
2025-08-27 14:20:27,355 [DEBUG] numba.core.ssa: on stmt: $90load_attr.18 = getattr(value=x, attr=shape)
2025-08-27 14:20:27,355 [DEBUG] numba.core.ssa: on stmt: $const110.19.2 = const(int, 0)
2025-08-27 14:20:27,355 [DEBUG] numba.core.ssa: on stmt: n_orig = static_getitem(value=$90load_attr.18, index=0, index_var=$const110.19.2, fn=<built-in function getitem>)
2025-08-27 14:20:27,355 [DEBUG] numba.core.ssa: on stmt: $120load_attr.22 = getattr(value=t_out, attr=shape)
2025-08-27 14:20:27,355 [DEBUG] numba.core.ssa: on stmt: $const140.23.2 = const(int, 0)
2025-08-27 14:20:27,355 [DEBUG] numba.core.ssa: on stmt: n_out = static_getitem(value=$120load_attr.22, index=0, index_var=$const140.23.2, fn=<built-in function getitem>)
2025-08-27 14:20:27,355 [DEBUG] numba.core.ssa: on stmt: $148load_global.25 = global(prange: <class 'numba.misc.special.prange'>)
2025-08-27 14:20:27,355 [DEBUG] numba.core.ssa: on stmt: $160call.28 = call $148load_global.25(n_out, func=$148load_global.25, args=[Var(n_out, interpn.py:21)], kws=(), vararg=None, varkwarg=None, target=None)
2025-08-27 14:20:27,355 [DEBUG] numba.core.ssa: on stmt: $168get_iter.29 = getiter(value=$160call.28)
2025-08-27 14:20:27,355 [DEBUG] numba.core.ssa: on stmt: $phi170.0 = $168get_iter.29
2025-08-27 14:20:27,355 [DEBUG] numba.core.ssa: on stmt: jump 170
2025-08-27 14:20:27,355 [DEBUG] numba.core.ssa: ==== SSA block rewrite pass on 170
2025-08-27 14:20:27,355 [DEBUG] numba.core.ssa: Running <numba.core.ssa._FixSSAVars object at 0x32e99cec0>
2025-08-27 14:20:27,355 [DEBUG] numba.core.ssa: on stmt: $170for_iter.1 = iternext(value=$phi170.0)
2025-08-27 14:20:27,355 [DEBUG] numba.core.ssa: on stmt: $170for_iter.2 = pair_first(value=$170for_iter.1)
2025-08-27 14:20:27,355 [DEBUG] numba.core.ssa: on stmt: $170for_iter.3 = pair_second(value=$170for_iter.1)
2025-08-27 14:20:27,355 [DEBUG] numba.core.ssa: on stmt: $phi174.1 = $170for_iter.2
2025-08-27 14:20:27,355 [DEBUG] numba.core.ssa: on stmt: branch $170for_iter.3, 174, 654
2025-08-27 14:20:27,355 [DEBUG] numba.core.ssa: ==== SSA block rewrite pass on 174
2025-08-27 14:20:27,355 [DEBUG] numba.core.ssa: Running <numba.core.ssa._FixSSAVars object at 0x32e99cec0>
2025-08-27 14:20:27,355 [DEBUG] numba.core.ssa: on stmt: t = $phi174.1
2025-08-27 14:20:27,355 [DEBUG] numba.core.ssa: on stmt: time_register.1 = getitem(value=t_out, index=t, fn=<built-in function getitem>)
2025-08-27 14:20:27,355 [DEBUG] numba.core.ssa: on stmt: $186load_global.5 = global(int: <class 'int'>)
2025-08-27 14:20:27,355 [DEBUG] numba.core.ssa: on stmt: n = call $186load_global.5(time_register, func=$186load_global.5, args=[Var(time_register, interpn.py:10)], kws=(), vararg=None, varkwarg=None, target=None)
2025-08-27 14:20:27,355 [DEBUG] numba.core.ssa: find_def var='time_register' stmt=n = call $186load_global.5(time_register, func=$186load_global.5, args=[Var(time_register, interpn.py:10)], kws=(), vararg=None, varkwarg=None, target=None)
2025-08-27 14:20:27,355 [DEBUG] numba.core.ssa: replaced with: n = call $186load_global.5(time_register.1, func=$186load_global.5, args=[Var(time_register.1, interpn.py:24)], kws=(), vararg=None, varkwarg=None, target=None)
2025-08-27 14:20:27,355 [DEBUG] numba.core.ssa: on stmt: $binop_sub212.12 = time_register - n
2025-08-27 14:20:27,355 [DEBUG] numba.core.ssa: find_def var='time_register' stmt=$binop_sub212.12 = time_register - n
2025-08-27 14:20:27,355 [DEBUG] numba.core.ssa: replaced with: $binop_sub212.12 = time_register.1 - n
2025-08-27 14:20:27,355 [DEBUG] numba.core.ssa: on stmt: frac = scale * $binop_sub212.12
2025-08-27 14:20:27,355 [DEBUG] numba.core.ssa: on stmt: index_frac = frac * num_table
2025-08-27 14:20:27,355 [DEBUG] numba.core.ssa: on stmt: $230load_global.17 = global(int: <class 'int'>)
2025-08-27 14:20:27,355 [DEBUG] numba.core.ssa: on stmt: offset = call $230load_global.17(index_frac, func=$230load_global.17, args=[Var(index_frac, interpn.py:14)], kws=(), vararg=None, varkwarg=None, target=None)
2025-08-27 14:20:27,355 [DEBUG] numba.core.ssa: on stmt: eta = index_frac - offset
2025-08-27 14:20:27,355 [DEBUG] numba.core.ssa: on stmt: $260load_global.24 = global(min: <built-in function min>)
2025-08-27 14:20:27,355 [DEBUG] numba.core.ssa: on stmt: $const272.27.3 = const(int, 1)
2025-08-27 14:20:27,356 [DEBUG] numba.core.ssa: on stmt: $binop_add274.28 = n + $const272.27.3
2025-08-27 14:20:27,356 [DEBUG] numba.core.ssa: on stmt: $binop_sub280.31 = nwin - offset
2025-08-27 14:20:27,356 [DEBUG] numba.core.ssa: on stmt: $binop_floordiv286.33 = $binop_sub280.31 // index_step
2025-08-27 14:20:27,356 [DEBUG] numba.core.ssa: on stmt: i_max = call $260load_global.24($binop_add274.28, $binop_floordiv286.33, func=$260load_global.24, args=[Var($binop_add274.28, interpn.py:40), Var($binop_floordiv286.33, interpn.py:40)], kws=(), vararg=None, varkwarg=None, target=None)
2025-08-27 14:20:27,356 [DEBUG] numba.core.ssa: on stmt: $300load_global.35 = global(range: <class 'range'>)
2025-08-27 14:20:27,356 [DEBUG] numba.core.ssa: on stmt: $312call.38 = call $300load_global.35(i_max, func=$300load_global.35, args=[Var(i_max, interpn.py:40)], kws=(), vararg=None, varkwarg=None, target=None)
2025-08-27 14:20:27,356 [DEBUG] numba.core.ssa: on stmt: $320get_iter.39 = getiter(value=$312call.38)
2025-08-27 14:20:27,356 [DEBUG] numba.core.ssa: on stmt: $phi322.1 = $320get_iter.39
2025-08-27 14:20:27,356 [DEBUG] numba.core.ssa: on stmt: jump 322
2025-08-27 14:20:27,356 [DEBUG] numba.core.ssa: ==== SSA block rewrite pass on 322
2025-08-27 14:20:27,356 [DEBUG] numba.core.ssa: Running <numba.core.ssa._FixSSAVars object at 0x32e99cec0>
2025-08-27 14:20:27,356 [DEBUG] numba.core.ssa: on stmt: $322for_iter.2 = iternext(value=$phi322.1)
2025-08-27 14:20:27,356 [DEBUG] numba.core.ssa: on stmt: $322for_iter.3 = pair_first(value=$322for_iter.2)
2025-08-27 14:20:27,356 [DEBUG] numba.core.ssa: on stmt: $322for_iter.4 = pair_second(value=$322for_iter.2)
2025-08-27 14:20:27,356 [DEBUG] numba.core.ssa: on stmt: $phi326.2 = $322for_iter.3
2025-08-27 14:20:27,356 [DEBUG] numba.core.ssa: on stmt: branch $322for_iter.4, 326, 422
2025-08-27 14:20:27,356 [DEBUG] numba.core.ssa: ==== SSA block rewrite pass on 326
2025-08-27 14:20:27,356 [DEBUG] numba.core.ssa: Running <numba.core.ssa._FixSSAVars object at 0x32e99cec0>
2025-08-27 14:20:27,356 [DEBUG] numba.core.ssa: on stmt: i = $phi326.2
2025-08-27 14:20:27,356 [DEBUG] numba.core.ssa: on stmt: $binop_mul334.7 = i * index_step
2025-08-27 14:20:27,356 [DEBUG] numba.core.ssa: on stmt: $binop_add338.8 = offset + $binop_mul334.7
2025-08-27 14:20:27,356 [DEBUG] numba.core.ssa: on stmt: $342binary_subscr.9 = getitem(value=interp_win, index=$binop_add338.8, fn=<built-in function getitem>)
2025-08-27 14:20:27,356 [DEBUG] numba.core.ssa: on stmt: $binop_mul354.15 = i * index_step
2025-08-27 14:20:27,356 [DEBUG] numba.core.ssa: on stmt: $binop_add358.16 = offset + $binop_mul354.15
2025-08-27 14:20:27,356 [DEBUG] numba.core.ssa: on stmt: $362binary_subscr.17 = getitem(value=interp_delta, index=$binop_add358.16, fn=<built-in function getitem>)
2025-08-27 14:20:27,356 [DEBUG] numba.core.ssa: on stmt: $binop_mul366.18 = eta * $362binary_subscr.17
2025-08-27 14:20:27,356 [DEBUG] numba.core.ssa: on stmt: weight = $342binary_subscr.9 + $binop_mul366.18
2025-08-27 14:20:27,356 [DEBUG] numba.core.ssa: on stmt: $384binary_subscr.22 = getitem(value=y, index=t, fn=<built-in function getitem>)
2025-08-27 14:20:27,356 [DEBUG] numba.core.ssa: on stmt: $binop_sub394.27 = n - i
2025-08-27 14:20:27,356 [DEBUG] numba.core.ssa: on stmt: $398binary_subscr.28 = getitem(value=x, index=$binop_sub394.27, fn=<built-in function getitem>)
2025-08-27 14:20:27,356 [DEBUG] numba.core.ssa: on stmt: $binop_mul402.29 = weight * $398binary_subscr.28
2025-08-27 14:20:27,356 [DEBUG] numba.core.ssa: on stmt: $binop_iadd406.30 = inplace_binop(fn=<built-in function iadd>, immutable_fn=<built-in function add>, lhs=$384binary_subscr.22, rhs=$binop_mul402.29, static_lhs=Undefined, static_rhs=Undefined)
2025-08-27 14:20:27,356 [DEBUG] numba.core.ssa: on stmt: y[t] = $binop_iadd406.30
2025-08-27 14:20:27,356 [DEBUG] numba.core.ssa: on stmt: jump 322
2025-08-27 14:20:27,356 [DEBUG] numba.core.ssa: ==== SSA block rewrite pass on 422
2025-08-27 14:20:27,356 [DEBUG] numba.core.ssa: Running <numba.core.ssa._FixSSAVars object at 0x32e99cec0>
2025-08-27 14:20:27,356 [DEBUG] numba.core.ssa: on stmt: $binop_sub428.5 = scale - frac
2025-08-27 14:20:27,356 [DEBUG] numba.core.ssa: on stmt: frac = $binop_sub428.5
2025-08-27 14:20:27,356 [DEBUG] numba.core.ssa: on stmt: index_frac = frac * num_table
2025-08-27 14:20:27,356 [DEBUG] numba.core.ssa: on stmt: $442load_global.9 = global(int: <class 'int'>)
2025-08-27 14:20:27,356 [DEBUG] numba.core.ssa: on stmt: offset = call $442load_global.9(index_frac, func=$442load_global.9, args=[Var(index_frac, interpn.py:14)], kws=(), vararg=None, varkwarg=None, target=None)
2025-08-27 14:20:27,356 [DEBUG] numba.core.ssa: on stmt: eta = index_frac - offset
2025-08-27 14:20:27,356 [DEBUG] numba.core.ssa: on stmt: $472load_global.16 = global(min: <built-in function min>)
2025-08-27 14:20:27,356 [DEBUG] numba.core.ssa: on stmt: $binop_sub486.20 = n_orig - n
2025-08-27 14:20:27,356 [DEBUG] numba.core.ssa: on stmt: $const490.21.3 = const(int, 1)
2025-08-27 14:20:27,356 [DEBUG] numba.core.ssa: on stmt: $binop_sub492.22 = $binop_sub486.20 - $const490.21.3
2025-08-27 14:20:27,356 [DEBUG] numba.core.ssa: on stmt: $binop_sub498.25 = nwin - offset
2025-08-27 14:20:27,356 [DEBUG] numba.core.ssa: on stmt: $binop_floordiv504.27 = $binop_sub498.25 // index_step
2025-08-27 14:20:27,356 [DEBUG] numba.core.ssa: on stmt: k_max = call $472load_global.16($binop_sub492.22, $binop_floordiv504.27, func=$472load_global.16, args=[Var($binop_sub492.22, interpn.py:60), Var($binop_floordiv504.27, interpn.py:60)], kws=(), vararg=None, varkwarg=None, target=None)
2025-08-27 14:20:27,356 [DEBUG] numba.core.ssa: on stmt: $518load_global.29 = global(range: <class 'range'>)
2025-08-27 14:20:27,356 [DEBUG] numba.core.ssa: on stmt: $530call.32 = call $518load_global.29(k_max, func=$518load_global.29, args=[Var(k_max, interpn.py:60)], kws=(), vararg=None, varkwarg=None, target=None)
2025-08-27 14:20:27,356 [DEBUG] numba.core.ssa: on stmt: $538get_iter.33 = getiter(value=$530call.32)
2025-08-27 14:20:27,356 [DEBUG] numba.core.ssa: on stmt: $phi540.1 = $538get_iter.33
2025-08-27 14:20:27,356 [DEBUG] numba.core.ssa: on stmt: jump 540
2025-08-27 14:20:27,356 [DEBUG] numba.core.ssa: ==== SSA block rewrite pass on 540
2025-08-27 14:20:27,356 [DEBUG] numba.core.ssa: Running <numba.core.ssa._FixSSAVars object at 0x32e99cec0>
2025-08-27 14:20:27,356 [DEBUG] numba.core.ssa: on stmt: $540for_iter.2 = iternext(value=$phi540.1)
2025-08-27 14:20:27,356 [DEBUG] numba.core.ssa: on stmt: $540for_iter.3 = pair_first(value=$540for_iter.2)
2025-08-27 14:20:27,356 [DEBUG] numba.core.ssa: on stmt: $540for_iter.4 = pair_second(value=$540for_iter.2)
2025-08-27 14:20:27,356 [DEBUG] numba.core.ssa: on stmt: $phi544.2 = $540for_iter.3
2025-08-27 14:20:27,356 [DEBUG] numba.core.ssa: on stmt: branch $540for_iter.4, 544, 646
2025-08-27 14:20:27,356 [DEBUG] numba.core.ssa: ==== SSA block rewrite pass on 544
2025-08-27 14:20:27,356 [DEBUG] numba.core.ssa: Running <numba.core.ssa._FixSSAVars object at 0x32e99cec0>
2025-08-27 14:20:27,356 [DEBUG] numba.core.ssa: on stmt: k = $phi544.2
2025-08-27 14:20:27,356 [DEBUG] numba.core.ssa: on stmt: $binop_mul552.7 = k * index_step
2025-08-27 14:20:27,357 [DEBUG] numba.core.ssa: on stmt: $binop_add556.8 = offset + $binop_mul552.7
2025-08-27 14:20:27,357 [DEBUG] numba.core.ssa: on stmt: $560binary_subscr.9 = getitem(value=interp_win, index=$binop_add556.8, fn=<built-in function getitem>)
2025-08-27 14:20:27,357 [DEBUG] numba.core.ssa: on stmt: $binop_mul572.15 = k * index_step
2025-08-27 14:20:27,357 [DEBUG] numba.core.ssa: on stmt: $binop_add576.16 = offset + $binop_mul572.15
2025-08-27 14:20:27,357 [DEBUG] numba.core.ssa: on stmt: $580binary_subscr.17 = getitem(value=interp_delta, index=$binop_add576.16, fn=<built-in function getitem>)
2025-08-27 14:20:27,357 [DEBUG] numba.core.ssa: on stmt: $binop_mul584.18 = eta * $580binary_subscr.17
2025-08-27 14:20:27,357 [DEBUG] numba.core.ssa: on stmt: weight = $560binary_subscr.9 + $binop_mul584.18
2025-08-27 14:20:27,357 [DEBUG] numba.core.ssa: on stmt: $602binary_subscr.22 = getitem(value=y, index=t, fn=<built-in function getitem>)
2025-08-27 14:20:27,357 [DEBUG] numba.core.ssa: on stmt: $binop_add612.27 = n + k
2025-08-27 14:20:27,357 [DEBUG] numba.core.ssa: on stmt: $const616.28.3 = const(int, 1)
2025-08-27 14:20:27,357 [DEBUG] numba.core.ssa: on stmt: $binop_add618.29 = $binop_add612.27 + $const616.28.3
2025-08-27 14:20:27,357 [DEBUG] numba.core.ssa: on stmt: $622binary_subscr.30 = getitem(value=x, index=$binop_add618.29, fn=<built-in function getitem>)
2025-08-27 14:20:27,357 [DEBUG] numba.core.ssa: on stmt: $binop_mul626.31 = weight * $622binary_subscr.30
2025-08-27 14:20:27,357 [DEBUG] numba.core.ssa: on stmt: $binop_iadd630.32 = inplace_binop(fn=<built-in function iadd>, immutable_fn=<built-in function add>, lhs=$602binary_subscr.22, rhs=$binop_mul626.31, static_lhs=Undefined, static_rhs=Undefined)
2025-08-27 14:20:27,357 [DEBUG] numba.core.ssa: on stmt: y[t] = $binop_iadd630.32
2025-08-27 14:20:27,357 [DEBUG] numba.core.ssa: on stmt: jump 540
2025-08-27 14:20:27,357 [DEBUG] numba.core.ssa: ==== SSA block rewrite pass on 646
2025-08-27 14:20:27,357 [DEBUG] numba.core.ssa: Running <numba.core.ssa._FixSSAVars object at 0x32e99cec0>
2025-08-27 14:20:27,357 [DEBUG] numba.core.ssa: on stmt: jump 170
2025-08-27 14:20:27,357 [DEBUG] numba.core.ssa: ==== SSA block rewrite pass on 654
2025-08-27 14:20:27,357 [DEBUG] numba.core.ssa: Running <numba.core.ssa._FixSSAVars object at 0x32e99cec0>
2025-08-27 14:20:27,357 [DEBUG] numba.core.ssa: on stmt: $const658.2 = const(NoneType, None)
2025-08-27 14:20:27,357 [DEBUG] numba.core.ssa: on stmt: $658return_const.3 = cast(value=$const658.2)
2025-08-27 14:20:27,357 [DEBUG] numba.core.ssa: on stmt: return $658return_const.3
2025-08-27 14:20:27,357 [DEBUG] numba.core.ssa: Fix SSA violator on var n
2025-08-27 14:20:27,357 [DEBUG] numba.core.ssa: ==== SSA block rewrite pass on 0
2025-08-27 14:20:27,357 [DEBUG] numba.core.ssa: Running <numba.core.ssa._FreshVarHandler object at 0x32e8b11d0>
2025-08-27 14:20:27,357 [DEBUG] numba.core.ssa: on stmt: x = arg(0, name=x)
2025-08-27 14:20:27,357 [DEBUG] numba.core.ssa: on stmt: t_out = arg(1, name=t_out)
2025-08-27 14:20:27,357 [DEBUG] numba.core.ssa: on stmt: interp_win = arg(2, name=interp_win)
2025-08-27 14:20:27,357 [DEBUG] numba.core.ssa: on stmt: interp_delta = arg(3, name=interp_delta)
2025-08-27 14:20:27,357 [DEBUG] numba.core.ssa: on stmt: num_table = arg(4, name=num_table)
2025-08-27 14:20:27,357 [DEBUG] numba.core.ssa: on stmt: scale = arg(5, name=scale)
2025-08-27 14:20:27,357 [DEBUG] numba.core.ssa: on stmt: y = arg(6, name=y)
2025-08-27 14:20:27,357 [DEBUG] numba.core.ssa: on stmt: $4load_global.0 = global(int: <class 'int'>)
2025-08-27 14:20:27,357 [DEBUG] numba.core.ssa: on stmt: $binop_mul16.4 = scale * num_table
2025-08-27 14:20:27,357 [DEBUG] numba.core.ssa: on stmt: index_step = call $4load_global.0($binop_mul16.4, func=$4load_global.0, args=[Var($binop_mul16.4, interpn.py:9)], kws=(), vararg=None, varkwarg=None, target=None)
2025-08-27 14:20:27,357 [DEBUG] numba.core.ssa: on stmt: time_register = const(float, 0.0)
2025-08-27 14:20:27,357 [DEBUG] numba.core.ssa: on stmt: n = const(int, 0)
2025-08-27 14:20:27,357 [DEBUG] numba.core.ssa: first assign: n
2025-08-27 14:20:27,357 [DEBUG] numba.core.ssa: replaced with: n = const(int, 0)
2025-08-27 14:20:27,357 [DEBUG] numba.core.ssa: on stmt: frac = const(float, 0.0)
2025-08-27 14:20:27,357 [DEBUG] numba.core.ssa: on stmt: index_frac = const(float, 0.0)
2025-08-27 14:20:27,357 [DEBUG] numba.core.ssa: on stmt: offset = const(int, 0)
2025-08-27 14:20:27,357 [DEBUG] numba.core.ssa: on stmt: eta = const(float, 0.0)
2025-08-27 14:20:27,357 [DEBUG] numba.core.ssa: on stmt: weight = const(float, 0.0)
2025-08-27 14:20:27,357 [DEBUG] numba.core.ssa: on stmt: $60load_attr.14 = getattr(value=interp_win, attr=shape)
2025-08-27 14:20:27,357 [DEBUG] numba.core.ssa: on stmt: $const80.15.2 = const(int, 0)
2025-08-27 14:20:27,357 [DEBUG] numba.core.ssa: on stmt: nwin = static_getitem(value=$60load_attr.14, index=0, index_var=$const80.15.2, fn=<built-in function getitem>)
2025-08-27 14:20:27,357 [DEBUG] numba.core.ssa: on stmt: $90load_attr.18 = getattr(value=x, attr=shape)
2025-08-27 14:20:27,357 [DEBUG] numba.core.ssa: on stmt: $const110.19.2 = const(int, 0)
2025-08-27 14:20:27,357 [DEBUG] numba.core.ssa: on stmt: n_orig = static_getitem(value=$90load_attr.18, index=0, index_var=$const110.19.2, fn=<built-in function getitem>)
2025-08-27 14:20:27,357 [DEBUG] numba.core.ssa: on stmt: $120load_attr.22 = getattr(value=t_out, attr=shape)
2025-08-27 14:20:27,357 [DEBUG] numba.core.ssa: on stmt: $const140.23.2 = const(int, 0)
2025-08-27 14:20:27,357 [DEBUG] numba.core.ssa: on stmt: n_out = static_getitem(value=$120load_attr.22, index=0, index_var=$const140.23.2, fn=<built-in function getitem>)
2025-08-27 14:20:27,357 [DEBUG] numba.core.ssa: on stmt: $148load_global.25 = global(prange: <class 'numba.misc.special.prange'>)
2025-08-27 14:20:27,357 [DEBUG] numba.core.ssa: on stmt: $160call.28 = call $148load_global.25(n_out, func=$148load_global.25, args=[Var(n_out, interpn.py:21)], kws=(), vararg=None, varkwarg=None, target=None)
2025-08-27 14:20:27,357 [DEBUG] numba.core.ssa: on stmt: $168get_iter.29 = getiter(value=$160call.28)
2025-08-27 14:20:27,357 [DEBUG] numba.core.ssa: on stmt: $phi170.0 = $168get_iter.29
2025-08-27 14:20:27,357 [DEBUG] numba.core.ssa: on stmt: jump 170
2025-08-27 14:20:27,357 [DEBUG] numba.core.ssa: ==== SSA block rewrite pass on 170
2025-08-27 14:20:27,357 [DEBUG] numba.core.ssa: Running <numba.core.ssa._FreshVarHandler object at 0x32e8b11d0>
2025-08-27 14:20:27,357 [DEBUG] numba.core.ssa: on stmt: $170for_iter.1 = iternext(value=$phi170.0)
2025-08-27 14:20:27,357 [DEBUG] numba.core.ssa: on stmt: $170for_iter.2 = pair_first(value=$170for_iter.1)
2025-08-27 14:20:27,357 [DEBUG] numba.core.ssa: on stmt: $170for_iter.3 = pair_second(value=$170for_iter.1)
2025-08-27 14:20:27,357 [DEBUG] numba.core.ssa: on stmt: $phi174.1 = $170for_iter.2
2025-08-27 14:20:27,357 [DEBUG] numba.core.ssa: on stmt: branch $170for_iter.3, 174, 654
2025-08-27 14:20:27,357 [DEBUG] numba.core.ssa: ==== SSA block rewrite pass on 174
2025-08-27 14:20:27,357 [DEBUG] numba.core.ssa: Running <numba.core.ssa._FreshVarHandler object at 0x32e8b11d0>
2025-08-27 14:20:27,357 [DEBUG] numba.core.ssa: on stmt: t = $phi174.1
2025-08-27 14:20:27,358 [DEBUG] numba.core.ssa: on stmt: time_register.1 = getitem(value=t_out, index=t, fn=<built-in function getitem>)
2025-08-27 14:20:27,358 [DEBUG] numba.core.ssa: on stmt: $186load_global.5 = global(int: <class 'int'>)
2025-08-27 14:20:27,358 [DEBUG] numba.core.ssa: on stmt: n = call $186load_global.5(time_register.1, func=$186load_global.5, args=[Var(time_register.1, interpn.py:24)], kws=(), vararg=None, varkwarg=None, target=None)
2025-08-27 14:20:27,358 [DEBUG] numba.core.ssa: replaced with: n.1 = call $186load_global.5(time_register.1, func=$186load_global.5, args=[Var(time_register.1, interpn.py:24)], kws=(), vararg=None, varkwarg=None, target=None)
2025-08-27 14:20:27,358 [DEBUG] numba.core.ssa: on stmt: $binop_sub212.12 = time_register.1 - n
2025-08-27 14:20:27,358 [DEBUG] numba.core.ssa: on stmt: frac = scale * $binop_sub212.12
2025-08-27 14:20:27,358 [DEBUG] numba.core.ssa: on stmt: index_frac = frac * num_table
2025-08-27 14:20:27,358 [DEBUG] numba.core.ssa: on stmt: $230load_global.17 = global(int: <class 'int'>)
2025-08-27 14:20:27,358 [DEBUG] numba.core.ssa: on stmt: offset = call $230load_global.17(index_frac, func=$230load_global.17, args=[Var(index_frac, interpn.py:14)], kws=(), vararg=None, varkwarg=None, target=None)
2025-08-27 14:20:27,358 [DEBUG] numba.core.ssa: on stmt: eta = index_frac - offset
2025-08-27 14:20:27,358 [DEBUG] numba.core.ssa: on stmt: $260load_global.24 = global(min: <built-in function min>)
2025-08-27 14:20:27,358 [DEBUG] numba.core.ssa: on stmt: $const272.27.3 = const(int, 1)
2025-08-27 14:20:27,358 [DEBUG] numba.core.ssa: on stmt: $binop_add274.28 = n + $const272.27.3
2025-08-27 14:20:27,358 [DEBUG] numba.core.ssa: on stmt: $binop_sub280.31 = nwin - offset
2025-08-27 14:20:27,358 [DEBUG] numba.core.ssa: on stmt: $binop_floordiv286.33 = $binop_sub280.31 // index_step
2025-08-27 14:20:27,358 [DEBUG] numba.core.ssa: on stmt: i_max = call $260load_global.24($binop_add274.28, $binop_floordiv286.33, func=$260load_global.24, args=[Var($binop_add274.28, interpn.py:40), Var($binop_floordiv286.33, interpn.py:40)], kws=(), vararg=None, varkwarg=None, target=None)
2025-08-27 14:20:27,358 [DEBUG] numba.core.ssa: on stmt: $300load_global.35 = global(range: <class 'range'>)
2025-08-27 14:20:27,358 [DEBUG] numba.core.ssa: on stmt: $312call.38 = call $300load_global.35(i_max, func=$300load_global.35, args=[Var(i_max, interpn.py:40)], kws=(), vararg=None, varkwarg=None, target=None)
2025-08-27 14:20:27,358 [DEBUG] numba.core.ssa: on stmt: $320get_iter.39 = getiter(value=$312call.38)
2025-08-27 14:20:27,358 [DEBUG] numba.core.ssa: on stmt: $phi322.1 = $320get_iter.39
2025-08-27 14:20:27,358 [DEBUG] numba.core.ssa: on stmt: jump 322
2025-08-27 14:20:27,358 [DEBUG] numba.core.ssa: ==== SSA block rewrite pass on 322
2025-08-27 14:20:27,358 [DEBUG] numba.core.ssa: Running <numba.core.ssa._FreshVarHandler object at 0x32e8b11d0>
2025-08-27 14:20:27,358 [DEBUG] numba.core.ssa: on stmt: $322for_iter.2 = iternext(value=$phi322.1)
2025-08-27 14:20:27,358 [DEBUG] numba.core.ssa: on stmt: $322for_iter.3 = pair_first(value=$322for_iter.2)
2025-08-27 14:20:27,358 [DEBUG] numba.core.ssa: on stmt: $322for_iter.4 = pair_second(value=$322for_iter.2)
2025-08-27 14:20:27,358 [DEBUG] numba.core.ssa: on stmt: $phi326.2 = $322for_iter.3
2025-08-27 14:20:27,358 [DEBUG] numba.core.ssa: on stmt: branch $322for_iter.4, 326, 422
2025-08-27 14:20:27,358 [DEBUG] numba.core.ssa: ==== SSA block rewrite pass on 326
2025-08-27 14:20:27,358 [DEBUG] numba.core.ssa: Running <numba.core.ssa._FreshVarHandler object at 0x32e8b11d0>
2025-08-27 14:20:27,358 [DEBUG] numba.core.ssa: on stmt: i = $phi326.2
2025-08-27 14:20:27,358 [DEBUG] numba.core.ssa: on stmt: $binop_mul334.7 = i * index_step
2025-08-27 14:20:27,358 [DEBUG] numba.core.ssa: on stmt: $binop_add338.8 = offset + $binop_mul334.7
2025-08-27 14:20:27,358 [DEBUG] numba.core.ssa: on stmt: $342binary_subscr.9 = getitem(value=interp_win, index=$binop_add338.8, fn=<built-in function getitem>)
2025-08-27 14:20:27,358 [DEBUG] numba.core.ssa: on stmt: $binop_mul354.15 = i * index_step
2025-08-27 14:20:27,358 [DEBUG] numba.core.ssa: on stmt: $binop_add358.16 = offset + $binop_mul354.15
2025-08-27 14:20:27,358 [DEBUG] numba.core.ssa: on stmt: $362binary_subscr.17 = getitem(value=interp_delta, index=$binop_add358.16, fn=<built-in function getitem>)
2025-08-27 14:20:27,358 [DEBUG] numba.core.ssa: on stmt: $binop_mul366.18 = eta * $362binary_subscr.17
2025-08-27 14:20:27,358 [DEBUG] numba.core.ssa: on stmt: weight = $342binary_subscr.9 + $binop_mul366.18
2025-08-27 14:20:27,358 [DEBUG] numba.core.ssa: on stmt: $384binary_subscr.22 = getitem(value=y, index=t, fn=<built-in function getitem>)
2025-08-27 14:20:27,358 [DEBUG] numba.core.ssa: on stmt: $binop_sub394.27 = n - i
2025-08-27 14:20:27,358 [DEBUG] numba.core.ssa: on stmt: $398binary_subscr.28 = getitem(value=x, index=$binop_sub394.27, fn=<built-in function getitem>)
2025-08-27 14:20:27,358 [DEBUG] numba.core.ssa: on stmt: $binop_mul402.29 = weight * $398binary_subscr.28
2025-08-27 14:20:27,358 [DEBUG] numba.core.ssa: on stmt: $binop_iadd406.30 = inplace_binop(fn=<built-in function iadd>, immutable_fn=<built-in function add>, lhs=$384binary_subscr.22, rhs=$binop_mul402.29, static_lhs=Undefined, static_rhs=Undefined)
2025-08-27 14:20:27,358 [DEBUG] numba.core.ssa: on stmt: y[t] = $binop_iadd406.30
2025-08-27 14:20:27,358 [DEBUG] numba.core.ssa: on stmt: jump 322
2025-08-27 14:20:27,358 [DEBUG] numba.core.ssa: ==== SSA block rewrite pass on 422
2025-08-27 14:20:27,358 [DEBUG] numba.core.ssa: Running <numba.core.ssa._FreshVarHandler object at 0x32e8b11d0>
2025-08-27 14:20:27,358 [DEBUG] numba.core.ssa: on stmt: $binop_sub428.5 = scale - frac
2025-08-27 14:20:27,358 [DEBUG] numba.core.ssa: on stmt: frac = $binop_sub428.5
2025-08-27 14:20:27,358 [DEBUG] numba.core.ssa: on stmt: index_frac = frac * num_table
2025-08-27 14:20:27,358 [DEBUG] numba.core.ssa: on stmt: $442load_global.9 = global(int: <class 'int'>)
2025-08-27 14:20:27,358 [DEBUG] numba.core.ssa: on stmt: offset = call $442load_global.9(index_frac, func=$442load_global.9, args=[Var(index_frac, interpn.py:14)], kws=(), vararg=None, varkwarg=None, target=None)
2025-08-27 14:20:27,358 [DEBUG] numba.core.ssa: on stmt: eta = index_frac - offset
2025-08-27 14:20:27,358 [DEBUG] numba.core.ssa: on stmt: $472load_global.16 = global(min: <built-in function min>)
2025-08-27 14:20:27,358 [DEBUG] numba.core.ssa: on stmt: $binop_sub486.20 = n_orig - n
2025-08-27 14:20:27,358 [DEBUG] numba.core.ssa: on stmt: $const490.21.3 = const(int, 1)
2025-08-27 14:20:27,358 [DEBUG] numba.core.ssa: on stmt: $binop_sub492.22 = $binop_sub486.20 - $const490.21.3
2025-08-27 14:20:27,358 [DEBUG] numba.core.ssa: on stmt: $binop_sub498.25 = nwin - offset
2025-08-27 14:20:27,358 [DEBUG] numba.core.ssa: on stmt: $binop_floordiv504.27 = $binop_sub498.25 // index_step
2025-08-27 14:20:27,358 [DEBUG] numba.core.ssa: on stmt: k_max = call $472load_global.16($binop_sub492.22, $binop_floordiv504.27, func=$472load_global.16, args=[Var($binop_sub492.22, interpn.py:60), Var($binop_floordiv504.27, interpn.py:60)], kws=(), vararg=None, varkwarg=None, target=None)
2025-08-27 14:20:27,358 [DEBUG] numba.core.ssa: on stmt: $518load_global.29 = global(range: <class 'range'>)
2025-08-27 14:20:27,358 [DEBUG] numba.core.ssa: on stmt: $530call.32 = call $518load_global.29(k_max, func=$518load_global.29, args=[Var(k_max, interpn.py:60)], kws=(), vararg=None, varkwarg=None, target=None)
2025-08-27 14:20:27,358 [DEBUG] numba.core.ssa: on stmt: $538get_iter.33 = getiter(value=$530call.32)
2025-08-27 14:20:27,358 [DEBUG] numba.core.ssa: on stmt: $phi540.1 = $538get_iter.33
2025-08-27 14:20:27,358 [DEBUG] numba.core.ssa: on stmt: jump 540
2025-08-27 14:20:27,358 [DEBUG] numba.core.ssa: ==== SSA block rewrite pass on 540
2025-08-27 14:20:27,358 [DEBUG] numba.core.ssa: Running <numba.core.ssa._FreshVarHandler object at 0x32e8b11d0>
2025-08-27 14:20:27,358 [DEBUG] numba.core.ssa: on stmt: $540for_iter.2 = iternext(value=$phi540.1)
2025-08-27 14:20:27,358 [DEBUG] numba.core.ssa: on stmt: $540for_iter.3 = pair_first(value=$540for_iter.2)
2025-08-27 14:20:27,358 [DEBUG] numba.core.ssa: on stmt: $540for_iter.4 = pair_second(value=$540for_iter.2)
2025-08-27 14:20:27,358 [DEBUG] numba.core.ssa: on stmt: $phi544.2 = $540for_iter.3
2025-08-27 14:20:27,359 [DEBUG] numba.core.ssa: on stmt: branch $540for_iter.4, 544, 646
2025-08-27 14:20:27,359 [DEBUG] numba.core.ssa: ==== SSA block rewrite pass on 544
2025-08-27 14:20:27,359 [DEBUG] numba.core.ssa: Running <numba.core.ssa._FreshVarHandler object at 0x32e8b11d0>
2025-08-27 14:20:27,359 [DEBUG] numba.core.ssa: on stmt: k = $phi544.2
2025-08-27 14:20:27,359 [DEBUG] numba.core.ssa: on stmt: $binop_mul552.7 = k * index_step
2025-08-27 14:20:27,359 [DEBUG] numba.core.ssa: on stmt: $binop_add556.8 = offset + $binop_mul552.7
2025-08-27 14:20:27,359 [DEBUG] numba.core.ssa: on stmt: $560binary_subscr.9 = getitem(value=interp_win, index=$binop_add556.8, fn=<built-in function getitem>)
2025-08-27 14:20:27,359 [DEBUG] numba.core.ssa: on stmt: $binop_mul572.15 = k * index_step
2025-08-27 14:20:27,359 [DEBUG] numba.core.ssa: on stmt: $binop_add576.16 = offset + $binop_mul572.15
2025-08-27 14:20:27,359 [DEBUG] numba.core.ssa: on stmt: $580binary_subscr.17 = getitem(value=interp_delta, index=$binop_add576.16, fn=<built-in function getitem>)
2025-08-27 14:20:27,359 [DEBUG] numba.core.ssa: on stmt: $binop_mul584.18 = eta * $580binary_subscr.17
2025-08-27 14:20:27,359 [DEBUG] numba.core.ssa: on stmt: weight = $560binary_subscr.9 + $binop_mul584.18
2025-08-27 14:20:27,359 [DEBUG] numba.core.ssa: on stmt: $602binary_subscr.22 = getitem(value=y, index=t, fn=<built-in function getitem>)
2025-08-27 14:20:27,359 [DEBUG] numba.core.ssa: on stmt: $binop_add612.27 = n + k
2025-08-27 14:20:27,359 [DEBUG] numba.core.ssa: on stmt: $const616.28.3 = const(int, 1)
2025-08-27 14:20:27,359 [DEBUG] numba.core.ssa: on stmt: $binop_add618.29 = $binop_add612.27 + $const616.28.3
2025-08-27 14:20:27,359 [DEBUG] numba.core.ssa: on stmt: $622binary_subscr.30 = getitem(value=x, index=$binop_add618.29, fn=<built-in function getitem>)
2025-08-27 14:20:27,359 [DEBUG] numba.core.ssa: on stmt: $binop_mul626.31 = weight * $622binary_subscr.30
2025-08-27 14:20:27,359 [DEBUG] numba.core.ssa: on stmt: $binop_iadd630.32 = inplace_binop(fn=<built-in function iadd>, immutable_fn=<built-in function add>, lhs=$602binary_subscr.22, rhs=$binop_mul626.31, static_lhs=Undefined, static_rhs=Undefined)
2025-08-27 14:20:27,359 [DEBUG] numba.core.ssa: on stmt: y[t] = $binop_iadd630.32
2025-08-27 14:20:27,359 [DEBUG] numba.core.ssa: on stmt: jump 540
2025-08-27 14:20:27,359 [DEBUG] numba.core.ssa: ==== SSA block rewrite pass on 646
2025-08-27 14:20:27,359 [DEBUG] numba.core.ssa: Running <numba.core.ssa._FreshVarHandler object at 0x32e8b11d0>
2025-08-27 14:20:27,359 [DEBUG] numba.core.ssa: on stmt: jump 170
2025-08-27 14:20:27,359 [DEBUG] numba.core.ssa: ==== SSA block rewrite pass on 654
2025-08-27 14:20:27,359 [DEBUG] numba.core.ssa: Running <numba.core.ssa._FreshVarHandler object at 0x32e8b11d0>
2025-08-27 14:20:27,359 [DEBUG] numba.core.ssa: on stmt: $const658.2 = const(NoneType, None)
2025-08-27 14:20:27,359 [DEBUG] numba.core.ssa: on stmt: $658return_const.3 = cast(value=$const658.2)
2025-08-27 14:20:27,359 [DEBUG] numba.core.ssa: on stmt: return $658return_const.3
2025-08-27 14:20:27,359 [DEBUG] numba.core.ssa: Replaced assignments: defaultdict(<class 'list'>,
            {0: [<numba.core.ir.Assign object at 0x32ea05bb0>],
             174: [<numba.core.ir.Assign object at 0x32ea05c10>]})
2025-08-27 14:20:27,359 [DEBUG] numba.core.ssa: ==== SSA block rewrite pass on 0
2025-08-27 14:20:27,359 [DEBUG] numba.core.ssa: Running <numba.core.ssa._FixSSAVars object at 0x32e8b11d0>
2025-08-27 14:20:27,359 [DEBUG] numba.core.ssa: on stmt: x = arg(0, name=x)
2025-08-27 14:20:27,359 [DEBUG] numba.core.ssa: on stmt: t_out = arg(1, name=t_out)
2025-08-27 14:20:27,359 [DEBUG] numba.core.ssa: on stmt: interp_win = arg(2, name=interp_win)
2025-08-27 14:20:27,359 [DEBUG] numba.core.ssa: on stmt: interp_delta = arg(3, name=interp_delta)
2025-08-27 14:20:27,359 [DEBUG] numba.core.ssa: on stmt: num_table = arg(4, name=num_table)
2025-08-27 14:20:27,359 [DEBUG] numba.core.ssa: on stmt: scale = arg(5, name=scale)
2025-08-27 14:20:27,359 [DEBUG] numba.core.ssa: on stmt: y = arg(6, name=y)
2025-08-27 14:20:27,359 [DEBUG] numba.core.ssa: on stmt: $4load_global.0 = global(int: <class 'int'>)
2025-08-27 14:20:27,359 [DEBUG] numba.core.ssa: on stmt: $binop_mul16.4 = scale * num_table
2025-08-27 14:20:27,359 [DEBUG] numba.core.ssa: on stmt: index_step = call $4load_global.0($binop_mul16.4, func=$4load_global.0, args=[Var($binop_mul16.4, interpn.py:9)], kws=(), vararg=None, varkwarg=None, target=None)
2025-08-27 14:20:27,359 [DEBUG] numba.core.ssa: on stmt: time_register = const(float, 0.0)
2025-08-27 14:20:27,359 [DEBUG] numba.core.ssa: on stmt: n = const(int, 0)
2025-08-27 14:20:27,359 [DEBUG] numba.core.ssa: on stmt: frac = const(float, 0.0)
2025-08-27 14:20:27,359 [DEBUG] numba.core.ssa: on stmt: index_frac = const(float, 0.0)
2025-08-27 14:20:27,359 [DEBUG] numba.core.ssa: on stmt: offset = const(int, 0)
2025-08-27 14:20:27,359 [DEBUG] numba.core.ssa: on stmt: eta = const(float, 0.0)
2025-08-27 14:20:27,359 [DEBUG] numba.core.ssa: on stmt: weight = const(float, 0.0)
2025-08-27 14:20:27,359 [DEBUG] numba.core.ssa: on stmt: $60load_attr.14 = getattr(value=interp_win, attr=shape)
2025-08-27 14:20:27,359 [DEBUG] numba.core.ssa: on stmt: $const80.15.2 = const(int, 0)
2025-08-27 14:20:27,359 [DEBUG] numba.core.ssa: on stmt: nwin = static_getitem(value=$60load_attr.14, index=0, index_var=$const80.15.2, fn=<built-in function getitem>)
2025-08-27 14:20:27,359 [DEBUG] numba.core.ssa: on stmt: $90load_attr.18 = getattr(value=x, attr=shape)
2025-08-27 14:20:27,359 [DEBUG] numba.core.ssa: on stmt: $const110.19.2 = const(int, 0)
2025-08-27 14:20:27,359 [DEBUG] numba.core.ssa: on stmt: n_orig = static_getitem(value=$90load_attr.18, index=0, index_var=$const110.19.2, fn=<built-in function getitem>)
2025-08-27 14:20:27,359 [DEBUG] numba.core.ssa: on stmt: $120load_attr.22 = getattr(value=t_out, attr=shape)
2025-08-27 14:20:27,359 [DEBUG] numba.core.ssa: on stmt: $const140.23.2 = const(int, 0)
2025-08-27 14:20:27,359 [DEBUG] numba.core.ssa: on stmt: n_out = static_getitem(value=$120load_attr.22, index=0, index_var=$const140.23.2, fn=<built-in function getitem>)
2025-08-27 14:20:27,359 [DEBUG] numba.core.ssa: on stmt: $148load_global.25 = global(prange: <class 'numba.misc.special.prange'>)
2025-08-27 14:20:27,359 [DEBUG] numba.core.ssa: on stmt: $160call.28 = call $148load_global.25(n_out, func=$148load_global.25, args=[Var(n_out, interpn.py:21)], kws=(), vararg=None, varkwarg=None, target=None)
2025-08-27 14:20:27,359 [DEBUG] numba.core.ssa: on stmt: $168get_iter.29 = getiter(value=$160call.28)
2025-08-27 14:20:27,359 [DEBUG] numba.core.ssa: on stmt: $phi170.0 = $168get_iter.29
2025-08-27 14:20:27,359 [DEBUG] numba.core.ssa: on stmt: jump 170
2025-08-27 14:20:27,359 [DEBUG] numba.core.ssa: ==== SSA block rewrite pass on 170
2025-08-27 14:20:27,359 [DEBUG] numba.core.ssa: Running <numba.core.ssa._FixSSAVars object at 0x32e8b11d0>
2025-08-27 14:20:27,359 [DEBUG] numba.core.ssa: on stmt: $170for_iter.1 = iternext(value=$phi170.0)
2025-08-27 14:20:27,359 [DEBUG] numba.core.ssa: on stmt: $170for_iter.2 = pair_first(value=$170for_iter.1)
2025-08-27 14:20:27,359 [DEBUG] numba.core.ssa: on stmt: $170for_iter.3 = pair_second(value=$170for_iter.1)
2025-08-27 14:20:27,359 [DEBUG] numba.core.ssa: on stmt: $phi174.1 = $170for_iter.2
2025-08-27 14:20:27,359 [DEBUG] numba.core.ssa: on stmt: branch $170for_iter.3, 174, 654
2025-08-27 14:20:27,360 [DEBUG] numba.core.ssa: ==== SSA block rewrite pass on 174
2025-08-27 14:20:27,360 [DEBUG] numba.core.ssa: Running <numba.core.ssa._FixSSAVars object at 0x32e8b11d0>
2025-08-27 14:20:27,360 [DEBUG] numba.core.ssa: on stmt: t = $phi174.1
2025-08-27 14:20:27,360 [DEBUG] numba.core.ssa: on stmt: time_register.1 = getitem(value=t_out, index=t, fn=<built-in function getitem>)
2025-08-27 14:20:27,360 [DEBUG] numba.core.ssa: on stmt: $186load_global.5 = global(int: <class 'int'>)
2025-08-27 14:20:27,360 [DEBUG] numba.core.ssa: on stmt: n.1 = call $186load_global.5(time_register.1, func=$186load_global.5, args=[Var(time_register.1, interpn.py:24)], kws=(), vararg=None, varkwarg=None, target=None)
2025-08-27 14:20:27,360 [DEBUG] numba.core.ssa: on stmt: $binop_sub212.12 = time_register.1 - n
2025-08-27 14:20:27,360 [DEBUG] numba.core.ssa: find_def var='n' stmt=$binop_sub212.12 = time_register.1 - n
2025-08-27 14:20:27,360 [DEBUG] numba.core.ssa: replaced with: $binop_sub212.12 = time_register.1 - n.1
2025-08-27 14:20:27,360 [DEBUG] numba.core.ssa: on stmt: frac = scale * $binop_sub212.12
2025-08-27 14:20:27,360 [DEBUG] numba.core.ssa: on stmt: index_frac = frac * num_table
2025-08-27 14:20:27,360 [DEBUG] numba.core.ssa: on stmt: $230load_global.17 = global(int: <class 'int'>)
2025-08-27 14:20:27,360 [DEBUG] numba.core.ssa: on stmt: offset = call $230load_global.17(index_frac, func=$230load_global.17, args=[Var(index_frac, interpn.py:14)], kws=(), vararg=None, varkwarg=None, target=None)
2025-08-27 14:20:27,360 [DEBUG] numba.core.ssa: on stmt: eta = index_frac - offset
2025-08-27 14:20:27,360 [DEBUG] numba.core.ssa: on stmt: $260load_global.24 = global(min: <built-in function min>)
2025-08-27 14:20:27,360 [DEBUG] numba.core.ssa: on stmt: $const272.27.3 = const(int, 1)
2025-08-27 14:20:27,360 [DEBUG] numba.core.ssa: on stmt: $binop_add274.28 = n + $const272.27.3
2025-08-27 14:20:27,360 [DEBUG] numba.core.ssa: find_def var='n' stmt=$binop_add274.28 = n + $const272.27.3
2025-08-27 14:20:27,360 [DEBUG] numba.core.ssa: replaced with: $binop_add274.28 = n.1 + $const272.27.3
2025-08-27 14:20:27,360 [DEBUG] numba.core.ssa: on stmt: $binop_sub280.31 = nwin - offset
2025-08-27 14:20:27,360 [DEBUG] numba.core.ssa: on stmt: $binop_floordiv286.33 = $binop_sub280.31 // index_step
2025-08-27 14:20:27,360 [DEBUG] numba.core.ssa: on stmt: i_max = call $260load_global.24($binop_add274.28, $binop_floordiv286.33, func=$260load_global.24, args=[Var($binop_add274.28, interpn.py:40), Var($binop_floordiv286.33, interpn.py:40)], kws=(), vararg=None, varkwarg=None, target=None)
2025-08-27 14:20:27,360 [DEBUG] numba.core.ssa: on stmt: $300load_global.35 = global(range: <class 'range'>)
2025-08-27 14:20:27,360 [DEBUG] numba.core.ssa: on stmt: $312call.38 = call $300load_global.35(i_max, func=$300load_global.35, args=[Var(i_max, interpn.py:40)], kws=(), vararg=None, varkwarg=None, target=None)
2025-08-27 14:20:27,360 [DEBUG] numba.core.ssa: on stmt: $320get_iter.39 = getiter(value=$312call.38)
2025-08-27 14:20:27,360 [DEBUG] numba.core.ssa: on stmt: $phi322.1 = $320get_iter.39
2025-08-27 14:20:27,360 [DEBUG] numba.core.ssa: on stmt: jump 322
2025-08-27 14:20:27,360 [DEBUG] numba.core.ssa: ==== SSA block rewrite pass on 322
2025-08-27 14:20:27,360 [DEBUG] numba.core.ssa: Running <numba.core.ssa._FixSSAVars object at 0x32e8b11d0>
2025-08-27 14:20:27,360 [DEBUG] numba.core.ssa: on stmt: $322for_iter.2 = iternext(value=$phi322.1)
2025-08-27 14:20:27,360 [DEBUG] numba.core.ssa: on stmt: $322for_iter.3 = pair_first(value=$322for_iter.2)
2025-08-27 14:20:27,360 [DEBUG] numba.core.ssa: on stmt: $322for_iter.4 = pair_second(value=$322for_iter.2)
2025-08-27 14:20:27,360 [DEBUG] numba.core.ssa: on stmt: $phi326.2 = $322for_iter.3
2025-08-27 14:20:27,360 [DEBUG] numba.core.ssa: on stmt: branch $322for_iter.4, 326, 422
2025-08-27 14:20:27,360 [DEBUG] numba.core.ssa: ==== SSA block rewrite pass on 326
2025-08-27 14:20:27,360 [DEBUG] numba.core.ssa: Running <numba.core.ssa._FixSSAVars object at 0x32e8b11d0>
2025-08-27 14:20:27,360 [DEBUG] numba.core.ssa: on stmt: i = $phi326.2
2025-08-27 14:20:27,360 [DEBUG] numba.core.ssa: on stmt: $binop_mul334.7 = i * index_step
2025-08-27 14:20:27,360 [DEBUG] numba.core.ssa: on stmt: $binop_add338.8 = offset + $binop_mul334.7
2025-08-27 14:20:27,360 [DEBUG] numba.core.ssa: on stmt: $342binary_subscr.9 = getitem(value=interp_win, index=$binop_add338.8, fn=<built-in function getitem>)
2025-08-27 14:20:27,360 [DEBUG] numba.core.ssa: on stmt: $binop_mul354.15 = i * index_step
2025-08-27 14:20:27,360 [DEBUG] numba.core.ssa: on stmt: $binop_add358.16 = offset + $binop_mul354.15
2025-08-27 14:20:27,360 [DEBUG] numba.core.ssa: on stmt: $362binary_subscr.17 = getitem(value=interp_delta, index=$binop_add358.16, fn=<built-in function getitem>)
2025-08-27 14:20:27,360 [DEBUG] numba.core.ssa: on stmt: $binop_mul366.18 = eta * $362binary_subscr.17
2025-08-27 14:20:27,360 [DEBUG] numba.core.ssa: on stmt: weight = $342binary_subscr.9 + $binop_mul366.18
2025-08-27 14:20:27,360 [DEBUG] numba.core.ssa: on stmt: $384binary_subscr.22 = getitem(value=y, index=t, fn=<built-in function getitem>)
2025-08-27 14:20:27,360 [DEBUG] numba.core.ssa: on stmt: $binop_sub394.27 = n - i
2025-08-27 14:20:27,360 [DEBUG] numba.core.ssa: find_def var='n' stmt=$binop_sub394.27 = n - i
2025-08-27 14:20:27,360 [DEBUG] numba.core.ssa: find_def_from_top label 326
2025-08-27 14:20:27,360 [DEBUG] numba.core.ssa: idom 322 from label 326
2025-08-27 14:20:27,360 [DEBUG] numba.core.ssa: find_def_from_bottom label 322
2025-08-27 14:20:27,360 [DEBUG] numba.core.ssa: find_def_from_top label 322
2025-08-27 14:20:27,360 [DEBUG] numba.core.ssa: idom 174 from label 322
2025-08-27 14:20:27,360 [DEBUG] numba.core.ssa: find_def_from_bottom label 174
2025-08-27 14:20:27,360 [DEBUG] numba.core.ssa: replaced with: $binop_sub394.27 = n.1 - i
2025-08-27 14:20:27,360 [DEBUG] numba.core.ssa: on stmt: $398binary_subscr.28 = getitem(value=x, index=$binop_sub394.27, fn=<built-in function getitem>)
2025-08-27 14:20:27,360 [DEBUG] numba.core.ssa: on stmt: $binop_mul402.29 = weight * $398binary_subscr.28
2025-08-27 14:20:27,360 [DEBUG] numba.core.ssa: on stmt: $binop_iadd406.30 = inplace_binop(fn=<built-in function iadd>, immutable_fn=<built-in function add>, lhs=$384binary_subscr.22, rhs=$binop_mul402.29, static_lhs=Undefined, static_rhs=Undefined)
2025-08-27 14:20:27,360 [DEBUG] numba.core.ssa: on stmt: y[t] = $binop_iadd406.30
2025-08-27 14:20:27,360 [DEBUG] numba.core.ssa: on stmt: jump 322
2025-08-27 14:20:27,360 [DEBUG] numba.core.ssa: ==== SSA block rewrite pass on 422
2025-08-27 14:20:27,360 [DEBUG] numba.core.ssa: Running <numba.core.ssa._FixSSAVars object at 0x32e8b11d0>
2025-08-27 14:20:27,360 [DEBUG] numba.core.ssa: on stmt: $binop_sub428.5 = scale - frac
2025-08-27 14:20:27,360 [DEBUG] numba.core.ssa: on stmt: frac = $binop_sub428.5
2025-08-27 14:20:27,360 [DEBUG] numba.core.ssa: on stmt: index_frac = frac * num_table
2025-08-27 14:20:27,360 [DEBUG] numba.core.ssa: on stmt: $442load_global.9 = global(int: <class 'int'>)
2025-08-27 14:20:27,360 [DEBUG] numba.core.ssa: on stmt: offset = call $442load_global.9(index_frac, func=$442load_global.9, args=[Var(index_frac, interpn.py:14)], kws=(), vararg=None, varkwarg=None, target=None)
2025-08-27 14:20:27,360 [DEBUG] numba.core.ssa: on stmt: eta = index_frac - offset
2025-08-27 14:20:27,361 [DEBUG] numba.core.ssa: on stmt: $472load_global.16 = global(min: <built-in function min>)
2025-08-27 14:20:27,361 [DEBUG] numba.core.ssa: on stmt: $binop_sub486.20 = n_orig - n
2025-08-27 14:20:27,361 [DEBUG] numba.core.ssa: find_def var='n' stmt=$binop_sub486.20 = n_orig - n
2025-08-27 14:20:27,361 [DEBUG] numba.core.ssa: find_def_from_top label 422
2025-08-27 14:20:27,361 [DEBUG] numba.core.ssa: idom 322 from label 422
2025-08-27 14:20:27,361 [DEBUG] numba.core.ssa: find_def_from_bottom label 322
2025-08-27 14:20:27,361 [DEBUG] numba.core.ssa: find_def_from_top label 322
2025-08-27 14:20:27,361 [DEBUG] numba.core.ssa: idom 174 from label 322
2025-08-27 14:20:27,361 [DEBUG] numba.core.ssa: find_def_from_bottom label 174
2025-08-27 14:20:27,361 [DEBUG] numba.core.ssa: replaced with: $binop_sub486.20 = n_orig - n.1
2025-08-27 14:20:27,361 [DEBUG] numba.core.ssa: on stmt: $const490.21.3 = const(int, 1)
2025-08-27 14:20:27,361 [DEBUG] numba.core.ssa: on stmt: $binop_sub492.22 = $binop_sub486.20 - $const490.21.3
2025-08-27 14:20:27,361 [DEBUG] numba.core.ssa: on stmt: $binop_sub498.25 = nwin - offset
2025-08-27 14:20:27,361 [DEBUG] numba.core.ssa: on stmt: $binop_floordiv504.27 = $binop_sub498.25 // index_step
2025-08-27 14:20:27,361 [DEBUG] numba.core.ssa: on stmt: k_max = call $472load_global.16($binop_sub492.22, $binop_floordiv504.27, func=$472load_global.16, args=[Var($binop_sub492.22, interpn.py:60), Var($binop_floordiv504.27, interpn.py:60)], kws=(), vararg=None, varkwarg=None, target=None)
2025-08-27 14:20:27,361 [DEBUG] numba.core.ssa: on stmt: $518load_global.29 = global(range: <class 'range'>)
2025-08-27 14:20:27,361 [DEBUG] numba.core.ssa: on stmt: $530call.32 = call $518load_global.29(k_max, func=$518load_global.29, args=[Var(k_max, interpn.py:60)], kws=(), vararg=None, varkwarg=None, target=None)
2025-08-27 14:20:27,361 [DEBUG] numba.core.ssa: on stmt: $538get_iter.33 = getiter(value=$530call.32)
2025-08-27 14:20:27,361 [DEBUG] numba.core.ssa: on stmt: $phi540.1 = $538get_iter.33
2025-08-27 14:20:27,361 [DEBUG] numba.core.ssa: on stmt: jump 540
2025-08-27 14:20:27,361 [DEBUG] numba.core.ssa: ==== SSA block rewrite pass on 540
2025-08-27 14:20:27,361 [DEBUG] numba.core.ssa: Running <numba.core.ssa._FixSSAVars object at 0x32e8b11d0>
2025-08-27 14:20:27,361 [DEBUG] numba.core.ssa: on stmt: $540for_iter.2 = iternext(value=$phi540.1)
2025-08-27 14:20:27,361 [DEBUG] numba.core.ssa: on stmt: $540for_iter.3 = pair_first(value=$540for_iter.2)
2025-08-27 14:20:27,361 [DEBUG] numba.core.ssa: on stmt: $540for_iter.4 = pair_second(value=$540for_iter.2)
2025-08-27 14:20:27,361 [DEBUG] numba.core.ssa: on stmt: $phi544.2 = $540for_iter.3
2025-08-27 14:20:27,361 [DEBUG] numba.core.ssa: on stmt: branch $540for_iter.4, 544, 646
2025-08-27 14:20:27,361 [DEBUG] numba.core.ssa: ==== SSA block rewrite pass on 544
2025-08-27 14:20:27,361 [DEBUG] numba.core.ssa: Running <numba.core.ssa._FixSSAVars object at 0x32e8b11d0>
2025-08-27 14:20:27,361 [DEBUG] numba.core.ssa: on stmt: k = $phi544.2
2025-08-27 14:20:27,361 [DEBUG] numba.core.ssa: on stmt: $binop_mul552.7 = k * index_step
2025-08-27 14:20:27,361 [DEBUG] numba.core.ssa: on stmt: $binop_add556.8 = offset + $binop_mul552.7
2025-08-27 14:20:27,361 [DEBUG] numba.core.ssa: on stmt: $560binary_subscr.9 = getitem(value=interp_win, index=$binop_add556.8, fn=<built-in function getitem>)
2025-08-27 14:20:27,361 [DEBUG] numba.core.ssa: on stmt: $binop_mul572.15 = k * index_step
2025-08-27 14:20:27,361 [DEBUG] numba.core.ssa: on stmt: $binop_add576.16 = offset + $binop_mul572.15
2025-08-27 14:20:27,361 [DEBUG] numba.core.ssa: on stmt: $580binary_subscr.17 = getitem(value=interp_delta, index=$binop_add576.16, fn=<built-in function getitem>)
2025-08-27 14:20:27,361 [DEBUG] numba.core.ssa: on stmt: $binop_mul584.18 = eta * $580binary_subscr.17
2025-08-27 14:20:27,361 [DEBUG] numba.core.ssa: on stmt: weight = $560binary_subscr.9 + $binop_mul584.18
2025-08-27 14:20:27,361 [DEBUG] numba.core.ssa: on stmt: $602binary_subscr.22 = getitem(value=y, index=t, fn=<built-in function getitem>)
2025-08-27 14:20:27,361 [DEBUG] numba.core.ssa: on stmt: $binop_add612.27 = n + k
2025-08-27 14:20:27,361 [DEBUG] numba.core.ssa: find_def var='n' stmt=$binop_add612.27 = n + k
2025-08-27 14:20:27,361 [DEBUG] numba.core.ssa: find_def_from_top label 544
2025-08-27 14:20:27,361 [DEBUG] numba.core.ssa: idom 540 from label 544
2025-08-27 14:20:27,361 [DEBUG] numba.core.ssa: find_def_from_bottom label 540
2025-08-27 14:20:27,361 [DEBUG] numba.core.ssa: find_def_from_top label 540
2025-08-27 14:20:27,361 [DEBUG] numba.core.ssa: idom 422 from label 540
2025-08-27 14:20:27,361 [DEBUG] numba.core.ssa: find_def_from_bottom label 422
2025-08-27 14:20:27,361 [DEBUG] numba.core.ssa: find_def_from_top label 422
2025-08-27 14:20:27,361 [DEBUG] numba.core.ssa: idom 322 from label 422
2025-08-27 14:20:27,361 [DEBUG] numba.core.ssa: find_def_from_bottom label 322
2025-08-27 14:20:27,361 [DEBUG] numba.core.ssa: find_def_from_top label 322
2025-08-27 14:20:27,361 [DEBUG] numba.core.ssa: idom 174 from label 322
2025-08-27 14:20:27,361 [DEBUG] numba.core.ssa: find_def_from_bottom label 174
2025-08-27 14:20:27,361 [DEBUG] numba.core.ssa: replaced with: $binop_add612.27 = n.1 + k
2025-08-27 14:20:27,361 [DEBUG] numba.core.ssa: on stmt: $const616.28.3 = const(int, 1)
2025-08-27 14:20:27,361 [DEBUG] numba.core.ssa: on stmt: $binop_add618.29 = $binop_add612.27 + $const616.28.3
2025-08-27 14:20:27,361 [DEBUG] numba.core.ssa: on stmt: $622binary_subscr.30 = getitem(value=x, index=$binop_add618.29, fn=<built-in function getitem>)
2025-08-27 14:20:27,361 [DEBUG] numba.core.ssa: on stmt: $binop_mul626.31 = weight * $622binary_subscr.30
2025-08-27 14:20:27,361 [DEBUG] numba.core.ssa: on stmt: $binop_iadd630.32 = inplace_binop(fn=<built-in function iadd>, immutable_fn=<built-in function add>, lhs=$602binary_subscr.22, rhs=$binop_mul626.31, static_lhs=Undefined, static_rhs=Undefined)
2025-08-27 14:20:27,361 [DEBUG] numba.core.ssa: on stmt: y[t] = $binop_iadd630.32
2025-08-27 14:20:27,361 [DEBUG] numba.core.ssa: on stmt: jump 540
2025-08-27 14:20:27,361 [DEBUG] numba.core.ssa: ==== SSA block rewrite pass on 646
2025-08-27 14:20:27,361 [DEBUG] numba.core.ssa: Running <numba.core.ssa._FixSSAVars object at 0x32e8b11d0>
2025-08-27 14:20:27,361 [DEBUG] numba.core.ssa: on stmt: jump 170
2025-08-27 14:20:27,361 [DEBUG] numba.core.ssa: ==== SSA block rewrite pass on 654
2025-08-27 14:20:27,361 [DEBUG] numba.core.ssa: Running <numba.core.ssa._FixSSAVars object at 0x32e8b11d0>
2025-08-27 14:20:27,361 [DEBUG] numba.core.ssa: on stmt: $const658.2 = const(NoneType, None)
2025-08-27 14:20:27,361 [DEBUG] numba.core.ssa: on stmt: $658return_const.3 = cast(value=$const658.2)
2025-08-27 14:20:27,361 [DEBUG] numba.core.ssa: on stmt: return $658return_const.3
2025-08-27 14:20:27,361 [DEBUG] numba.core.ssa: Fix SSA violator on var frac
2025-08-27 14:20:27,361 [DEBUG] numba.core.ssa: ==== SSA block rewrite pass on 0
2025-08-27 14:20:27,361 [DEBUG] numba.core.ssa: Running <numba.core.ssa._FreshVarHandler object at 0x32e8b11d0>
2025-08-27 14:20:27,361 [DEBUG] numba.core.ssa: on stmt: x = arg(0, name=x)
2025-08-27 14:20:27,362 [DEBUG] numba.core.ssa: on stmt: t_out = arg(1, name=t_out)
2025-08-27 14:20:27,362 [DEBUG] numba.core.ssa: on stmt: interp_win = arg(2, name=interp_win)
2025-08-27 14:20:27,362 [DEBUG] numba.core.ssa: on stmt: interp_delta = arg(3, name=interp_delta)
2025-08-27 14:20:27,362 [DEBUG] numba.core.ssa: on stmt: num_table = arg(4, name=num_table)
2025-08-27 14:20:27,362 [DEBUG] numba.core.ssa: on stmt: scale = arg(5, name=scale)
2025-08-27 14:20:27,362 [DEBUG] numba.core.ssa: on stmt: y = arg(6, name=y)
2025-08-27 14:20:27,362 [DEBUG] numba.core.ssa: on stmt: $4load_global.0 = global(int: <class 'int'>)
2025-08-27 14:20:27,362 [DEBUG] numba.core.ssa: on stmt: $binop_mul16.4 = scale * num_table
2025-08-27 14:20:27,362 [DEBUG] numba.core.ssa: on stmt: index_step = call $4load_global.0($binop_mul16.4, func=$4load_global.0, args=[Var($binop_mul16.4, interpn.py:9)], kws=(), vararg=None, varkwarg=None, target=None)
2025-08-27 14:20:27,362 [DEBUG] numba.core.ssa: on stmt: time_register = const(float, 0.0)
2025-08-27 14:20:27,362 [DEBUG] numba.core.ssa: on stmt: n = const(int, 0)
2025-08-27 14:20:27,362 [DEBUG] numba.core.ssa: on stmt: frac = const(float, 0.0)
2025-08-27 14:20:27,362 [DEBUG] numba.core.ssa: first assign: frac
2025-08-27 14:20:27,362 [DEBUG] numba.core.ssa: replaced with: frac = const(float, 0.0)
2025-08-27 14:20:27,362 [DEBUG] numba.core.ssa: on stmt: index_frac = const(float, 0.0)
2025-08-27 14:20:27,362 [DEBUG] numba.core.ssa: on stmt: offset = const(int, 0)
2025-08-27 14:20:27,362 [DEBUG] numba.core.ssa: on stmt: eta = const(float, 0.0)
2025-08-27 14:20:27,362 [DEBUG] numba.core.ssa: on stmt: weight = const(float, 0.0)
2025-08-27 14:20:27,362 [DEBUG] numba.core.ssa: on stmt: $60load_attr.14 = getattr(value=interp_win, attr=shape)
2025-08-27 14:20:27,362 [DEBUG] numba.core.ssa: on stmt: $const80.15.2 = const(int, 0)
2025-08-27 14:20:27,362 [DEBUG] numba.core.ssa: on stmt: nwin = static_getitem(value=$60load_attr.14, index=0, index_var=$const80.15.2, fn=<built-in function getitem>)
2025-08-27 14:20:27,362 [DEBUG] numba.core.ssa: on stmt: $90load_attr.18 = getattr(value=x, attr=shape)
2025-08-27 14:20:27,362 [DEBUG] numba.core.ssa: on stmt: $const110.19.2 = const(int, 0)
2025-08-27 14:20:27,362 [DEBUG] numba.core.ssa: on stmt: n_orig = static_getitem(value=$90load_attr.18, index=0, index_var=$const110.19.2, fn=<built-in function getitem>)
2025-08-27 14:20:27,362 [DEBUG] numba.core.ssa: on stmt: $120load_attr.22 = getattr(value=t_out, attr=shape)
2025-08-27 14:20:27,362 [DEBUG] numba.core.ssa: on stmt: $const140.23.2 = const(int, 0)
2025-08-27 14:20:27,362 [DEBUG] numba.core.ssa: on stmt: n_out = static_getitem(value=$120load_attr.22, index=0, index_var=$const140.23.2, fn=<built-in function getitem>)
2025-08-27 14:20:27,362 [DEBUG] numba.core.ssa: on stmt: $148load_global.25 = global(prange: <class 'numba.misc.special.prange'>)
2025-08-27 14:20:27,362 [DEBUG] numba.core.ssa: on stmt: $160call.28 = call $148load_global.25(n_out, func=$148load_global.25, args=[Var(n_out, interpn.py:21)], kws=(), vararg=None, varkwarg=None, target=None)
2025-08-27 14:20:27,362 [DEBUG] numba.core.ssa: on stmt: $168get_iter.29 = getiter(value=$160call.28)
2025-08-27 14:20:27,362 [DEBUG] numba.core.ssa: on stmt: $phi170.0 = $168get_iter.29
2025-08-27 14:20:27,362 [DEBUG] numba.core.ssa: on stmt: jump 170
2025-08-27 14:20:27,362 [DEBUG] numba.core.ssa: ==== SSA block rewrite pass on 170
2025-08-27 14:20:27,362 [DEBUG] numba.core.ssa: Running <numba.core.ssa._FreshVarHandler object at 0x32e8b11d0>
2025-08-27 14:20:27,362 [DEBUG] numba.core.ssa: on stmt: $170for_iter.1 = iternext(value=$phi170.0)
2025-08-27 14:20:27,362 [DEBUG] numba.core.ssa: on stmt: $170for_iter.2 = pair_first(value=$170for_iter.1)
2025-08-27 14:20:27,362 [DEBUG] numba.core.ssa: on stmt: $170for_iter.3 = pair_second(value=$170for_iter.1)
2025-08-27 14:20:27,362 [DEBUG] numba.core.ssa: on stmt: $phi174.1 = $170for_iter.2
2025-08-27 14:20:27,362 [DEBUG] numba.core.ssa: on stmt: branch $170for_iter.3, 174, 654
2025-08-27 14:20:27,362 [DEBUG] numba.core.ssa: ==== SSA block rewrite pass on 174
2025-08-27 14:20:27,362 [DEBUG] numba.core.ssa: Running <numba.core.ssa._FreshVarHandler object at 0x32e8b11d0>
2025-08-27 14:20:27,362 [DEBUG] numba.core.ssa: on stmt: t = $phi174.1
2025-08-27 14:20:27,362 [DEBUG] numba.core.ssa: on stmt: time_register.1 = getitem(value=t_out, index=t, fn=<built-in function getitem>)
2025-08-27 14:20:27,362 [DEBUG] numba.core.ssa: on stmt: $186load_global.5 = global(int: <class 'int'>)
2025-08-27 14:20:27,362 [DEBUG] numba.core.ssa: on stmt: n.1 = call $186load_global.5(time_register.1, func=$186load_global.5, args=[Var(time_register.1, interpn.py:24)], kws=(), vararg=None, varkwarg=None, target=None)
2025-08-27 14:20:27,362 [DEBUG] numba.core.ssa: on stmt: $binop_sub212.12 = time_register.1 - n.1
2025-08-27 14:20:27,362 [DEBUG] numba.core.ssa: on stmt: frac = scale * $binop_sub212.12
2025-08-27 14:20:27,362 [DEBUG] numba.core.ssa: replaced with: frac.1 = scale * $binop_sub212.12
2025-08-27 14:20:27,362 [DEBUG] numba.core.ssa: on stmt: index_frac = frac * num_table
2025-08-27 14:20:27,362 [DEBUG] numba.core.ssa: on stmt: $230load_global.17 = global(int: <class 'int'>)
2025-08-27 14:20:27,362 [DEBUG] numba.core.ssa: on stmt: offset = call $230load_global.17(index_frac, func=$230load_global.17, args=[Var(index_frac, interpn.py:14)], kws=(), vararg=None, varkwarg=None, target=None)
2025-08-27 14:20:27,362 [DEBUG] numba.core.ssa: on stmt: eta = index_frac - offset
2025-08-27 14:20:27,362 [DEBUG] numba.core.ssa: on stmt: $260load_global.24 = global(min: <built-in function min>)
2025-08-27 14:20:27,366 [DEBUG] numba.core.ssa: on stmt: $const272.27.3 = const(int, 1)
2025-08-27 14:20:27,366 [DEBUG] numba.core.ssa: on stmt: $binop_add274.28 = n.1 + $const272.27.3
2025-08-27 14:20:27,366 [DEBUG] numba.core.ssa: on stmt: $binop_sub280.31 = nwin - offset
2025-08-27 14:20:27,366 [DEBUG] numba.core.ssa: on stmt: $binop_floordiv286.33 = $binop_sub280.31 // index_step
2025-08-27 14:20:27,366 [DEBUG] numba.core.ssa: on stmt: i_max = call $260load_global.24($binop_add274.28, $binop_floordiv286.33, func=$260load_global.24, args=[Var($binop_add274.28, interpn.py:40), Var($binop_floordiv286.33, interpn.py:40)], kws=(), vararg=None, varkwarg=None, target=None)
2025-08-27 14:20:27,366 [DEBUG] numba.core.ssa: on stmt: $300load_global.35 = global(range: <class 'range'>)
2025-08-27 14:20:27,366 [DEBUG] numba.core.ssa: on stmt: $312call.38 = call $300load_global.35(i_max, func=$300load_global.35, args=[Var(i_max, interpn.py:40)], kws=(), vararg=None, varkwarg=None, target=None)
2025-08-27 14:20:27,366 [DEBUG] numba.core.ssa: on stmt: $320get_iter.39 = getiter(value=$312call.38)
2025-08-27 14:20:27,366 [DEBUG] numba.core.ssa: on stmt: $phi322.1 = $320get_iter.39
2025-08-27 14:20:27,366 [DEBUG] numba.core.ssa: on stmt: jump 322
2025-08-27 14:20:27,366 [DEBUG] numba.core.ssa: ==== SSA block rewrite pass on 322
2025-08-27 14:20:27,366 [DEBUG] numba.core.ssa: Running <numba.core.ssa._FreshVarHandler object at 0x32e8b11d0>
2025-08-27 14:20:27,366 [DEBUG] numba.core.ssa: on stmt: $322for_iter.2 = iternext(value=$phi322.1)
2025-08-27 14:20:27,366 [DEBUG] numba.core.ssa: on stmt: $322for_iter.3 = pair_first(value=$322for_iter.2)
2025-08-27 14:20:27,366 [DEBUG] numba.core.ssa: on stmt: $322for_iter.4 = pair_second(value=$322for_iter.2)
2025-08-27 14:20:27,366 [DEBUG] numba.core.ssa: on stmt: $phi326.2 = $322for_iter.3
2025-08-27 14:20:27,366 [DEBUG] numba.core.ssa: on stmt: branch $322for_iter.4, 326, 422
2025-08-27 14:20:27,366 [DEBUG] numba.core.ssa: ==== SSA block rewrite pass on 326
2025-08-27 14:20:27,366 [DEBUG] numba.core.ssa: Running <numba.core.ssa._FreshVarHandler object at 0x32e8b11d0>
2025-08-27 14:20:27,367 [DEBUG] numba.core.ssa: on stmt: i = $phi326.2
2025-08-27 14:20:27,367 [DEBUG] numba.core.ssa: on stmt: $binop_mul334.7 = i * index_step
2025-08-27 14:20:27,367 [DEBUG] numba.core.ssa: on stmt: $binop_add338.8 = offset + $binop_mul334.7
2025-08-27 14:20:27,367 [DEBUG] numba.core.ssa: on stmt: $342binary_subscr.9 = getitem(value=interp_win, index=$binop_add338.8, fn=<built-in function getitem>)
2025-08-27 14:20:27,367 [DEBUG] numba.core.ssa: on stmt: $binop_mul354.15 = i * index_step
2025-08-27 14:20:27,367 [DEBUG] numba.core.ssa: on stmt: $binop_add358.16 = offset + $binop_mul354.15
2025-08-27 14:20:27,367 [DEBUG] numba.core.ssa: on stmt: $362binary_subscr.17 = getitem(value=interp_delta, index=$binop_add358.16, fn=<built-in function getitem>)
2025-08-27 14:20:27,367 [DEBUG] numba.core.ssa: on stmt: $binop_mul366.18 = eta * $362binary_subscr.17
2025-08-27 14:20:27,367 [DEBUG] numba.core.ssa: on stmt: weight = $342binary_subscr.9 + $binop_mul366.18
2025-08-27 14:20:27,367 [DEBUG] numba.core.ssa: on stmt: $384binary_subscr.22 = getitem(value=y, index=t, fn=<built-in function getitem>)
2025-08-27 14:20:27,367 [DEBUG] numba.core.ssa: on stmt: $binop_sub394.27 = n.1 - i
2025-08-27 14:20:27,367 [DEBUG] numba.core.ssa: on stmt: $398binary_subscr.28 = getitem(value=x, index=$binop_sub394.27, fn=<built-in function getitem>)
2025-08-27 14:20:27,367 [DEBUG] numba.core.ssa: on stmt: $binop_mul402.29 = weight * $398binary_subscr.28
2025-08-27 14:20:27,367 [DEBUG] numba.core.ssa: on stmt: $binop_iadd406.30 = inplace_binop(fn=<built-in function iadd>, immutable_fn=<built-in function add>, lhs=$384binary_subscr.22, rhs=$binop_mul402.29, static_lhs=Undefined, static_rhs=Undefined)
2025-08-27 14:20:27,367 [DEBUG] numba.core.ssa: on stmt: y[t] = $binop_iadd406.30
2025-08-27 14:20:27,367 [DEBUG] numba.core.ssa: on stmt: jump 322
2025-08-27 14:20:27,367 [DEBUG] numba.core.ssa: ==== SSA block rewrite pass on 422
2025-08-27 14:20:27,367 [DEBUG] numba.core.ssa: Running <numba.core.ssa._FreshVarHandler object at 0x32e8b11d0>
2025-08-27 14:20:27,367 [DEBUG] numba.core.ssa: on stmt: $binop_sub428.5 = scale - frac
2025-08-27 14:20:27,367 [DEBUG] numba.core.ssa: on stmt: frac = $binop_sub428.5
2025-08-27 14:20:27,367 [DEBUG] numba.core.ssa: replaced with: frac.2 = $binop_sub428.5
2025-08-27 14:20:27,367 [DEBUG] numba.core.ssa: on stmt: index_frac = frac * num_table
2025-08-27 14:20:27,367 [DEBUG] numba.core.ssa: on stmt: $442load_global.9 = global(int: <class 'int'>)
2025-08-27 14:20:27,367 [DEBUG] numba.core.ssa: on stmt: offset = call $442load_global.9(index_frac, func=$442load_global.9, args=[Var(index_frac, interpn.py:14)], kws=(), vararg=None, varkwarg=None, target=None)
2025-08-27 14:20:27,367 [DEBUG] numba.core.ssa: on stmt: eta = index_frac - offset
2025-08-27 14:20:27,367 [DEBUG] numba.core.ssa: on stmt: $472load_global.16 = global(min: <built-in function min>)
2025-08-27 14:20:27,367 [DEBUG] numba.core.ssa: on stmt: $binop_sub486.20 = n_orig - n.1
2025-08-27 14:20:27,367 [DEBUG] numba.core.ssa: on stmt: $const490.21.3 = const(int, 1)
2025-08-27 14:20:27,367 [DEBUG] numba.core.ssa: on stmt: $binop_sub492.22 = $binop_sub486.20 - $const490.21.3
2025-08-27 14:20:27,367 [DEBUG] numba.core.ssa: on stmt: $binop_sub498.25 = nwin - offset
2025-08-27 14:20:27,367 [DEBUG] numba.core.ssa: on stmt: $binop_floordiv504.27 = $binop_sub498.25 // index_step
2025-08-27 14:20:27,367 [DEBUG] numba.core.ssa: on stmt: k_max = call $472load_global.16($binop_sub492.22, $binop_floordiv504.27, func=$472load_global.16, args=[Var($binop_sub492.22, interpn.py:60), Var($binop_floordiv504.27, interpn.py:60)], kws=(), vararg=None, varkwarg=None, target=None)
2025-08-27 14:20:27,367 [DEBUG] numba.core.ssa: on stmt: $518load_global.29 = global(range: <class 'range'>)
2025-08-27 14:20:27,367 [DEBUG] numba.core.ssa: on stmt: $530call.32 = call $518load_global.29(k_max, func=$518load_global.29, args=[Var(k_max, interpn.py:60)], kws=(), vararg=None, varkwarg=None, target=None)
2025-08-27 14:20:27,367 [DEBUG] numba.core.ssa: on stmt: $538get_iter.33 = getiter(value=$530call.32)
2025-08-27 14:20:27,367 [DEBUG] numba.core.ssa: on stmt: $phi540.1 = $538get_iter.33
2025-08-27 14:20:27,367 [DEBUG] numba.core.ssa: on stmt: jump 540
2025-08-27 14:20:27,367 [DEBUG] numba.core.ssa: ==== SSA block rewrite pass on 540
2025-08-27 14:20:27,367 [DEBUG] numba.core.ssa: Running <numba.core.ssa._FreshVarHandler object at 0x32e8b11d0>
2025-08-27 14:20:27,367 [DEBUG] numba.core.ssa: on stmt: $540for_iter.2 = iternext(value=$phi540.1)
2025-08-27 14:20:27,367 [DEBUG] numba.core.ssa: on stmt: $540for_iter.3 = pair_first(value=$540for_iter.2)
2025-08-27 14:20:27,367 [DEBUG] numba.core.ssa: on stmt: $540for_iter.4 = pair_second(value=$540for_iter.2)
2025-08-27 14:20:27,367 [DEBUG] numba.core.ssa: on stmt: $phi544.2 = $540for_iter.3
2025-08-27 14:20:27,367 [DEBUG] numba.core.ssa: on stmt: branch $540for_iter.4, 544, 646
2025-08-27 14:20:27,367 [DEBUG] numba.core.ssa: ==== SSA block rewrite pass on 544
2025-08-27 14:20:27,367 [DEBUG] numba.core.ssa: Running <numba.core.ssa._FreshVarHandler object at 0x32e8b11d0>
2025-08-27 14:20:27,367 [DEBUG] numba.core.ssa: on stmt: k = $phi544.2
2025-08-27 14:20:27,367 [DEBUG] numba.core.ssa: on stmt: $binop_mul552.7 = k * index_step
2025-08-27 14:20:27,367 [DEBUG] numba.core.ssa: on stmt: $binop_add556.8 = offset + $binop_mul552.7
2025-08-27 14:20:27,367 [DEBUG] numba.core.ssa: on stmt: $560binary_subscr.9 = getitem(value=interp_win, index=$binop_add556.8, fn=<built-in function getitem>)
2025-08-27 14:20:27,367 [DEBUG] numba.core.ssa: on stmt: $binop_mul572.15 = k * index_step
2025-08-27 14:20:27,367 [DEBUG] numba.core.ssa: on stmt: $binop_add576.16 = offset + $binop_mul572.15
2025-08-27 14:20:27,367 [DEBUG] numba.core.ssa: on stmt: $580binary_subscr.17 = getitem(value=interp_delta, index=$binop_add576.16, fn=<built-in function getitem>)
2025-08-27 14:20:27,367 [DEBUG] numba.core.ssa: on stmt: $binop_mul584.18 = eta * $580binary_subscr.17
2025-08-27 14:20:27,367 [DEBUG] numba.core.ssa: on stmt: weight = $560binary_subscr.9 + $binop_mul584.18
2025-08-27 14:20:27,367 [DEBUG] numba.core.ssa: on stmt: $602binary_subscr.22 = getitem(value=y, index=t, fn=<built-in function getitem>)
2025-08-27 14:20:27,367 [DEBUG] numba.core.ssa: on stmt: $binop_add612.27 = n.1 + k
2025-08-27 14:20:27,367 [DEBUG] numba.core.ssa: on stmt: $const616.28.3 = const(int, 1)
2025-08-27 14:20:27,367 [DEBUG] numba.core.ssa: on stmt: $binop_add618.29 = $binop_add612.27 + $const616.28.3
2025-08-27 14:20:27,367 [DEBUG] numba.core.ssa: on stmt: $622binary_subscr.30 = getitem(value=x, index=$binop_add618.29, fn=<built-in function getitem>)
2025-08-27 14:20:27,367 [DEBUG] numba.core.ssa: on stmt: $binop_mul626.31 = weight * $622binary_subscr.30
2025-08-27 14:20:27,367 [DEBUG] numba.core.ssa: on stmt: $binop_iadd630.32 = inplace_binop(fn=<built-in function iadd>, immutable_fn=<built-in function add>, lhs=$602binary_subscr.22, rhs=$binop_mul626.31, static_lhs=Undefined, static_rhs=Undefined)
2025-08-27 14:20:27,367 [DEBUG] numba.core.ssa: on stmt: y[t] = $binop_iadd630.32
2025-08-27 14:20:27,367 [DEBUG] numba.core.ssa: on stmt: jump 540
2025-08-27 14:20:27,367 [DEBUG] numba.core.ssa: ==== SSA block rewrite pass on 646
2025-08-27 14:20:27,367 [DEBUG] numba.core.ssa: Running <numba.core.ssa._FreshVarHandler object at 0x32e8b11d0>
2025-08-27 14:20:27,367 [DEBUG] numba.core.ssa: on stmt: jump 170
2025-08-27 14:20:27,367 [DEBUG] numba.core.ssa: ==== SSA block rewrite pass on 654
2025-08-27 14:20:27,367 [DEBUG] numba.core.ssa: Running <numba.core.ssa._FreshVarHandler object at 0x32e8b11d0>
2025-08-27 14:20:27,367 [DEBUG] numba.core.ssa: on stmt: $const658.2 = const(NoneType, None)
2025-08-27 14:20:27,367 [DEBUG] numba.core.ssa: on stmt: $658return_const.3 = cast(value=$const658.2)
2025-08-27 14:20:27,368 [DEBUG] numba.core.ssa: on stmt: return $658return_const.3
2025-08-27 14:20:27,368 [DEBUG] numba.core.ssa: Replaced assignments: defaultdict(<class 'list'>,
            {0: [<numba.core.ir.Assign object at 0x32ea05250>],
             174: [<numba.core.ir.Assign object at 0x32ea051f0>],
             422: [<numba.core.ir.Assign object at 0x32ea05b50>]})
2025-08-27 14:20:27,368 [DEBUG] numba.core.ssa: ==== SSA block rewrite pass on 0
2025-08-27 14:20:27,368 [DEBUG] numba.core.ssa: Running <numba.core.ssa._FixSSAVars object at 0x32e8b11d0>
2025-08-27 14:20:27,368 [DEBUG] numba.core.ssa: on stmt: x = arg(0, name=x)
2025-08-27 14:20:27,368 [DEBUG] numba.core.ssa: on stmt: t_out = arg(1, name=t_out)
2025-08-27 14:20:27,368 [DEBUG] numba.core.ssa: on stmt: interp_win = arg(2, name=interp_win)
2025-08-27 14:20:27,368 [DEBUG] numba.core.ssa: on stmt: interp_delta = arg(3, name=interp_delta)
2025-08-27 14:20:27,368 [DEBUG] numba.core.ssa: on stmt: num_table = arg(4, name=num_table)
2025-08-27 14:20:27,368 [DEBUG] numba.core.ssa: on stmt: scale = arg(5, name=scale)
2025-08-27 14:20:27,368 [DEBUG] numba.core.ssa: on stmt: y = arg(6, name=y)
2025-08-27 14:20:27,368 [DEBUG] numba.core.ssa: on stmt: $4load_global.0 = global(int: <class 'int'>)
2025-08-27 14:20:27,368 [DEBUG] numba.core.ssa: on stmt: $binop_mul16.4 = scale * num_table
2025-08-27 14:20:27,368 [DEBUG] numba.core.ssa: on stmt: index_step = call $4load_global.0($binop_mul16.4, func=$4load_global.0, args=[Var($binop_mul16.4, interpn.py:9)], kws=(), vararg=None, varkwarg=None, target=None)
2025-08-27 14:20:27,368 [DEBUG] numba.core.ssa: on stmt: time_register = const(float, 0.0)
2025-08-27 14:20:27,368 [DEBUG] numba.core.ssa: on stmt: n = const(int, 0)
2025-08-27 14:20:27,368 [DEBUG] numba.core.ssa: on stmt: frac = const(float, 0.0)
2025-08-27 14:20:27,368 [DEBUG] numba.core.ssa: on stmt: index_frac = const(float, 0.0)
2025-08-27 14:20:27,368 [DEBUG] numba.core.ssa: on stmt: offset = const(int, 0)
2025-08-27 14:20:27,368 [DEBUG] numba.core.ssa: on stmt: eta = const(float, 0.0)
2025-08-27 14:20:27,368 [DEBUG] numba.core.ssa: on stmt: weight = const(float, 0.0)
2025-08-27 14:20:27,368 [DEBUG] numba.core.ssa: on stmt: $60load_attr.14 = getattr(value=interp_win, attr=shape)
2025-08-27 14:20:27,368 [DEBUG] numba.core.ssa: on stmt: $const80.15.2 = const(int, 0)
2025-08-27 14:20:27,368 [DEBUG] numba.core.ssa: on stmt: nwin = static_getitem(value=$60load_attr.14, index=0, index_var=$const80.15.2, fn=<built-in function getitem>)
2025-08-27 14:20:27,368 [DEBUG] numba.core.ssa: on stmt: $90load_attr.18 = getattr(value=x, attr=shape)
2025-08-27 14:20:27,368 [DEBUG] numba.core.ssa: on stmt: $const110.19.2 = const(int, 0)
2025-08-27 14:20:27,368 [DEBUG] numba.core.ssa: on stmt: n_orig = static_getitem(value=$90load_attr.18, index=0, index_var=$const110.19.2, fn=<built-in function getitem>)
2025-08-27 14:20:27,368 [DEBUG] numba.core.ssa: on stmt: $120load_attr.22 = getattr(value=t_out, attr=shape)
2025-08-27 14:20:27,368 [DEBUG] numba.core.ssa: on stmt: $const140.23.2 = const(int, 0)
2025-08-27 14:20:27,368 [DEBUG] numba.core.ssa: on stmt: n_out = static_getitem(value=$120load_attr.22, index=0, index_var=$const140.23.2, fn=<built-in function getitem>)
2025-08-27 14:20:27,368 [DEBUG] numba.core.ssa: on stmt: $148load_global.25 = global(prange: <class 'numba.misc.special.prange'>)
2025-08-27 14:20:27,368 [DEBUG] numba.core.ssa: on stmt: $160call.28 = call $148load_global.25(n_out, func=$148load_global.25, args=[Var(n_out, interpn.py:21)], kws=(), vararg=None, varkwarg=None, target=None)
2025-08-27 14:20:27,368 [DEBUG] numba.core.ssa: on stmt: $168get_iter.29 = getiter(value=$160call.28)
2025-08-27 14:20:27,368 [DEBUG] numba.core.ssa: on stmt: $phi170.0 = $168get_iter.29
2025-08-27 14:20:27,368 [DEBUG] numba.core.ssa: on stmt: jump 170
2025-08-27 14:20:27,368 [DEBUG] numba.core.ssa: ==== SSA block rewrite pass on 170
2025-08-27 14:20:27,368 [DEBUG] numba.core.ssa: Running <numba.core.ssa._FixSSAVars object at 0x32e8b11d0>
2025-08-27 14:20:27,368 [DEBUG] numba.core.ssa: on stmt: $170for_iter.1 = iternext(value=$phi170.0)
2025-08-27 14:20:27,368 [DEBUG] numba.core.ssa: on stmt: $170for_iter.2 = pair_first(value=$170for_iter.1)
2025-08-27 14:20:27,368 [DEBUG] numba.core.ssa: on stmt: $170for_iter.3 = pair_second(value=$170for_iter.1)
2025-08-27 14:20:27,368 [DEBUG] numba.core.ssa: on stmt: $phi174.1 = $170for_iter.2
2025-08-27 14:20:27,368 [DEBUG] numba.core.ssa: on stmt: branch $170for_iter.3, 174, 654
2025-08-27 14:20:27,368 [DEBUG] numba.core.ssa: ==== SSA block rewrite pass on 174
2025-08-27 14:20:27,368 [DEBUG] numba.core.ssa: Running <numba.core.ssa._FixSSAVars object at 0x32e8b11d0>
2025-08-27 14:20:27,368 [DEBUG] numba.core.ssa: on stmt: t = $phi174.1
2025-08-27 14:20:27,368 [DEBUG] numba.core.ssa: on stmt: time_register.1 = getitem(value=t_out, index=t, fn=<built-in function getitem>)
2025-08-27 14:20:27,368 [DEBUG] numba.core.ssa: on stmt: $186load_global.5 = global(int: <class 'int'>)
2025-08-27 14:20:27,368 [DEBUG] numba.core.ssa: on stmt: n.1 = call $186load_global.5(time_register.1, func=$186load_global.5, args=[Var(time_register.1, interpn.py:24)], kws=(), vararg=None, varkwarg=None, target=None)
2025-08-27 14:20:27,368 [DEBUG] numba.core.ssa: on stmt: $binop_sub212.12 = time_register.1 - n.1
2025-08-27 14:20:27,368 [DEBUG] numba.core.ssa: on stmt: frac.1 = scale * $binop_sub212.12
2025-08-27 14:20:27,368 [DEBUG] numba.core.ssa: on stmt: index_frac = frac * num_table
2025-08-27 14:20:27,368 [DEBUG] numba.core.ssa: find_def var='frac' stmt=index_frac = frac * num_table
2025-08-27 14:20:27,368 [DEBUG] numba.core.ssa: replaced with: index_frac = frac.1 * num_table
2025-08-27 14:20:27,368 [DEBUG] numba.core.ssa: on stmt: $230load_global.17 = global(int: <class 'int'>)
2025-08-27 14:20:27,368 [DEBUG] numba.core.ssa: on stmt: offset = call $230load_global.17(index_frac, func=$230load_global.17, args=[Var(index_frac, interpn.py:14)], kws=(), vararg=None, varkwarg=None, target=None)
2025-08-27 14:20:27,368 [DEBUG] numba.core.ssa: on stmt: eta = index_frac - offset
2025-08-27 14:20:27,368 [DEBUG] numba.core.ssa: on stmt: $260load_global.24 = global(min: <built-in function min>)
2025-08-27 14:20:27,368 [DEBUG] numba.core.ssa: on stmt: $const272.27.3 = const(int, 1)
2025-08-27 14:20:27,368 [DEBUG] numba.core.ssa: on stmt: $binop_add274.28 = n.1 + $const272.27.3
2025-08-27 14:20:27,368 [DEBUG] numba.core.ssa: on stmt: $binop_sub280.31 = nwin - offset
2025-08-27 14:20:27,368 [DEBUG] numba.core.ssa: on stmt: $binop_floordiv286.33 = $binop_sub280.31 // index_step
2025-08-27 14:20:27,368 [DEBUG] numba.core.ssa: on stmt: i_max = call $260load_global.24($binop_add274.28, $binop_floordiv286.33, func=$260load_global.24, args=[Var($binop_add274.28, interpn.py:40), Var($binop_floordiv286.33, interpn.py:40)], kws=(), vararg=None, varkwarg=None, target=None)
2025-08-27 14:20:27,368 [DEBUG] numba.core.ssa: on stmt: $300load_global.35 = global(range: <class 'range'>)
2025-08-27 14:20:27,368 [DEBUG] numba.core.ssa: on stmt: $312call.38 = call $300load_global.35(i_max, func=$300load_global.35, args=[Var(i_max, interpn.py:40)], kws=(), vararg=None, varkwarg=None, target=None)
2025-08-27 14:20:27,368 [DEBUG] numba.core.ssa: on stmt: $320get_iter.39 = getiter(value=$312call.38)
2025-08-27 14:20:27,368 [DEBUG] numba.core.ssa: on stmt: $phi322.1 = $320get_iter.39
2025-08-27 14:20:27,369 [DEBUG] numba.core.ssa: on stmt: jump 322
2025-08-27 14:20:27,369 [DEBUG] numba.core.ssa: ==== SSA block rewrite pass on 322
2025-08-27 14:20:27,369 [DEBUG] numba.core.ssa: Running <numba.core.ssa._FixSSAVars object at 0x32e8b11d0>
2025-08-27 14:20:27,369 [DEBUG] numba.core.ssa: on stmt: $322for_iter.2 = iternext(value=$phi322.1)
2025-08-27 14:20:27,369 [DEBUG] numba.core.ssa: on stmt: $322for_iter.3 = pair_first(value=$322for_iter.2)
2025-08-27 14:20:27,369 [DEBUG] numba.core.ssa: on stmt: $322for_iter.4 = pair_second(value=$322for_iter.2)
2025-08-27 14:20:27,369 [DEBUG] numba.core.ssa: on stmt: $phi326.2 = $322for_iter.3
2025-08-27 14:20:27,369 [DEBUG] numba.core.ssa: on stmt: branch $322for_iter.4, 326, 422
2025-08-27 14:20:27,369 [DEBUG] numba.core.ssa: ==== SSA block rewrite pass on 326
2025-08-27 14:20:27,369 [DEBUG] numba.core.ssa: Running <numba.core.ssa._FixSSAVars object at 0x32e8b11d0>
2025-08-27 14:20:27,369 [DEBUG] numba.core.ssa: on stmt: i = $phi326.2
2025-08-27 14:20:27,369 [DEBUG] numba.core.ssa: on stmt: $binop_mul334.7 = i * index_step
2025-08-27 14:20:27,369 [DEBUG] numba.core.ssa: on stmt: $binop_add338.8 = offset + $binop_mul334.7
2025-08-27 14:20:27,369 [DEBUG] numba.core.ssa: on stmt: $342binary_subscr.9 = getitem(value=interp_win, index=$binop_add338.8, fn=<built-in function getitem>)
2025-08-27 14:20:27,369 [DEBUG] numba.core.ssa: on stmt: $binop_mul354.15 = i * index_step
2025-08-27 14:20:27,369 [DEBUG] numba.core.ssa: on stmt: $binop_add358.16 = offset + $binop_mul354.15
2025-08-27 14:20:27,369 [DEBUG] numba.core.ssa: on stmt: $362binary_subscr.17 = getitem(value=interp_delta, index=$binop_add358.16, fn=<built-in function getitem>)
2025-08-27 14:20:27,369 [DEBUG] numba.core.ssa: on stmt: $binop_mul366.18 = eta * $362binary_subscr.17
2025-08-27 14:20:27,369 [DEBUG] numba.core.ssa: on stmt: weight = $342binary_subscr.9 + $binop_mul366.18
2025-08-27 14:20:27,369 [DEBUG] numba.core.ssa: on stmt: $384binary_subscr.22 = getitem(value=y, index=t, fn=<built-in function getitem>)
2025-08-27 14:20:27,369 [DEBUG] numba.core.ssa: on stmt: $binop_sub394.27 = n.1 - i
2025-08-27 14:20:27,369 [DEBUG] numba.core.ssa: on stmt: $398binary_subscr.28 = getitem(value=x, index=$binop_sub394.27, fn=<built-in function getitem>)
2025-08-27 14:20:27,369 [DEBUG] numba.core.ssa: on stmt: $binop_mul402.29 = weight * $398binary_subscr.28
2025-08-27 14:20:27,369 [DEBUG] numba.core.ssa: on stmt: $binop_iadd406.30 = inplace_binop(fn=<built-in function iadd>, immutable_fn=<built-in function add>, lhs=$384binary_subscr.22, rhs=$binop_mul402.29, static_lhs=Undefined, static_rhs=Undefined)
2025-08-27 14:20:27,369 [DEBUG] numba.core.ssa: on stmt: y[t] = $binop_iadd406.30
2025-08-27 14:20:27,369 [DEBUG] numba.core.ssa: on stmt: jump 322
2025-08-27 14:20:27,369 [DEBUG] numba.core.ssa: ==== SSA block rewrite pass on 422
2025-08-27 14:20:27,369 [DEBUG] numba.core.ssa: Running <numba.core.ssa._FixSSAVars object at 0x32e8b11d0>
2025-08-27 14:20:27,369 [DEBUG] numba.core.ssa: on stmt: $binop_sub428.5 = scale - frac
2025-08-27 14:20:27,369 [DEBUG] numba.core.ssa: find_def var='frac' stmt=$binop_sub428.5 = scale - frac
2025-08-27 14:20:27,369 [DEBUG] numba.core.ssa: find_def_from_top label 422
2025-08-27 14:20:27,369 [DEBUG] numba.core.ssa: idom 322 from label 422
2025-08-27 14:20:27,369 [DEBUG] numba.core.ssa: find_def_from_bottom label 322
2025-08-27 14:20:27,369 [DEBUG] numba.core.ssa: find_def_from_top label 322
2025-08-27 14:20:27,369 [DEBUG] numba.core.ssa: idom 174 from label 322
2025-08-27 14:20:27,369 [DEBUG] numba.core.ssa: find_def_from_bottom label 174
2025-08-27 14:20:27,369 [DEBUG] numba.core.ssa: replaced with: $binop_sub428.5 = scale - frac.1
2025-08-27 14:20:27,369 [DEBUG] numba.core.ssa: on stmt: frac.2 = $binop_sub428.5
2025-08-27 14:20:27,369 [DEBUG] numba.core.ssa: on stmt: index_frac = frac * num_table
2025-08-27 14:20:27,369 [DEBUG] numba.core.ssa: find_def var='frac' stmt=index_frac = frac * num_table
2025-08-27 14:20:27,369 [DEBUG] numba.core.ssa: replaced with: index_frac = frac.2 * num_table
2025-08-27 14:20:27,369 [DEBUG] numba.core.ssa: on stmt: $442load_global.9 = global(int: <class 'int'>)
2025-08-27 14:20:27,369 [DEBUG] numba.core.ssa: on stmt: offset = call $442load_global.9(index_frac, func=$442load_global.9, args=[Var(index_frac, interpn.py:14)], kws=(), vararg=None, varkwarg=None, target=None)
2025-08-27 14:20:27,369 [DEBUG] numba.core.ssa: on stmt: eta = index_frac - offset
2025-08-27 14:20:27,369 [DEBUG] numba.core.ssa: on stmt: $472load_global.16 = global(min: <built-in function min>)
2025-08-27 14:20:27,369 [DEBUG] numba.core.ssa: on stmt: $binop_sub486.20 = n_orig - n.1
2025-08-27 14:20:27,369 [DEBUG] numba.core.ssa: on stmt: $const490.21.3 = const(int, 1)
2025-08-27 14:20:27,369 [DEBUG] numba.core.ssa: on stmt: $binop_sub492.22 = $binop_sub486.20 - $const490.21.3
2025-08-27 14:20:27,369 [DEBUG] numba.core.ssa: on stmt: $binop_sub498.25 = nwin - offset
2025-08-27 14:20:27,369 [DEBUG] numba.core.ssa: on stmt: $binop_floordiv504.27 = $binop_sub498.25 // index_step
2025-08-27 14:20:27,369 [DEBUG] numba.core.ssa: on stmt: k_max = call $472load_global.16($binop_sub492.22, $binop_floordiv504.27, func=$472load_global.16, args=[Var($binop_sub492.22, interpn.py:60), Var($binop_floordiv504.27, interpn.py:60)], kws=(), vararg=None, varkwarg=None, target=None)
2025-08-27 14:20:27,369 [DEBUG] numba.core.ssa: on stmt: $518load_global.29 = global(range: <class 'range'>)
2025-08-27 14:20:27,369 [DEBUG] numba.core.ssa: on stmt: $530call.32 = call $518load_global.29(k_max, func=$518load_global.29, args=[Var(k_max, interpn.py:60)], kws=(), vararg=None, varkwarg=None, target=None)
2025-08-27 14:20:27,369 [DEBUG] numba.core.ssa: on stmt: $538get_iter.33 = getiter(value=$530call.32)
2025-08-27 14:20:27,369 [DEBUG] numba.core.ssa: on stmt: $phi540.1 = $538get_iter.33
2025-08-27 14:20:27,369 [DEBUG] numba.core.ssa: on stmt: jump 540
2025-08-27 14:20:27,369 [DEBUG] numba.core.ssa: ==== SSA block rewrite pass on 540
2025-08-27 14:20:27,369 [DEBUG] numba.core.ssa: Running <numba.core.ssa._FixSSAVars object at 0x32e8b11d0>
2025-08-27 14:20:27,369 [DEBUG] numba.core.ssa: on stmt: $540for_iter.2 = iternext(value=$phi540.1)
2025-08-27 14:20:27,369 [DEBUG] numba.core.ssa: on stmt: $540for_iter.3 = pair_first(value=$540for_iter.2)
2025-08-27 14:20:27,369 [DEBUG] numba.core.ssa: on stmt: $540for_iter.4 = pair_second(value=$540for_iter.2)
2025-08-27 14:20:27,369 [DEBUG] numba.core.ssa: on stmt: $phi544.2 = $540for_iter.3
2025-08-27 14:20:27,369 [DEBUG] numba.core.ssa: on stmt: branch $540for_iter.4, 544, 646
2025-08-27 14:20:27,369 [DEBUG] numba.core.ssa: ==== SSA block rewrite pass on 544
2025-08-27 14:20:27,369 [DEBUG] numba.core.ssa: Running <numba.core.ssa._FixSSAVars object at 0x32e8b11d0>
2025-08-27 14:20:27,369 [DEBUG] numba.core.ssa: on stmt: k = $phi544.2
2025-08-27 14:20:27,369 [DEBUG] numba.core.ssa: on stmt: $binop_mul552.7 = k * index_step
2025-08-27 14:20:27,369 [DEBUG] numba.core.ssa: on stmt: $binop_add556.8 = offset + $binop_mul552.7
2025-08-27 14:20:27,369 [DEBUG] numba.core.ssa: on stmt: $560binary_subscr.9 = getitem(value=interp_win, index=$binop_add556.8, fn=<built-in function getitem>)
2025-08-27 14:20:27,369 [DEBUG] numba.core.ssa: on stmt: $binop_mul572.15 = k * index_step
2025-08-27 14:20:27,369 [DEBUG] numba.core.ssa: on stmt: $binop_add576.16 = offset + $binop_mul572.15
2025-08-27 14:20:27,370 [DEBUG] numba.core.ssa: on stmt: $580binary_subscr.17 = getitem(value=interp_delta, index=$binop_add576.16, fn=<built-in function getitem>)
2025-08-27 14:20:27,370 [DEBUG] numba.core.ssa: on stmt: $binop_mul584.18 = eta * $580binary_subscr.17
2025-08-27 14:20:27,370 [DEBUG] numba.core.ssa: on stmt: weight = $560binary_subscr.9 + $binop_mul584.18
2025-08-27 14:20:27,370 [DEBUG] numba.core.ssa: on stmt: $602binary_subscr.22 = getitem(value=y, index=t, fn=<built-in function getitem>)
2025-08-27 14:20:27,370 [DEBUG] numba.core.ssa: on stmt: $binop_add612.27 = n.1 + k
2025-08-27 14:20:27,370 [DEBUG] numba.core.ssa: on stmt: $const616.28.3 = const(int, 1)
2025-08-27 14:20:27,370 [DEBUG] numba.core.ssa: on stmt: $binop_add618.29 = $binop_add612.27 + $const616.28.3
2025-08-27 14:20:27,370 [DEBUG] numba.core.ssa: on stmt: $622binary_subscr.30 = getitem(value=x, index=$binop_add618.29, fn=<built-in function getitem>)
2025-08-27 14:20:27,370 [DEBUG] numba.core.ssa: on stmt: $binop_mul626.31 = weight * $622binary_subscr.30
2025-08-27 14:20:27,370 [DEBUG] numba.core.ssa: on stmt: $binop_iadd630.32 = inplace_binop(fn=<built-in function iadd>, immutable_fn=<built-in function add>, lhs=$602binary_subscr.22, rhs=$binop_mul626.31, static_lhs=Undefined, static_rhs=Undefined)
2025-08-27 14:20:27,370 [DEBUG] numba.core.ssa: on stmt: y[t] = $binop_iadd630.32
2025-08-27 14:20:27,370 [DEBUG] numba.core.ssa: on stmt: jump 540
2025-08-27 14:20:27,370 [DEBUG] numba.core.ssa: ==== SSA block rewrite pass on 646
2025-08-27 14:20:27,370 [DEBUG] numba.core.ssa: Running <numba.core.ssa._FixSSAVars object at 0x32e8b11d0>
2025-08-27 14:20:27,370 [DEBUG] numba.core.ssa: on stmt: jump 170
2025-08-27 14:20:27,370 [DEBUG] numba.core.ssa: ==== SSA block rewrite pass on 654
2025-08-27 14:20:27,370 [DEBUG] numba.core.ssa: Running <numba.core.ssa._FixSSAVars object at 0x32e8b11d0>
2025-08-27 14:20:27,370 [DEBUG] numba.core.ssa: on stmt: $const658.2 = const(NoneType, None)
2025-08-27 14:20:27,370 [DEBUG] numba.core.ssa: on stmt: $658return_const.3 = cast(value=$const658.2)
2025-08-27 14:20:27,370 [DEBUG] numba.core.ssa: on stmt: return $658return_const.3
2025-08-27 14:20:27,370 [DEBUG] numba.core.ssa: Fix SSA violator on var index_frac
2025-08-27 14:20:27,370 [DEBUG] numba.core.ssa: ==== SSA block rewrite pass on 0
2025-08-27 14:20:27,370 [DEBUG] numba.core.ssa: Running <numba.core.ssa._FreshVarHandler object at 0x32e9a8b00>
2025-08-27 14:20:27,370 [DEBUG] numba.core.ssa: on stmt: x = arg(0, name=x)
2025-08-27 14:20:27,370 [DEBUG] numba.core.ssa: on stmt: t_out = arg(1, name=t_out)
2025-08-27 14:20:27,370 [DEBUG] numba.core.ssa: on stmt: interp_win = arg(2, name=interp_win)
2025-08-27 14:20:27,370 [DEBUG] numba.core.ssa: on stmt: interp_delta = arg(3, name=interp_delta)
2025-08-27 14:20:27,370 [DEBUG] numba.core.ssa: on stmt: num_table = arg(4, name=num_table)
2025-08-27 14:20:27,370 [DEBUG] numba.core.ssa: on stmt: scale = arg(5, name=scale)
2025-08-27 14:20:27,370 [DEBUG] numba.core.ssa: on stmt: y = arg(6, name=y)
2025-08-27 14:20:27,370 [DEBUG] numba.core.ssa: on stmt: $4load_global.0 = global(int: <class 'int'>)
2025-08-27 14:20:27,370 [DEBUG] numba.core.ssa: on stmt: $binop_mul16.4 = scale * num_table
2025-08-27 14:20:27,370 [DEBUG] numba.core.ssa: on stmt: index_step = call $4load_global.0($binop_mul16.4, func=$4load_global.0, args=[Var($binop_mul16.4, interpn.py:9)], kws=(), vararg=None, varkwarg=None, target=None)
2025-08-27 14:20:27,370 [DEBUG] numba.core.ssa: on stmt: time_register = const(float, 0.0)
2025-08-27 14:20:27,370 [DEBUG] numba.core.ssa: on stmt: n = const(int, 0)
2025-08-27 14:20:27,370 [DEBUG] numba.core.ssa: on stmt: frac = const(float, 0.0)
2025-08-27 14:20:27,370 [DEBUG] numba.core.ssa: on stmt: index_frac = const(float, 0.0)
2025-08-27 14:20:27,370 [DEBUG] numba.core.ssa: first assign: index_frac
2025-08-27 14:20:27,370 [DEBUG] numba.core.ssa: replaced with: index_frac = const(float, 0.0)
2025-08-27 14:20:27,370 [DEBUG] numba.core.ssa: on stmt: offset = const(int, 0)
2025-08-27 14:20:27,370 [DEBUG] numba.core.ssa: on stmt: eta = const(float, 0.0)
2025-08-27 14:20:27,370 [DEBUG] numba.core.ssa: on stmt: weight = const(float, 0.0)
2025-08-27 14:20:27,370 [DEBUG] numba.core.ssa: on stmt: $60load_attr.14 = getattr(value=interp_win, attr=shape)
2025-08-27 14:20:27,370 [DEBUG] numba.core.ssa: on stmt: $const80.15.2 = const(int, 0)
2025-08-27 14:20:27,370 [DEBUG] numba.core.ssa: on stmt: nwin = static_getitem(value=$60load_attr.14, index=0, index_var=$const80.15.2, fn=<built-in function getitem>)
2025-08-27 14:20:27,370 [DEBUG] numba.core.ssa: on stmt: $90load_attr.18 = getattr(value=x, attr=shape)
2025-08-27 14:20:27,370 [DEBUG] numba.core.ssa: on stmt: $const110.19.2 = const(int, 0)
2025-08-27 14:20:27,370 [DEBUG] numba.core.ssa: on stmt: n_orig = static_getitem(value=$90load_attr.18, index=0, index_var=$const110.19.2, fn=<built-in function getitem>)
2025-08-27 14:20:27,370 [DEBUG] numba.core.ssa: on stmt: $120load_attr.22 = getattr(value=t_out, attr=shape)
2025-08-27 14:20:27,370 [DEBUG] numba.core.ssa: on stmt: $const140.23.2 = const(int, 0)
2025-08-27 14:20:27,370 [DEBUG] numba.core.ssa: on stmt: n_out = static_getitem(value=$120load_attr.22, index=0, index_var=$const140.23.2, fn=<built-in function getitem>)
2025-08-27 14:20:27,370 [DEBUG] numba.core.ssa: on stmt: $148load_global.25 = global(prange: <class 'numba.misc.special.prange'>)
2025-08-27 14:20:27,370 [DEBUG] numba.core.ssa: on stmt: $160call.28 = call $148load_global.25(n_out, func=$148load_global.25, args=[Var(n_out, interpn.py:21)], kws=(), vararg=None, varkwarg=None, target=None)
2025-08-27 14:20:27,370 [DEBUG] numba.core.ssa: on stmt: $168get_iter.29 = getiter(value=$160call.28)
2025-08-27 14:20:27,370 [DEBUG] numba.core.ssa: on stmt: $phi170.0 = $168get_iter.29
2025-08-27 14:20:27,370 [DEBUG] numba.core.ssa: on stmt: jump 170
2025-08-27 14:20:27,370 [DEBUG] numba.core.ssa: ==== SSA block rewrite pass on 170
2025-08-27 14:20:27,370 [DEBUG] numba.core.ssa: Running <numba.core.ssa._FreshVarHandler object at 0x32e9a8b00>
2025-08-27 14:20:27,370 [DEBUG] numba.core.ssa: on stmt: $170for_iter.1 = iternext(value=$phi170.0)
2025-08-27 14:20:27,370 [DEBUG] numba.core.ssa: on stmt: $170for_iter.2 = pair_first(value=$170for_iter.1)
2025-08-27 14:20:27,370 [DEBUG] numba.core.ssa: on stmt: $170for_iter.3 = pair_second(value=$170for_iter.1)
2025-08-27 14:20:27,370 [DEBUG] numba.core.ssa: on stmt: $phi174.1 = $170for_iter.2
2025-08-27 14:20:27,370 [DEBUG] numba.core.ssa: on stmt: branch $170for_iter.3, 174, 654
2025-08-27 14:20:27,370 [DEBUG] numba.core.ssa: ==== SSA block rewrite pass on 174
2025-08-27 14:20:27,370 [DEBUG] numba.core.ssa: Running <numba.core.ssa._FreshVarHandler object at 0x32e9a8b00>
2025-08-27 14:20:27,370 [DEBUG] numba.core.ssa: on stmt: t = $phi174.1
2025-08-27 14:20:27,370 [DEBUG] numba.core.ssa: on stmt: time_register.1 = getitem(value=t_out, index=t, fn=<built-in function getitem>)
2025-08-27 14:20:27,370 [DEBUG] numba.core.ssa: on stmt: $186load_global.5 = global(int: <class 'int'>)
2025-08-27 14:20:27,370 [DEBUG] numba.core.ssa: on stmt: n.1 = call $186load_global.5(time_register.1, func=$186load_global.5, args=[Var(time_register.1, interpn.py:24)], kws=(), vararg=None, varkwarg=None, target=None)
2025-08-27 14:20:27,370 [DEBUG] numba.core.ssa: on stmt: $binop_sub212.12 = time_register.1 - n.1
2025-08-27 14:20:27,370 [DEBUG] numba.core.ssa: on stmt: frac.1 = scale * $binop_sub212.12
2025-08-27 14:20:27,371 [DEBUG] numba.core.ssa: on stmt: index_frac = frac.1 * num_table
2025-08-27 14:20:27,371 [DEBUG] numba.core.ssa: replaced with: index_frac.1 = frac.1 * num_table
2025-08-27 14:20:27,371 [DEBUG] numba.core.ssa: on stmt: $230load_global.17 = global(int: <class 'int'>)
2025-08-27 14:20:27,371 [DEBUG] numba.core.ssa: on stmt: offset = call $230load_global.17(index_frac, func=$230load_global.17, args=[Var(index_frac, interpn.py:14)], kws=(), vararg=None, varkwarg=None, target=None)
2025-08-27 14:20:27,371 [DEBUG] numba.core.ssa: on stmt: eta = index_frac - offset
2025-08-27 14:20:27,371 [DEBUG] numba.core.ssa: on stmt: $260load_global.24 = global(min: <built-in function min>)
2025-08-27 14:20:27,371 [DEBUG] numba.core.ssa: on stmt: $const272.27.3 = const(int, 1)
2025-08-27 14:20:27,371 [DEBUG] numba.core.ssa: on stmt: $binop_add274.28 = n.1 + $const272.27.3
2025-08-27 14:20:27,371 [DEBUG] numba.core.ssa: on stmt: $binop_sub280.31 = nwin - offset
2025-08-27 14:20:27,371 [DEBUG] numba.core.ssa: on stmt: $binop_floordiv286.33 = $binop_sub280.31 // index_step
2025-08-27 14:20:27,371 [DEBUG] numba.core.ssa: on stmt: i_max = call $260load_global.24($binop_add274.28, $binop_floordiv286.33, func=$260load_global.24, args=[Var($binop_add274.28, interpn.py:40), Var($binop_floordiv286.33, interpn.py:40)], kws=(), vararg=None, varkwarg=None, target=None)
2025-08-27 14:20:27,371 [DEBUG] numba.core.ssa: on stmt: $300load_global.35 = global(range: <class 'range'>)
2025-08-27 14:20:27,371 [DEBUG] numba.core.ssa: on stmt: $312call.38 = call $300load_global.35(i_max, func=$300load_global.35, args=[Var(i_max, interpn.py:40)], kws=(), vararg=None, varkwarg=None, target=None)
2025-08-27 14:20:27,371 [DEBUG] numba.core.ssa: on stmt: $320get_iter.39 = getiter(value=$312call.38)
2025-08-27 14:20:27,371 [DEBUG] numba.core.ssa: on stmt: $phi322.1 = $320get_iter.39
2025-08-27 14:20:27,371 [DEBUG] numba.core.ssa: on stmt: jump 322
2025-08-27 14:20:27,371 [DEBUG] numba.core.ssa: ==== SSA block rewrite pass on 322
2025-08-27 14:20:27,371 [DEBUG] numba.core.ssa: Running <numba.core.ssa._FreshVarHandler object at 0x32e9a8b00>
2025-08-27 14:20:27,371 [DEBUG] numba.core.ssa: on stmt: $322for_iter.2 = iternext(value=$phi322.1)
2025-08-27 14:20:27,371 [DEBUG] numba.core.ssa: on stmt: $322for_iter.3 = pair_first(value=$322for_iter.2)
2025-08-27 14:20:27,371 [DEBUG] numba.core.ssa: on stmt: $322for_iter.4 = pair_second(value=$322for_iter.2)
2025-08-27 14:20:27,371 [DEBUG] numba.core.ssa: on stmt: $phi326.2 = $322for_iter.3
2025-08-27 14:20:27,371 [DEBUG] numba.core.ssa: on stmt: branch $322for_iter.4, 326, 422
2025-08-27 14:20:27,371 [DEBUG] numba.core.ssa: ==== SSA block rewrite pass on 326
2025-08-27 14:20:27,371 [DEBUG] numba.core.ssa: Running <numba.core.ssa._FreshVarHandler object at 0x32e9a8b00>
2025-08-27 14:20:27,371 [DEBUG] numba.core.ssa: on stmt: i = $phi326.2
2025-08-27 14:20:27,371 [DEBUG] numba.core.ssa: on stmt: $binop_mul334.7 = i * index_step
2025-08-27 14:20:27,371 [DEBUG] numba.core.ssa: on stmt: $binop_add338.8 = offset + $binop_mul334.7
2025-08-27 14:20:27,371 [DEBUG] numba.core.ssa: on stmt: $342binary_subscr.9 = getitem(value=interp_win, index=$binop_add338.8, fn=<built-in function getitem>)
2025-08-27 14:20:27,371 [DEBUG] numba.core.ssa: on stmt: $binop_mul354.15 = i * index_step
2025-08-27 14:20:27,371 [DEBUG] numba.core.ssa: on stmt: $binop_add358.16 = offset + $binop_mul354.15
2025-08-27 14:20:27,371 [DEBUG] numba.core.ssa: on stmt: $362binary_subscr.17 = getitem(value=interp_delta, index=$binop_add358.16, fn=<built-in function getitem>)
2025-08-27 14:20:27,371 [DEBUG] numba.core.ssa: on stmt: $binop_mul366.18 = eta * $362binary_subscr.17
2025-08-27 14:20:27,371 [DEBUG] numba.core.ssa: on stmt: weight = $342binary_subscr.9 + $binop_mul366.18
2025-08-27 14:20:27,371 [DEBUG] numba.core.ssa: on stmt: $384binary_subscr.22 = getitem(value=y, index=t, fn=<built-in function getitem>)
2025-08-27 14:20:27,371 [DEBUG] numba.core.ssa: on stmt: $binop_sub394.27 = n.1 - i
2025-08-27 14:20:27,371 [DEBUG] numba.core.ssa: on stmt: $398binary_subscr.28 = getitem(value=x, index=$binop_sub394.27, fn=<built-in function getitem>)
2025-08-27 14:20:27,371 [DEBUG] numba.core.ssa: on stmt: $binop_mul402.29 = weight * $398binary_subscr.28
2025-08-27 14:20:27,371 [DEBUG] numba.core.ssa: on stmt: $binop_iadd406.30 = inplace_binop(fn=<built-in function iadd>, immutable_fn=<built-in function add>, lhs=$384binary_subscr.22, rhs=$binop_mul402.29, static_lhs=Undefined, static_rhs=Undefined)
2025-08-27 14:20:27,371 [DEBUG] numba.core.ssa: on stmt: y[t] = $binop_iadd406.30
2025-08-27 14:20:27,371 [DEBUG] numba.core.ssa: on stmt: jump 322
2025-08-27 14:20:27,371 [DEBUG] numba.core.ssa: ==== SSA block rewrite pass on 422
2025-08-27 14:20:27,371 [DEBUG] numba.core.ssa: Running <numba.core.ssa._FreshVarHandler object at 0x32e9a8b00>
2025-08-27 14:20:27,371 [DEBUG] numba.core.ssa: on stmt: $binop_sub428.5 = scale - frac.1
2025-08-27 14:20:27,371 [DEBUG] numba.core.ssa: on stmt: frac.2 = $binop_sub428.5
2025-08-27 14:20:27,371 [DEBUG] numba.core.ssa: on stmt: index_frac = frac.2 * num_table
2025-08-27 14:20:27,371 [DEBUG] numba.core.ssa: replaced with: index_frac.2 = frac.2 * num_table
2025-08-27 14:20:27,371 [DEBUG] numba.core.ssa: on stmt: $442load_global.9 = global(int: <class 'int'>)
2025-08-27 14:20:27,371 [DEBUG] numba.core.ssa: on stmt: offset = call $442load_global.9(index_frac, func=$442load_global.9, args=[Var(index_frac, interpn.py:14)], kws=(), vararg=None, varkwarg=None, target=None)
2025-08-27 14:20:27,371 [DEBUG] numba.core.ssa: on stmt: eta = index_frac - offset
2025-08-27 14:20:27,371 [DEBUG] numba.core.ssa: on stmt: $472load_global.16 = global(min: <built-in function min>)
2025-08-27 14:20:27,371 [DEBUG] numba.core.ssa: on stmt: $binop_sub486.20 = n_orig - n.1
2025-08-27 14:20:27,371 [DEBUG] numba.core.ssa: on stmt: $const490.21.3 = const(int, 1)
2025-08-27 14:20:27,371 [DEBUG] numba.core.ssa: on stmt: $binop_sub492.22 = $binop_sub486.20 - $const490.21.3
2025-08-27 14:20:27,371 [DEBUG] numba.core.ssa: on stmt: $binop_sub498.25 = nwin - offset
2025-08-27 14:20:27,371 [DEBUG] numba.core.ssa: on stmt: $binop_floordiv504.27 = $binop_sub498.25 // index_step
2025-08-27 14:20:27,371 [DEBUG] numba.core.ssa: on stmt: k_max = call $472load_global.16($binop_sub492.22, $binop_floordiv504.27, func=$472load_global.16, args=[Var($binop_sub492.22, interpn.py:60), Var($binop_floordiv504.27, interpn.py:60)], kws=(), vararg=None, varkwarg=None, target=None)
2025-08-27 14:20:27,371 [DEBUG] numba.core.ssa: on stmt: $518load_global.29 = global(range: <class 'range'>)
2025-08-27 14:20:27,371 [DEBUG] numba.core.ssa: on stmt: $530call.32 = call $518load_global.29(k_max, func=$518load_global.29, args=[Var(k_max, interpn.py:60)], kws=(), vararg=None, varkwarg=None, target=None)
2025-08-27 14:20:27,371 [DEBUG] numba.core.ssa: on stmt: $538get_iter.33 = getiter(value=$530call.32)
2025-08-27 14:20:27,371 [DEBUG] numba.core.ssa: on stmt: $phi540.1 = $538get_iter.33
2025-08-27 14:20:27,371 [DEBUG] numba.core.ssa: on stmt: jump 540
2025-08-27 14:20:27,371 [DEBUG] numba.core.ssa: ==== SSA block rewrite pass on 540
2025-08-27 14:20:27,371 [DEBUG] numba.core.ssa: Running <numba.core.ssa._FreshVarHandler object at 0x32e9a8b00>
2025-08-27 14:20:27,371 [DEBUG] numba.core.ssa: on stmt: $540for_iter.2 = iternext(value=$phi540.1)
2025-08-27 14:20:27,371 [DEBUG] numba.core.ssa: on stmt: $540for_iter.3 = pair_first(value=$540for_iter.2)
2025-08-27 14:20:27,371 [DEBUG] numba.core.ssa: on stmt: $540for_iter.4 = pair_second(value=$540for_iter.2)
2025-08-27 14:20:27,371 [DEBUG] numba.core.ssa: on stmt: $phi544.2 = $540for_iter.3
2025-08-27 14:20:27,371 [DEBUG] numba.core.ssa: on stmt: branch $540for_iter.4, 544, 646
2025-08-27 14:20:27,371 [DEBUG] numba.core.ssa: ==== SSA block rewrite pass on 544
2025-08-27 14:20:27,371 [DEBUG] numba.core.ssa: Running <numba.core.ssa._FreshVarHandler object at 0x32e9a8b00>
2025-08-27 14:20:27,371 [DEBUG] numba.core.ssa: on stmt: k = $phi544.2
2025-08-27 14:20:27,372 [DEBUG] numba.core.ssa: on stmt: $binop_mul552.7 = k * index_step
2025-08-27 14:20:27,372 [DEBUG] numba.core.ssa: on stmt: $binop_add556.8 = offset + $binop_mul552.7
2025-08-27 14:20:27,372 [DEBUG] numba.core.ssa: on stmt: $560binary_subscr.9 = getitem(value=interp_win, index=$binop_add556.8, fn=<built-in function getitem>)
2025-08-27 14:20:27,372 [DEBUG] numba.core.ssa: on stmt: $binop_mul572.15 = k * index_step
2025-08-27 14:20:27,372 [DEBUG] numba.core.ssa: on stmt: $binop_add576.16 = offset + $binop_mul572.15
2025-08-27 14:20:27,372 [DEBUG] numba.core.ssa: on stmt: $580binary_subscr.17 = getitem(value=interp_delta, index=$binop_add576.16, fn=<built-in function getitem>)
2025-08-27 14:20:27,372 [DEBUG] numba.core.ssa: on stmt: $binop_mul584.18 = eta * $580binary_subscr.17
2025-08-27 14:20:27,372 [DEBUG] numba.core.ssa: on stmt: weight = $560binary_subscr.9 + $binop_mul584.18
2025-08-27 14:20:27,372 [DEBUG] numba.core.ssa: on stmt: $602binary_subscr.22 = getitem(value=y, index=t, fn=<built-in function getitem>)
2025-08-27 14:20:27,372 [DEBUG] numba.core.ssa: on stmt: $binop_add612.27 = n.1 + k
2025-08-27 14:20:27,372 [DEBUG] numba.core.ssa: on stmt: $const616.28.3 = const(int, 1)
2025-08-27 14:20:27,372 [DEBUG] numba.core.ssa: on stmt: $binop_add618.29 = $binop_add612.27 + $const616.28.3
2025-08-27 14:20:27,372 [DEBUG] numba.core.ssa: on stmt: $622binary_subscr.30 = getitem(value=x, index=$binop_add618.29, fn=<built-in function getitem>)
2025-08-27 14:20:27,372 [DEBUG] numba.core.ssa: on stmt: $binop_mul626.31 = weight * $622binary_subscr.30
2025-08-27 14:20:27,372 [DEBUG] numba.core.ssa: on stmt: $binop_iadd630.32 = inplace_binop(fn=<built-in function iadd>, immutable_fn=<built-in function add>, lhs=$602binary_subscr.22, rhs=$binop_mul626.31, static_lhs=Undefined, static_rhs=Undefined)
2025-08-27 14:20:27,372 [DEBUG] numba.core.ssa: on stmt: y[t] = $binop_iadd630.32
2025-08-27 14:20:27,372 [DEBUG] numba.core.ssa: on stmt: jump 540
2025-08-27 14:20:27,372 [DEBUG] numba.core.ssa: ==== SSA block rewrite pass on 646
2025-08-27 14:20:27,372 [DEBUG] numba.core.ssa: Running <numba.core.ssa._FreshVarHandler object at 0x32e9a8b00>
2025-08-27 14:20:27,372 [DEBUG] numba.core.ssa: on stmt: jump 170
2025-08-27 14:20:27,372 [DEBUG] numba.core.ssa: ==== SSA block rewrite pass on 654
2025-08-27 14:20:27,372 [DEBUG] numba.core.ssa: Running <numba.core.ssa._FreshVarHandler object at 0x32e9a8b00>
2025-08-27 14:20:27,372 [DEBUG] numba.core.ssa: on stmt: $const658.2 = const(NoneType, None)
2025-08-27 14:20:27,372 [DEBUG] numba.core.ssa: on stmt: $658return_const.3 = cast(value=$const658.2)
2025-08-27 14:20:27,372 [DEBUG] numba.core.ssa: on stmt: return $658return_const.3
2025-08-27 14:20:27,372 [DEBUG] numba.core.ssa: Replaced assignments: defaultdict(<class 'list'>,
            {0: [<numba.core.ir.Assign object at 0x32ea049b0>],
             174: [<numba.core.ir.Assign object at 0x32ea057f0>],
             422: [<numba.core.ir.Assign object at 0x32ea04dd0>]})
2025-08-27 14:20:27,372 [DEBUG] numba.core.ssa: ==== SSA block rewrite pass on 0
2025-08-27 14:20:27,372 [DEBUG] numba.core.ssa: Running <numba.core.ssa._FixSSAVars object at 0x32e9a8b00>
2025-08-27 14:20:27,372 [DEBUG] numba.core.ssa: on stmt: x = arg(0, name=x)
2025-08-27 14:20:27,372 [DEBUG] numba.core.ssa: on stmt: t_out = arg(1, name=t_out)
2025-08-27 14:20:27,372 [DEBUG] numba.core.ssa: on stmt: interp_win = arg(2, name=interp_win)
2025-08-27 14:20:27,372 [DEBUG] numba.core.ssa: on stmt: interp_delta = arg(3, name=interp_delta)
2025-08-27 14:20:27,372 [DEBUG] numba.core.ssa: on stmt: num_table = arg(4, name=num_table)
2025-08-27 14:20:27,372 [DEBUG] numba.core.ssa: on stmt: scale = arg(5, name=scale)
2025-08-27 14:20:27,372 [DEBUG] numba.core.ssa: on stmt: y = arg(6, name=y)
2025-08-27 14:20:27,372 [DEBUG] numba.core.ssa: on stmt: $4load_global.0 = global(int: <class 'int'>)
2025-08-27 14:20:27,372 [DEBUG] numba.core.ssa: on stmt: $binop_mul16.4 = scale * num_table
2025-08-27 14:20:27,372 [DEBUG] numba.core.ssa: on stmt: index_step = call $4load_global.0($binop_mul16.4, func=$4load_global.0, args=[Var($binop_mul16.4, interpn.py:9)], kws=(), vararg=None, varkwarg=None, target=None)
2025-08-27 14:20:27,372 [DEBUG] numba.core.ssa: on stmt: time_register = const(float, 0.0)
2025-08-27 14:20:27,372 [DEBUG] numba.core.ssa: on stmt: n = const(int, 0)
2025-08-27 14:20:27,372 [DEBUG] numba.core.ssa: on stmt: frac = const(float, 0.0)
2025-08-27 14:20:27,372 [DEBUG] numba.core.ssa: on stmt: index_frac = const(float, 0.0)
2025-08-27 14:20:27,372 [DEBUG] numba.core.ssa: on stmt: offset = const(int, 0)
2025-08-27 14:20:27,372 [DEBUG] numba.core.ssa: on stmt: eta = const(float, 0.0)
2025-08-27 14:20:27,372 [DEBUG] numba.core.ssa: on stmt: weight = const(float, 0.0)
2025-08-27 14:20:27,372 [DEBUG] numba.core.ssa: on stmt: $60load_attr.14 = getattr(value=interp_win, attr=shape)
2025-08-27 14:20:27,372 [DEBUG] numba.core.ssa: on stmt: $const80.15.2 = const(int, 0)
2025-08-27 14:20:27,372 [DEBUG] numba.core.ssa: on stmt: nwin = static_getitem(value=$60load_attr.14, index=0, index_var=$const80.15.2, fn=<built-in function getitem>)
2025-08-27 14:20:27,372 [DEBUG] numba.core.ssa: on stmt: $90load_attr.18 = getattr(value=x, attr=shape)
2025-08-27 14:20:27,372 [DEBUG] numba.core.ssa: on stmt: $const110.19.2 = const(int, 0)
2025-08-27 14:20:27,372 [DEBUG] numba.core.ssa: on stmt: n_orig = static_getitem(value=$90load_attr.18, index=0, index_var=$const110.19.2, fn=<built-in function getitem>)
2025-08-27 14:20:27,372 [DEBUG] numba.core.ssa: on stmt: $120load_attr.22 = getattr(value=t_out, attr=shape)
2025-08-27 14:20:27,372 [DEBUG] numba.core.ssa: on stmt: $const140.23.2 = const(int, 0)
2025-08-27 14:20:27,372 [DEBUG] numba.core.ssa: on stmt: n_out = static_getitem(value=$120load_attr.22, index=0, index_var=$const140.23.2, fn=<built-in function getitem>)
2025-08-27 14:20:27,372 [DEBUG] numba.core.ssa: on stmt: $148load_global.25 = global(prange: <class 'numba.misc.special.prange'>)
2025-08-27 14:20:27,372 [DEBUG] numba.core.ssa: on stmt: $160call.28 = call $148load_global.25(n_out, func=$148load_global.25, args=[Var(n_out, interpn.py:21)], kws=(), vararg=None, varkwarg=None, target=None)
2025-08-27 14:20:27,372 [DEBUG] numba.core.ssa: on stmt: $168get_iter.29 = getiter(value=$160call.28)
2025-08-27 14:20:27,372 [DEBUG] numba.core.ssa: on stmt: $phi170.0 = $168get_iter.29
2025-08-27 14:20:27,372 [DEBUG] numba.core.ssa: on stmt: jump 170
2025-08-27 14:20:27,372 [DEBUG] numba.core.ssa: ==== SSA block rewrite pass on 170
2025-08-27 14:20:27,372 [DEBUG] numba.core.ssa: Running <numba.core.ssa._FixSSAVars object at 0x32e9a8b00>
2025-08-27 14:20:27,372 [DEBUG] numba.core.ssa: on stmt: $170for_iter.1 = iternext(value=$phi170.0)
2025-08-27 14:20:27,372 [DEBUG] numba.core.ssa: on stmt: $170for_iter.2 = pair_first(value=$170for_iter.1)
2025-08-27 14:20:27,372 [DEBUG] numba.core.ssa: on stmt: $170for_iter.3 = pair_second(value=$170for_iter.1)
2025-08-27 14:20:27,372 [DEBUG] numba.core.ssa: on stmt: $phi174.1 = $170for_iter.2
2025-08-27 14:20:27,372 [DEBUG] numba.core.ssa: on stmt: branch $170for_iter.3, 174, 654
2025-08-27 14:20:27,372 [DEBUG] numba.core.ssa: ==== SSA block rewrite pass on 174
2025-08-27 14:20:27,372 [DEBUG] numba.core.ssa: Running <numba.core.ssa._FixSSAVars object at 0x32e9a8b00>
2025-08-27 14:20:27,372 [DEBUG] numba.core.ssa: on stmt: t = $phi174.1
2025-08-27 14:20:27,372 [DEBUG] numba.core.ssa: on stmt: time_register.1 = getitem(value=t_out, index=t, fn=<built-in function getitem>)
2025-08-27 14:20:27,373 [DEBUG] numba.core.ssa: on stmt: $186load_global.5 = global(int: <class 'int'>)
2025-08-27 14:20:27,373 [DEBUG] numba.core.ssa: on stmt: n.1 = call $186load_global.5(time_register.1, func=$186load_global.5, args=[Var(time_register.1, interpn.py:24)], kws=(), vararg=None, varkwarg=None, target=None)
2025-08-27 14:20:27,373 [DEBUG] numba.core.ssa: on stmt: $binop_sub212.12 = time_register.1 - n.1
2025-08-27 14:20:27,373 [DEBUG] numba.core.ssa: on stmt: frac.1 = scale * $binop_sub212.12
2025-08-27 14:20:27,373 [DEBUG] numba.core.ssa: on stmt: index_frac.1 = frac.1 * num_table
2025-08-27 14:20:27,373 [DEBUG] numba.core.ssa: on stmt: $230load_global.17 = global(int: <class 'int'>)
2025-08-27 14:20:27,373 [DEBUG] numba.core.ssa: on stmt: offset = call $230load_global.17(index_frac, func=$230load_global.17, args=[Var(index_frac, interpn.py:14)], kws=(), vararg=None, varkwarg=None, target=None)
2025-08-27 14:20:27,373 [DEBUG] numba.core.ssa: find_def var='index_frac' stmt=offset = call $230load_global.17(index_frac, func=$230load_global.17, args=[Var(index_frac, interpn.py:14)], kws=(), vararg=None, varkwarg=None, target=None)
2025-08-27 14:20:27,373 [DEBUG] numba.core.ssa: replaced with: offset = call $230load_global.17(index_frac.1, func=$230load_global.17, args=[Var(index_frac.1, interpn.py:33)], kws=(), vararg=None, varkwarg=None, target=None)
2025-08-27 14:20:27,373 [DEBUG] numba.core.ssa: on stmt: eta = index_frac - offset
2025-08-27 14:20:27,373 [DEBUG] numba.core.ssa: find_def var='index_frac' stmt=eta = index_frac - offset
2025-08-27 14:20:27,373 [DEBUG] numba.core.ssa: replaced with: eta = index_frac.1 - offset
2025-08-27 14:20:27,373 [DEBUG] numba.core.ssa: on stmt: $260load_global.24 = global(min: <built-in function min>)
2025-08-27 14:20:27,373 [DEBUG] numba.core.ssa: on stmt: $const272.27.3 = const(int, 1)
2025-08-27 14:20:27,373 [DEBUG] numba.core.ssa: on stmt: $binop_add274.28 = n.1 + $const272.27.3
2025-08-27 14:20:27,373 [DEBUG] numba.core.ssa: on stmt: $binop_sub280.31 = nwin - offset
2025-08-27 14:20:27,373 [DEBUG] numba.core.ssa: on stmt: $binop_floordiv286.33 = $binop_sub280.31 // index_step
2025-08-27 14:20:27,373 [DEBUG] numba.core.ssa: on stmt: i_max = call $260load_global.24($binop_add274.28, $binop_floordiv286.33, func=$260load_global.24, args=[Var($binop_add274.28, interpn.py:40), Var($binop_floordiv286.33, interpn.py:40)], kws=(), vararg=None, varkwarg=None, target=None)
2025-08-27 14:20:27,373 [DEBUG] numba.core.ssa: on stmt: $300load_global.35 = global(range: <class 'range'>)
2025-08-27 14:20:27,373 [DEBUG] numba.core.ssa: on stmt: $312call.38 = call $300load_global.35(i_max, func=$300load_global.35, args=[Var(i_max, interpn.py:40)], kws=(), vararg=None, varkwarg=None, target=None)
2025-08-27 14:20:27,373 [DEBUG] numba.core.ssa: on stmt: $320get_iter.39 = getiter(value=$312call.38)
2025-08-27 14:20:27,373 [DEBUG] numba.core.ssa: on stmt: $phi322.1 = $320get_iter.39
2025-08-27 14:20:27,373 [DEBUG] numba.core.ssa: on stmt: jump 322
2025-08-27 14:20:27,373 [DEBUG] numba.core.ssa: ==== SSA block rewrite pass on 322
2025-08-27 14:20:27,373 [DEBUG] numba.core.ssa: Running <numba.core.ssa._FixSSAVars object at 0x32e9a8b00>
2025-08-27 14:20:27,373 [DEBUG] numba.core.ssa: on stmt: $322for_iter.2 = iternext(value=$phi322.1)
2025-08-27 14:20:27,373 [DEBUG] numba.core.ssa: on stmt: $322for_iter.3 = pair_first(value=$322for_iter.2)
2025-08-27 14:20:27,373 [DEBUG] numba.core.ssa: on stmt: $322for_iter.4 = pair_second(value=$322for_iter.2)
2025-08-27 14:20:27,373 [DEBUG] numba.core.ssa: on stmt: $phi326.2 = $322for_iter.3
2025-08-27 14:20:27,373 [DEBUG] numba.core.ssa: on stmt: branch $322for_iter.4, 326, 422
2025-08-27 14:20:27,373 [DEBUG] numba.core.ssa: ==== SSA block rewrite pass on 326
2025-08-27 14:20:27,373 [DEBUG] numba.core.ssa: Running <numba.core.ssa._FixSSAVars object at 0x32e9a8b00>
2025-08-27 14:20:27,373 [DEBUG] numba.core.ssa: on stmt: i = $phi326.2
2025-08-27 14:20:27,373 [DEBUG] numba.core.ssa: on stmt: $binop_mul334.7 = i * index_step
2025-08-27 14:20:27,373 [DEBUG] numba.core.ssa: on stmt: $binop_add338.8 = offset + $binop_mul334.7
2025-08-27 14:20:27,373 [DEBUG] numba.core.ssa: on stmt: $342binary_subscr.9 = getitem(value=interp_win, index=$binop_add338.8, fn=<built-in function getitem>)
2025-08-27 14:20:27,373 [DEBUG] numba.core.ssa: on stmt: $binop_mul354.15 = i * index_step
2025-08-27 14:20:27,373 [DEBUG] numba.core.ssa: on stmt: $binop_add358.16 = offset + $binop_mul354.15
2025-08-27 14:20:27,373 [DEBUG] numba.core.ssa: on stmt: $362binary_subscr.17 = getitem(value=interp_delta, index=$binop_add358.16, fn=<built-in function getitem>)
2025-08-27 14:20:27,373 [DEBUG] numba.core.ssa: on stmt: $binop_mul366.18 = eta * $362binary_subscr.17
2025-08-27 14:20:27,373 [DEBUG] numba.core.ssa: on stmt: weight = $342binary_subscr.9 + $binop_mul366.18
2025-08-27 14:20:27,373 [DEBUG] numba.core.ssa: on stmt: $384binary_subscr.22 = getitem(value=y, index=t, fn=<built-in function getitem>)
2025-08-27 14:20:27,373 [DEBUG] numba.core.ssa: on stmt: $binop_sub394.27 = n.1 - i
2025-08-27 14:20:27,373 [DEBUG] numba.core.ssa: on stmt: $398binary_subscr.28 = getitem(value=x, index=$binop_sub394.27, fn=<built-in function getitem>)
2025-08-27 14:20:27,373 [DEBUG] numba.core.ssa: on stmt: $binop_mul402.29 = weight * $398binary_subscr.28
2025-08-27 14:20:27,373 [DEBUG] numba.core.ssa: on stmt: $binop_iadd406.30 = inplace_binop(fn=<built-in function iadd>, immutable_fn=<built-in function add>, lhs=$384binary_subscr.22, rhs=$binop_mul402.29, static_lhs=Undefined, static_rhs=Undefined)
2025-08-27 14:20:27,373 [DEBUG] numba.core.ssa: on stmt: y[t] = $binop_iadd406.30
2025-08-27 14:20:27,373 [DEBUG] numba.core.ssa: on stmt: jump 322
2025-08-27 14:20:27,373 [DEBUG] numba.core.ssa: ==== SSA block rewrite pass on 422
2025-08-27 14:20:27,373 [DEBUG] numba.core.ssa: Running <numba.core.ssa._FixSSAVars object at 0x32e9a8b00>
2025-08-27 14:20:27,373 [DEBUG] numba.core.ssa: on stmt: $binop_sub428.5 = scale - frac.1
2025-08-27 14:20:27,373 [DEBUG] numba.core.ssa: on stmt: frac.2 = $binop_sub428.5
2025-08-27 14:20:27,373 [DEBUG] numba.core.ssa: on stmt: index_frac.2 = frac.2 * num_table
2025-08-27 14:20:27,373 [DEBUG] numba.core.ssa: on stmt: $442load_global.9 = global(int: <class 'int'>)
2025-08-27 14:20:27,373 [DEBUG] numba.core.ssa: on stmt: offset = call $442load_global.9(index_frac, func=$442load_global.9, args=[Var(index_frac, interpn.py:14)], kws=(), vararg=None, varkwarg=None, target=None)
2025-08-27 14:20:27,373 [DEBUG] numba.core.ssa: find_def var='index_frac' stmt=offset = call $442load_global.9(index_frac, func=$442load_global.9, args=[Var(index_frac, interpn.py:14)], kws=(), vararg=None, varkwarg=None, target=None)
2025-08-27 14:20:27,373 [DEBUG] numba.core.ssa: replaced with: offset = call $442load_global.9(index_frac.2, func=$442load_global.9, args=[Var(index_frac.2, interpn.py:53)], kws=(), vararg=None, varkwarg=None, target=None)
2025-08-27 14:20:27,373 [DEBUG] numba.core.ssa: on stmt: eta = index_frac - offset
2025-08-27 14:20:27,373 [DEBUG] numba.core.ssa: find_def var='index_frac' stmt=eta = index_frac - offset
2025-08-27 14:20:27,373 [DEBUG] numba.core.ssa: replaced with: eta = index_frac.2 - offset
2025-08-27 14:20:27,373 [DEBUG] numba.core.ssa: on stmt: $472load_global.16 = global(min: <built-in function min>)
2025-08-27 14:20:27,373 [DEBUG] numba.core.ssa: on stmt: $binop_sub486.20 = n_orig - n.1
2025-08-27 14:20:27,374 [DEBUG] numba.core.ssa: on stmt: $const490.21.3 = const(int, 1)
2025-08-27 14:20:27,374 [DEBUG] numba.core.ssa: on stmt: $binop_sub492.22 = $binop_sub486.20 - $const490.21.3
2025-08-27 14:20:27,374 [DEBUG] numba.core.ssa: on stmt: $binop_sub498.25 = nwin - offset
2025-08-27 14:20:27,374 [DEBUG] numba.core.ssa: on stmt: $binop_floordiv504.27 = $binop_sub498.25 // index_step
2025-08-27 14:20:27,374 [DEBUG] numba.core.ssa: on stmt: k_max = call $472load_global.16($binop_sub492.22, $binop_floordiv504.27, func=$472load_global.16, args=[Var($binop_sub492.22, interpn.py:60), Var($binop_floordiv504.27, interpn.py:60)], kws=(), vararg=None, varkwarg=None, target=None)
2025-08-27 14:20:27,374 [DEBUG] numba.core.ssa: on stmt: $518load_global.29 = global(range: <class 'range'>)
2025-08-27 14:20:27,374 [DEBUG] numba.core.ssa: on stmt: $530call.32 = call $518load_global.29(k_max, func=$518load_global.29, args=[Var(k_max, interpn.py:60)], kws=(), vararg=None, varkwarg=None, target=None)
2025-08-27 14:20:27,374 [DEBUG] numba.core.ssa: on stmt: $538get_iter.33 = getiter(value=$530call.32)
2025-08-27 14:20:27,374 [DEBUG] numba.core.ssa: on stmt: $phi540.1 = $538get_iter.33
2025-08-27 14:20:27,374 [DEBUG] numba.core.ssa: on stmt: jump 540
2025-08-27 14:20:27,374 [DEBUG] numba.core.ssa: ==== SSA block rewrite pass on 540
2025-08-27 14:20:27,374 [DEBUG] numba.core.ssa: Running <numba.core.ssa._FixSSAVars object at 0x32e9a8b00>
2025-08-27 14:20:27,374 [DEBUG] numba.core.ssa: on stmt: $540for_iter.2 = iternext(value=$phi540.1)
2025-08-27 14:20:27,374 [DEBUG] numba.core.ssa: on stmt: $540for_iter.3 = pair_first(value=$540for_iter.2)
2025-08-27 14:20:27,374 [DEBUG] numba.core.ssa: on stmt: $540for_iter.4 = pair_second(value=$540for_iter.2)
2025-08-27 14:20:27,374 [DEBUG] numba.core.ssa: on stmt: $phi544.2 = $540for_iter.3
2025-08-27 14:20:27,374 [DEBUG] numba.core.ssa: on stmt: branch $540for_iter.4, 544, 646
2025-08-27 14:20:27,374 [DEBUG] numba.core.ssa: ==== SSA block rewrite pass on 544
2025-08-27 14:20:27,374 [DEBUG] numba.core.ssa: Running <numba.core.ssa._FixSSAVars object at 0x32e9a8b00>
2025-08-27 14:20:27,374 [DEBUG] numba.core.ssa: on stmt: k = $phi544.2
2025-08-27 14:20:27,374 [DEBUG] numba.core.ssa: on stmt: $binop_mul552.7 = k * index_step
2025-08-27 14:20:27,374 [DEBUG] numba.core.ssa: on stmt: $binop_add556.8 = offset + $binop_mul552.7
2025-08-27 14:20:27,374 [DEBUG] numba.core.ssa: on stmt: $560binary_subscr.9 = getitem(value=interp_win, index=$binop_add556.8, fn=<built-in function getitem>)
2025-08-27 14:20:27,374 [DEBUG] numba.core.ssa: on stmt: $binop_mul572.15 = k * index_step
2025-08-27 14:20:27,374 [DEBUG] numba.core.ssa: on stmt: $binop_add576.16 = offset + $binop_mul572.15
2025-08-27 14:20:27,374 [DEBUG] numba.core.ssa: on stmt: $580binary_subscr.17 = getitem(value=interp_delta, index=$binop_add576.16, fn=<built-in function getitem>)
2025-08-27 14:20:27,374 [DEBUG] numba.core.ssa: on stmt: $binop_mul584.18 = eta * $580binary_subscr.17
2025-08-27 14:20:27,374 [DEBUG] numba.core.ssa: on stmt: weight = $560binary_subscr.9 + $binop_mul584.18
2025-08-27 14:20:27,374 [DEBUG] numba.core.ssa: on stmt: $602binary_subscr.22 = getitem(value=y, index=t, fn=<built-in function getitem>)
2025-08-27 14:20:27,374 [DEBUG] numba.core.ssa: on stmt: $binop_add612.27 = n.1 + k
2025-08-27 14:20:27,374 [DEBUG] numba.core.ssa: on stmt: $const616.28.3 = const(int, 1)
2025-08-27 14:20:27,374 [DEBUG] numba.core.ssa: on stmt: $binop_add618.29 = $binop_add612.27 + $const616.28.3
2025-08-27 14:20:27,374 [DEBUG] numba.core.ssa: on stmt: $622binary_subscr.30 = getitem(value=x, index=$binop_add618.29, fn=<built-in function getitem>)
2025-08-27 14:20:27,374 [DEBUG] numba.core.ssa: on stmt: $binop_mul626.31 = weight * $622binary_subscr.30
2025-08-27 14:20:27,374 [DEBUG] numba.core.ssa: on stmt: $binop_iadd630.32 = inplace_binop(fn=<built-in function iadd>, immutable_fn=<built-in function add>, lhs=$602binary_subscr.22, rhs=$binop_mul626.31, static_lhs=Undefined, static_rhs=Undefined)
2025-08-27 14:20:27,374 [DEBUG] numba.core.ssa: on stmt: y[t] = $binop_iadd630.32
2025-08-27 14:20:27,374 [DEBUG] numba.core.ssa: on stmt: jump 540
2025-08-27 14:20:27,374 [DEBUG] numba.core.ssa: ==== SSA block rewrite pass on 646
2025-08-27 14:20:27,374 [DEBUG] numba.core.ssa: Running <numba.core.ssa._FixSSAVars object at 0x32e9a8b00>
2025-08-27 14:20:27,374 [DEBUG] numba.core.ssa: on stmt: jump 170
2025-08-27 14:20:27,374 [DEBUG] numba.core.ssa: ==== SSA block rewrite pass on 654
2025-08-27 14:20:27,374 [DEBUG] numba.core.ssa: Running <numba.core.ssa._FixSSAVars object at 0x32e9a8b00>
2025-08-27 14:20:27,374 [DEBUG] numba.core.ssa: on stmt: $const658.2 = const(NoneType, None)
2025-08-27 14:20:27,374 [DEBUG] numba.core.ssa: on stmt: $658return_const.3 = cast(value=$const658.2)
2025-08-27 14:20:27,374 [DEBUG] numba.core.ssa: on stmt: return $658return_const.3
2025-08-27 14:20:27,374 [DEBUG] numba.core.ssa: Fix SSA violator on var offset
2025-08-27 14:20:27,374 [DEBUG] numba.core.ssa: ==== SSA block rewrite pass on 0
2025-08-27 14:20:27,374 [DEBUG] numba.core.ssa: Running <numba.core.ssa._FreshVarHandler object at 0x32e9a8b00>
2025-08-27 14:20:27,374 [DEBUG] numba.core.ssa: on stmt: x = arg(0, name=x)
2025-08-27 14:20:27,374 [DEBUG] numba.core.ssa: on stmt: t_out = arg(1, name=t_out)
2025-08-27 14:20:27,374 [DEBUG] numba.core.ssa: on stmt: interp_win = arg(2, name=interp_win)
2025-08-27 14:20:27,374 [DEBUG] numba.core.ssa: on stmt: interp_delta = arg(3, name=interp_delta)
2025-08-27 14:20:27,374 [DEBUG] numba.core.ssa: on stmt: num_table = arg(4, name=num_table)
2025-08-27 14:20:27,374 [DEBUG] numba.core.ssa: on stmt: scale = arg(5, name=scale)
2025-08-27 14:20:27,374 [DEBUG] numba.core.ssa: on stmt: y = arg(6, name=y)
2025-08-27 14:20:27,374 [DEBUG] numba.core.ssa: on stmt: $4load_global.0 = global(int: <class 'int'>)
2025-08-27 14:20:27,374 [DEBUG] numba.core.ssa: on stmt: $binop_mul16.4 = scale * num_table
2025-08-27 14:20:27,374 [DEBUG] numba.core.ssa: on stmt: index_step = call $4load_global.0($binop_mul16.4, func=$4load_global.0, args=[Var($binop_mul16.4, interpn.py:9)], kws=(), vararg=None, varkwarg=None, target=None)
2025-08-27 14:20:27,374 [DEBUG] numba.core.ssa: on stmt: time_register = const(float, 0.0)
2025-08-27 14:20:27,374 [DEBUG] numba.core.ssa: on stmt: n = const(int, 0)
2025-08-27 14:20:27,374 [DEBUG] numba.core.ssa: on stmt: frac = const(float, 0.0)
2025-08-27 14:20:27,374 [DEBUG] numba.core.ssa: on stmt: index_frac = const(float, 0.0)
2025-08-27 14:20:27,374 [DEBUG] numba.core.ssa: on stmt: offset = const(int, 0)
2025-08-27 14:20:27,374 [DEBUG] numba.core.ssa: first assign: offset
2025-08-27 14:20:27,374 [DEBUG] numba.core.ssa: replaced with: offset = const(int, 0)
2025-08-27 14:20:27,374 [DEBUG] numba.core.ssa: on stmt: eta = const(float, 0.0)
2025-08-27 14:20:27,374 [DEBUG] numba.core.ssa: on stmt: weight = const(float, 0.0)
2025-08-27 14:20:27,374 [DEBUG] numba.core.ssa: on stmt: $60load_attr.14 = getattr(value=interp_win, attr=shape)
2025-08-27 14:20:27,374 [DEBUG] numba.core.ssa: on stmt: $const80.15.2 = const(int, 0)
2025-08-27 14:20:27,374 [DEBUG] numba.core.ssa: on stmt: nwin = static_getitem(value=$60load_attr.14, index=0, index_var=$const80.15.2, fn=<built-in function getitem>)
2025-08-27 14:20:27,374 [DEBUG] numba.core.ssa: on stmt: $90load_attr.18 = getattr(value=x, attr=shape)
2025-08-27 14:20:27,374 [DEBUG] numba.core.ssa: on stmt: $const110.19.2 = const(int, 0)
2025-08-27 14:20:27,374 [DEBUG] numba.core.ssa: on stmt: n_orig = static_getitem(value=$90load_attr.18, index=0, index_var=$const110.19.2, fn=<built-in function getitem>)
2025-08-27 14:20:27,375 [DEBUG] numba.core.ssa: on stmt: $120load_attr.22 = getattr(value=t_out, attr=shape)
2025-08-27 14:20:27,375 [DEBUG] numba.core.ssa: on stmt: $const140.23.2 = const(int, 0)
2025-08-27 14:20:27,375 [DEBUG] numba.core.ssa: on stmt: n_out = static_getitem(value=$120load_attr.22, index=0, index_var=$const140.23.2, fn=<built-in function getitem>)
2025-08-27 14:20:27,375 [DEBUG] numba.core.ssa: on stmt: $148load_global.25 = global(prange: <class 'numba.misc.special.prange'>)
2025-08-27 14:20:27,375 [DEBUG] numba.core.ssa: on stmt: $160call.28 = call $148load_global.25(n_out, func=$148load_global.25, args=[Var(n_out, interpn.py:21)], kws=(), vararg=None, varkwarg=None, target=None)
2025-08-27 14:20:27,375 [DEBUG] numba.core.ssa: on stmt: $168get_iter.29 = getiter(value=$160call.28)
2025-08-27 14:20:27,375 [DEBUG] numba.core.ssa: on stmt: $phi170.0 = $168get_iter.29
2025-08-27 14:20:27,375 [DEBUG] numba.core.ssa: on stmt: jump 170
2025-08-27 14:20:27,375 [DEBUG] numba.core.ssa: ==== SSA block rewrite pass on 170
2025-08-27 14:20:27,375 [DEBUG] numba.core.ssa: Running <numba.core.ssa._FreshVarHandler object at 0x32e9a8b00>
2025-08-27 14:20:27,375 [DEBUG] numba.core.ssa: on stmt: $170for_iter.1 = iternext(value=$phi170.0)
2025-08-27 14:20:27,375 [DEBUG] numba.core.ssa: on stmt: $170for_iter.2 = pair_first(value=$170for_iter.1)
2025-08-27 14:20:27,375 [DEBUG] numba.core.ssa: on stmt: $170for_iter.3 = pair_second(value=$170for_iter.1)
2025-08-27 14:20:27,375 [DEBUG] numba.core.ssa: on stmt: $phi174.1 = $170for_iter.2
2025-08-27 14:20:27,375 [DEBUG] numba.core.ssa: on stmt: branch $170for_iter.3, 174, 654
2025-08-27 14:20:27,375 [DEBUG] numba.core.ssa: ==== SSA block rewrite pass on 174
2025-08-27 14:20:27,375 [DEBUG] numba.core.ssa: Running <numba.core.ssa._FreshVarHandler object at 0x32e9a8b00>
2025-08-27 14:20:27,375 [DEBUG] numba.core.ssa: on stmt: t = $phi174.1
2025-08-27 14:20:27,375 [DEBUG] numba.core.ssa: on stmt: time_register.1 = getitem(value=t_out, index=t, fn=<built-in function getitem>)
2025-08-27 14:20:27,375 [DEBUG] numba.core.ssa: on stmt: $186load_global.5 = global(int: <class 'int'>)
2025-08-27 14:20:27,375 [DEBUG] numba.core.ssa: on stmt: n.1 = call $186load_global.5(time_register.1, func=$186load_global.5, args=[Var(time_register.1, interpn.py:24)], kws=(), vararg=None, varkwarg=None, target=None)
2025-08-27 14:20:27,375 [DEBUG] numba.core.ssa: on stmt: $binop_sub212.12 = time_register.1 - n.1
2025-08-27 14:20:27,375 [DEBUG] numba.core.ssa: on stmt: frac.1 = scale * $binop_sub212.12
2025-08-27 14:20:27,375 [DEBUG] numba.core.ssa: on stmt: index_frac.1 = frac.1 * num_table
2025-08-27 14:20:27,375 [DEBUG] numba.core.ssa: on stmt: $230load_global.17 = global(int: <class 'int'>)
2025-08-27 14:20:27,375 [DEBUG] numba.core.ssa: on stmt: offset = call $230load_global.17(index_frac.1, func=$230load_global.17, args=[Var(index_frac.1, interpn.py:33)], kws=(), vararg=None, varkwarg=None, target=None)
2025-08-27 14:20:27,375 [DEBUG] numba.core.ssa: replaced with: offset.1 = call $230load_global.17(index_frac.1, func=$230load_global.17, args=[Var(index_frac.1, interpn.py:33)], kws=(), vararg=None, varkwarg=None, target=None)
2025-08-27 14:20:27,375 [DEBUG] numba.core.ssa: on stmt: eta = index_frac.1 - offset
2025-08-27 14:20:27,375 [DEBUG] numba.core.ssa: on stmt: $260load_global.24 = global(min: <built-in function min>)
2025-08-27 14:20:27,375 [DEBUG] numba.core.ssa: on stmt: $const272.27.3 = const(int, 1)
2025-08-27 14:20:27,375 [DEBUG] numba.core.ssa: on stmt: $binop_add274.28 = n.1 + $const272.27.3
2025-08-27 14:20:27,375 [DEBUG] numba.core.ssa: on stmt: $binop_sub280.31 = nwin - offset
2025-08-27 14:20:27,375 [DEBUG] numba.core.ssa: on stmt: $binop_floordiv286.33 = $binop_sub280.31 // index_step
2025-08-27 14:20:27,375 [DEBUG] numba.core.ssa: on stmt: i_max = call $260load_global.24($binop_add274.28, $binop_floordiv286.33, func=$260load_global.24, args=[Var($binop_add274.28, interpn.py:40), Var($binop_floordiv286.33, interpn.py:40)], kws=(), vararg=None, varkwarg=None, target=None)
2025-08-27 14:20:27,375 [DEBUG] numba.core.ssa: on stmt: $300load_global.35 = global(range: <class 'range'>)
2025-08-27 14:20:27,375 [DEBUG] numba.core.ssa: on stmt: $312call.38 = call $300load_global.35(i_max, func=$300load_global.35, args=[Var(i_max, interpn.py:40)], kws=(), vararg=None, varkwarg=None, target=None)
2025-08-27 14:20:27,375 [DEBUG] numba.core.ssa: on stmt: $320get_iter.39 = getiter(value=$312call.38)
2025-08-27 14:20:27,375 [DEBUG] numba.core.ssa: on stmt: $phi322.1 = $320get_iter.39
2025-08-27 14:20:27,375 [DEBUG] numba.core.ssa: on stmt: jump 322
2025-08-27 14:20:27,375 [DEBUG] numba.core.ssa: ==== SSA block rewrite pass on 322
2025-08-27 14:20:27,375 [DEBUG] numba.core.ssa: Running <numba.core.ssa._FreshVarHandler object at 0x32e9a8b00>
2025-08-27 14:20:27,375 [DEBUG] numba.core.ssa: on stmt: $322for_iter.2 = iternext(value=$phi322.1)
2025-08-27 14:20:27,375 [DEBUG] numba.core.ssa: on stmt: $322for_iter.3 = pair_first(value=$322for_iter.2)
2025-08-27 14:20:27,375 [DEBUG] numba.core.ssa: on stmt: $322for_iter.4 = pair_second(value=$322for_iter.2)
2025-08-27 14:20:27,375 [DEBUG] numba.core.ssa: on stmt: $phi326.2 = $322for_iter.3
2025-08-27 14:20:27,375 [DEBUG] numba.core.ssa: on stmt: branch $322for_iter.4, 326, 422
2025-08-27 14:20:27,375 [DEBUG] numba.core.ssa: ==== SSA block rewrite pass on 326
2025-08-27 14:20:27,375 [DEBUG] numba.core.ssa: Running <numba.core.ssa._FreshVarHandler object at 0x32e9a8b00>
2025-08-27 14:20:27,375 [DEBUG] numba.core.ssa: on stmt: i = $phi326.2
2025-08-27 14:20:27,375 [DEBUG] numba.core.ssa: on stmt: $binop_mul334.7 = i * index_step
2025-08-27 14:20:27,375 [DEBUG] numba.core.ssa: on stmt: $binop_add338.8 = offset + $binop_mul334.7
2025-08-27 14:20:27,375 [DEBUG] numba.core.ssa: on stmt: $342binary_subscr.9 = getitem(value=interp_win, index=$binop_add338.8, fn=<built-in function getitem>)
2025-08-27 14:20:27,375 [DEBUG] numba.core.ssa: on stmt: $binop_mul354.15 = i * index_step
2025-08-27 14:20:27,375 [DEBUG] numba.core.ssa: on stmt: $binop_add358.16 = offset + $binop_mul354.15
2025-08-27 14:20:27,375 [DEBUG] numba.core.ssa: on stmt: $362binary_subscr.17 = getitem(value=interp_delta, index=$binop_add358.16, fn=<built-in function getitem>)
2025-08-27 14:20:27,375 [DEBUG] numba.core.ssa: on stmt: $binop_mul366.18 = eta * $362binary_subscr.17
2025-08-27 14:20:27,375 [DEBUG] numba.core.ssa: on stmt: weight = $342binary_subscr.9 + $binop_mul366.18
2025-08-27 14:20:27,375 [DEBUG] numba.core.ssa: on stmt: $384binary_subscr.22 = getitem(value=y, index=t, fn=<built-in function getitem>)
2025-08-27 14:20:27,375 [DEBUG] numba.core.ssa: on stmt: $binop_sub394.27 = n.1 - i
2025-08-27 14:20:27,375 [DEBUG] numba.core.ssa: on stmt: $398binary_subscr.28 = getitem(value=x, index=$binop_sub394.27, fn=<built-in function getitem>)
2025-08-27 14:20:27,375 [DEBUG] numba.core.ssa: on stmt: $binop_mul402.29 = weight * $398binary_subscr.28
2025-08-27 14:20:27,375 [DEBUG] numba.core.ssa: on stmt: $binop_iadd406.30 = inplace_binop(fn=<built-in function iadd>, immutable_fn=<built-in function add>, lhs=$384binary_subscr.22, rhs=$binop_mul402.29, static_lhs=Undefined, static_rhs=Undefined)
2025-08-27 14:20:27,375 [DEBUG] numba.core.ssa: on stmt: y[t] = $binop_iadd406.30
2025-08-27 14:20:27,375 [DEBUG] numba.core.ssa: on stmt: jump 322
2025-08-27 14:20:27,375 [DEBUG] numba.core.ssa: ==== SSA block rewrite pass on 422
2025-08-27 14:20:27,375 [DEBUG] numba.core.ssa: Running <numba.core.ssa._FreshVarHandler object at 0x32e9a8b00>
2025-08-27 14:20:27,375 [DEBUG] numba.core.ssa: on stmt: $binop_sub428.5 = scale - frac.1
2025-08-27 14:20:27,375 [DEBUG] numba.core.ssa: on stmt: frac.2 = $binop_sub428.5
2025-08-27 14:20:27,375 [DEBUG] numba.core.ssa: on stmt: index_frac.2 = frac.2 * num_table
2025-08-27 14:20:27,375 [DEBUG] numba.core.ssa: on stmt: $442load_global.9 = global(int: <class 'int'>)
2025-08-27 14:20:27,375 [DEBUG] numba.core.ssa: on stmt: offset = call $442load_global.9(index_frac.2, func=$442load_global.9, args=[Var(index_frac.2, interpn.py:53)], kws=(), vararg=None, varkwarg=None, target=None)
2025-08-27 14:20:27,375 [DEBUG] numba.core.ssa: replaced with: offset.2 = call $442load_global.9(index_frac.2, func=$442load_global.9, args=[Var(index_frac.2, interpn.py:53)], kws=(), vararg=None, varkwarg=None, target=None)
2025-08-27 14:20:27,376 [DEBUG] numba.core.ssa: on stmt: eta = index_frac.2 - offset
2025-08-27 14:20:27,376 [DEBUG] numba.core.ssa: on stmt: $472load_global.16 = global(min: <built-in function min>)
2025-08-27 14:20:27,376 [DEBUG] numba.core.ssa: on stmt: $binop_sub486.20 = n_orig - n.1
2025-08-27 14:20:27,376 [DEBUG] numba.core.ssa: on stmt: $const490.21.3 = const(int, 1)
2025-08-27 14:20:27,376 [DEBUG] numba.core.ssa: on stmt: $binop_sub492.22 = $binop_sub486.20 - $const490.21.3
2025-08-27 14:20:27,376 [DEBUG] numba.core.ssa: on stmt: $binop_sub498.25 = nwin - offset
2025-08-27 14:20:27,376 [DEBUG] numba.core.ssa: on stmt: $binop_floordiv504.27 = $binop_sub498.25 // index_step
2025-08-27 14:20:27,376 [DEBUG] numba.core.ssa: on stmt: k_max = call $472load_global.16($binop_sub492.22, $binop_floordiv504.27, func=$472load_global.16, args=[Var($binop_sub492.22, interpn.py:60), Var($binop_floordiv504.27, interpn.py:60)], kws=(), vararg=None, varkwarg=None, target=None)
2025-08-27 14:20:27,376 [DEBUG] numba.core.ssa: on stmt: $518load_global.29 = global(range: <class 'range'>)
2025-08-27 14:20:27,376 [DEBUG] numba.core.ssa: on stmt: $530call.32 = call $518load_global.29(k_max, func=$518load_global.29, args=[Var(k_max, interpn.py:60)], kws=(), vararg=None, varkwarg=None, target=None)
2025-08-27 14:20:27,376 [DEBUG] numba.core.ssa: on stmt: $538get_iter.33 = getiter(value=$530call.32)
2025-08-27 14:20:27,376 [DEBUG] numba.core.ssa: on stmt: $phi540.1 = $538get_iter.33
2025-08-27 14:20:27,376 [DEBUG] numba.core.ssa: on stmt: jump 540
2025-08-27 14:20:27,376 [DEBUG] numba.core.ssa: ==== SSA block rewrite pass on 540
2025-08-27 14:20:27,376 [DEBUG] numba.core.ssa: Running <numba.core.ssa._FreshVarHandler object at 0x32e9a8b00>
2025-08-27 14:20:27,376 [DEBUG] numba.core.ssa: on stmt: $540for_iter.2 = iternext(value=$phi540.1)
2025-08-27 14:20:27,376 [DEBUG] numba.core.ssa: on stmt: $540for_iter.3 = pair_first(value=$540for_iter.2)
2025-08-27 14:20:27,376 [DEBUG] numba.core.ssa: on stmt: $540for_iter.4 = pair_second(value=$540for_iter.2)
2025-08-27 14:20:27,376 [DEBUG] numba.core.ssa: on stmt: $phi544.2 = $540for_iter.3
2025-08-27 14:20:27,376 [DEBUG] numba.core.ssa: on stmt: branch $540for_iter.4, 544, 646
2025-08-27 14:20:27,376 [DEBUG] numba.core.ssa: ==== SSA block rewrite pass on 544
2025-08-27 14:20:27,376 [DEBUG] numba.core.ssa: Running <numba.core.ssa._FreshVarHandler object at 0x32e9a8b00>
2025-08-27 14:20:27,376 [DEBUG] numba.core.ssa: on stmt: k = $phi544.2
2025-08-27 14:20:27,376 [DEBUG] numba.core.ssa: on stmt: $binop_mul552.7 = k * index_step
2025-08-27 14:20:27,376 [DEBUG] numba.core.ssa: on stmt: $binop_add556.8 = offset + $binop_mul552.7
2025-08-27 14:20:27,376 [DEBUG] numba.core.ssa: on stmt: $560binary_subscr.9 = getitem(value=interp_win, index=$binop_add556.8, fn=<built-in function getitem>)
2025-08-27 14:20:27,376 [DEBUG] numba.core.ssa: on stmt: $binop_mul572.15 = k * index_step
2025-08-27 14:20:27,376 [DEBUG] numba.core.ssa: on stmt: $binop_add576.16 = offset + $binop_mul572.15
2025-08-27 14:20:27,376 [DEBUG] numba.core.ssa: on stmt: $580binary_subscr.17 = getitem(value=interp_delta, index=$binop_add576.16, fn=<built-in function getitem>)
2025-08-27 14:20:27,376 [DEBUG] numba.core.ssa: on stmt: $binop_mul584.18 = eta * $580binary_subscr.17
2025-08-27 14:20:27,376 [DEBUG] numba.core.ssa: on stmt: weight = $560binary_subscr.9 + $binop_mul584.18
2025-08-27 14:20:27,376 [DEBUG] numba.core.ssa: on stmt: $602binary_subscr.22 = getitem(value=y, index=t, fn=<built-in function getitem>)
2025-08-27 14:20:27,376 [DEBUG] numba.core.ssa: on stmt: $binop_add612.27 = n.1 + k
2025-08-27 14:20:27,376 [DEBUG] numba.core.ssa: on stmt: $const616.28.3 = const(int, 1)
2025-08-27 14:20:27,376 [DEBUG] numba.core.ssa: on stmt: $binop_add618.29 = $binop_add612.27 + $const616.28.3
2025-08-27 14:20:27,376 [DEBUG] numba.core.ssa: on stmt: $622binary_subscr.30 = getitem(value=x, index=$binop_add618.29, fn=<built-in function getitem>)
2025-08-27 14:20:27,376 [DEBUG] numba.core.ssa: on stmt: $binop_mul626.31 = weight * $622binary_subscr.30
2025-08-27 14:20:27,376 [DEBUG] numba.core.ssa: on stmt: $binop_iadd630.32 = inplace_binop(fn=<built-in function iadd>, immutable_fn=<built-in function add>, lhs=$602binary_subscr.22, rhs=$binop_mul626.31, static_lhs=Undefined, static_rhs=Undefined)
2025-08-27 14:20:27,376 [DEBUG] numba.core.ssa: on stmt: y[t] = $binop_iadd630.32
2025-08-27 14:20:27,376 [DEBUG] numba.core.ssa: on stmt: jump 540
2025-08-27 14:20:27,376 [DEBUG] numba.core.ssa: ==== SSA block rewrite pass on 646
2025-08-27 14:20:27,376 [DEBUG] numba.core.ssa: Running <numba.core.ssa._FreshVarHandler object at 0x32e9a8b00>
2025-08-27 14:20:27,376 [DEBUG] numba.core.ssa: on stmt: jump 170
2025-08-27 14:20:27,376 [DEBUG] numba.core.ssa: ==== SSA block rewrite pass on 654
2025-08-27 14:20:27,376 [DEBUG] numba.core.ssa: Running <numba.core.ssa._FreshVarHandler object at 0x32e9a8b00>
2025-08-27 14:20:27,376 [DEBUG] numba.core.ssa: on stmt: $const658.2 = const(NoneType, None)
2025-08-27 14:20:27,376 [DEBUG] numba.core.ssa: on stmt: $658return_const.3 = cast(value=$const658.2)
2025-08-27 14:20:27,376 [DEBUG] numba.core.ssa: on stmt: return $658return_const.3
2025-08-27 14:20:27,376 [DEBUG] numba.core.ssa: Replaced assignments: defaultdict(<class 'list'>,
            {0: [<numba.core.ir.Assign object at 0x32ea04350>],
             174: [<numba.core.ir.Assign object at 0x32ea052b0>],
             422: [<numba.core.ir.Assign object at 0x32ea04a10>]})
2025-08-27 14:20:27,376 [DEBUG] numba.core.ssa: ==== SSA block rewrite pass on 0
2025-08-27 14:20:27,376 [DEBUG] numba.core.ssa: Running <numba.core.ssa._FixSSAVars object at 0x32e9a8b00>
2025-08-27 14:20:27,376 [DEBUG] numba.core.ssa: on stmt: x = arg(0, name=x)
2025-08-27 14:20:27,376 [DEBUG] numba.core.ssa: on stmt: t_out = arg(1, name=t_out)
2025-08-27 14:20:27,376 [DEBUG] numba.core.ssa: on stmt: interp_win = arg(2, name=interp_win)
2025-08-27 14:20:27,376 [DEBUG] numba.core.ssa: on stmt: interp_delta = arg(3, name=interp_delta)
2025-08-27 14:20:27,376 [DEBUG] numba.core.ssa: on stmt: num_table = arg(4, name=num_table)
2025-08-27 14:20:27,376 [DEBUG] numba.core.ssa: on stmt: scale = arg(5, name=scale)
2025-08-27 14:20:27,376 [DEBUG] numba.core.ssa: on stmt: y = arg(6, name=y)
2025-08-27 14:20:27,376 [DEBUG] numba.core.ssa: on stmt: $4load_global.0 = global(int: <class 'int'>)
2025-08-27 14:20:27,376 [DEBUG] numba.core.ssa: on stmt: $binop_mul16.4 = scale * num_table
2025-08-27 14:20:27,376 [DEBUG] numba.core.ssa: on stmt: index_step = call $4load_global.0($binop_mul16.4, func=$4load_global.0, args=[Var($binop_mul16.4, interpn.py:9)], kws=(), vararg=None, varkwarg=None, target=None)
2025-08-27 14:20:27,376 [DEBUG] numba.core.ssa: on stmt: time_register = const(float, 0.0)
2025-08-27 14:20:27,376 [DEBUG] numba.core.ssa: on stmt: n = const(int, 0)
2025-08-27 14:20:27,376 [DEBUG] numba.core.ssa: on stmt: frac = const(float, 0.0)
2025-08-27 14:20:27,376 [DEBUG] numba.core.ssa: on stmt: index_frac = const(float, 0.0)
2025-08-27 14:20:27,376 [DEBUG] numba.core.ssa: on stmt: offset = const(int, 0)
2025-08-27 14:20:27,376 [DEBUG] numba.core.ssa: on stmt: eta = const(float, 0.0)
2025-08-27 14:20:27,376 [DEBUG] numba.core.ssa: on stmt: weight = const(float, 0.0)
2025-08-27 14:20:27,376 [DEBUG] numba.core.ssa: on stmt: $60load_attr.14 = getattr(value=interp_win, attr=shape)
2025-08-27 14:20:27,376 [DEBUG] numba.core.ssa: on stmt: $const80.15.2 = const(int, 0)
2025-08-27 14:20:27,377 [DEBUG] numba.core.ssa: on stmt: nwin = static_getitem(value=$60load_attr.14, index=0, index_var=$const80.15.2, fn=<built-in function getitem>)
2025-08-27 14:20:27,377 [DEBUG] numba.core.ssa: on stmt: $90load_attr.18 = getattr(value=x, attr=shape)
2025-08-27 14:20:27,377 [DEBUG] numba.core.ssa: on stmt: $const110.19.2 = const(int, 0)
2025-08-27 14:20:27,377 [DEBUG] numba.core.ssa: on stmt: n_orig = static_getitem(value=$90load_attr.18, index=0, index_var=$const110.19.2, fn=<built-in function getitem>)
2025-08-27 14:20:27,377 [DEBUG] numba.core.ssa: on stmt: $120load_attr.22 = getattr(value=t_out, attr=shape)
2025-08-27 14:20:27,377 [DEBUG] numba.core.ssa: on stmt: $const140.23.2 = const(int, 0)
2025-08-27 14:20:27,377 [DEBUG] numba.core.ssa: on stmt: n_out = static_getitem(value=$120load_attr.22, index=0, index_var=$const140.23.2, fn=<built-in function getitem>)
2025-08-27 14:20:27,377 [DEBUG] numba.core.ssa: on stmt: $148load_global.25 = global(prange: <class 'numba.misc.special.prange'>)
2025-08-27 14:20:27,377 [DEBUG] numba.core.ssa: on stmt: $160call.28 = call $148load_global.25(n_out, func=$148load_global.25, args=[Var(n_out, interpn.py:21)], kws=(), vararg=None, varkwarg=None, target=None)
2025-08-27 14:20:27,377 [DEBUG] numba.core.ssa: on stmt: $168get_iter.29 = getiter(value=$160call.28)
2025-08-27 14:20:27,377 [DEBUG] numba.core.ssa: on stmt: $phi170.0 = $168get_iter.29
2025-08-27 14:20:27,377 [DEBUG] numba.core.ssa: on stmt: jump 170
2025-08-27 14:20:27,377 [DEBUG] numba.core.ssa: ==== SSA block rewrite pass on 170
2025-08-27 14:20:27,377 [DEBUG] numba.core.ssa: Running <numba.core.ssa._FixSSAVars object at 0x32e9a8b00>
2025-08-27 14:20:27,377 [DEBUG] numba.core.ssa: on stmt: $170for_iter.1 = iternext(value=$phi170.0)
2025-08-27 14:20:27,377 [DEBUG] numba.core.ssa: on stmt: $170for_iter.2 = pair_first(value=$170for_iter.1)
2025-08-27 14:20:27,377 [DEBUG] numba.core.ssa: on stmt: $170for_iter.3 = pair_second(value=$170for_iter.1)
2025-08-27 14:20:27,377 [DEBUG] numba.core.ssa: on stmt: $phi174.1 = $170for_iter.2
2025-08-27 14:20:27,377 [DEBUG] numba.core.ssa: on stmt: branch $170for_iter.3, 174, 654
2025-08-27 14:20:27,377 [DEBUG] numba.core.ssa: ==== SSA block rewrite pass on 174
2025-08-27 14:20:27,377 [DEBUG] numba.core.ssa: Running <numba.core.ssa._FixSSAVars object at 0x32e9a8b00>
2025-08-27 14:20:27,377 [DEBUG] numba.core.ssa: on stmt: t = $phi174.1
2025-08-27 14:20:27,377 [DEBUG] numba.core.ssa: on stmt: time_register.1 = getitem(value=t_out, index=t, fn=<built-in function getitem>)
2025-08-27 14:20:27,377 [DEBUG] numba.core.ssa: on stmt: $186load_global.5 = global(int: <class 'int'>)
2025-08-27 14:20:27,377 [DEBUG] numba.core.ssa: on stmt: n.1 = call $186load_global.5(time_register.1, func=$186load_global.5, args=[Var(time_register.1, interpn.py:24)], kws=(), vararg=None, varkwarg=None, target=None)
2025-08-27 14:20:27,377 [DEBUG] numba.core.ssa: on stmt: $binop_sub212.12 = time_register.1 - n.1
2025-08-27 14:20:27,377 [DEBUG] numba.core.ssa: on stmt: frac.1 = scale * $binop_sub212.12
2025-08-27 14:20:27,377 [DEBUG] numba.core.ssa: on stmt: index_frac.1 = frac.1 * num_table
2025-08-27 14:20:27,377 [DEBUG] numba.core.ssa: on stmt: $230load_global.17 = global(int: <class 'int'>)
2025-08-27 14:20:27,377 [DEBUG] numba.core.ssa: on stmt: offset.1 = call $230load_global.17(index_frac.1, func=$230load_global.17, args=[Var(index_frac.1, interpn.py:33)], kws=(), vararg=None, varkwarg=None, target=None)
2025-08-27 14:20:27,377 [DEBUG] numba.core.ssa: on stmt: eta = index_frac.1 - offset
2025-08-27 14:20:27,377 [DEBUG] numba.core.ssa: find_def var='offset' stmt=eta = index_frac.1 - offset
2025-08-27 14:20:27,377 [DEBUG] numba.core.ssa: replaced with: eta = index_frac.1 - offset.1
2025-08-27 14:20:27,377 [DEBUG] numba.core.ssa: on stmt: $260load_global.24 = global(min: <built-in function min>)
2025-08-27 14:20:27,377 [DEBUG] numba.core.ssa: on stmt: $const272.27.3 = const(int, 1)
2025-08-27 14:20:27,377 [DEBUG] numba.core.ssa: on stmt: $binop_add274.28 = n.1 + $const272.27.3
2025-08-27 14:20:27,377 [DEBUG] numba.core.ssa: on stmt: $binop_sub280.31 = nwin - offset
2025-08-27 14:20:27,377 [DEBUG] numba.core.ssa: find_def var='offset' stmt=$binop_sub280.31 = nwin - offset
2025-08-27 14:20:27,377 [DEBUG] numba.core.ssa: replaced with: $binop_sub280.31 = nwin - offset.1
2025-08-27 14:20:27,377 [DEBUG] numba.core.ssa: on stmt: $binop_floordiv286.33 = $binop_sub280.31 // index_step
2025-08-27 14:20:27,377 [DEBUG] numba.core.ssa: on stmt: i_max = call $260load_global.24($binop_add274.28, $binop_floordiv286.33, func=$260load_global.24, args=[Var($binop_add274.28, interpn.py:40), Var($binop_floordiv286.33, interpn.py:40)], kws=(), vararg=None, varkwarg=None, target=None)
2025-08-27 14:20:27,377 [DEBUG] numba.core.ssa: on stmt: $300load_global.35 = global(range: <class 'range'>)
2025-08-27 14:20:27,377 [DEBUG] numba.core.ssa: on stmt: $312call.38 = call $300load_global.35(i_max, func=$300load_global.35, args=[Var(i_max, interpn.py:40)], kws=(), vararg=None, varkwarg=None, target=None)
2025-08-27 14:20:27,377 [DEBUG] numba.core.ssa: on stmt: $320get_iter.39 = getiter(value=$312call.38)
2025-08-27 14:20:27,377 [DEBUG] numba.core.ssa: on stmt: $phi322.1 = $320get_iter.39
2025-08-27 14:20:27,377 [DEBUG] numba.core.ssa: on stmt: jump 322
2025-08-27 14:20:27,377 [DEBUG] numba.core.ssa: ==== SSA block rewrite pass on 322
2025-08-27 14:20:27,377 [DEBUG] numba.core.ssa: Running <numba.core.ssa._FixSSAVars object at 0x32e9a8b00>
2025-08-27 14:20:27,377 [DEBUG] numba.core.ssa: on stmt: $322for_iter.2 = iternext(value=$phi322.1)
2025-08-27 14:20:27,377 [DEBUG] numba.core.ssa: on stmt: $322for_iter.3 = pair_first(value=$322for_iter.2)
2025-08-27 14:20:27,377 [DEBUG] numba.core.ssa: on stmt: $322for_iter.4 = pair_second(value=$322for_iter.2)
2025-08-27 14:20:27,377 [DEBUG] numba.core.ssa: on stmt: $phi326.2 = $322for_iter.3
2025-08-27 14:20:27,377 [DEBUG] numba.core.ssa: on stmt: branch $322for_iter.4, 326, 422
2025-08-27 14:20:27,377 [DEBUG] numba.core.ssa: ==== SSA block rewrite pass on 326
2025-08-27 14:20:27,377 [DEBUG] numba.core.ssa: Running <numba.core.ssa._FixSSAVars object at 0x32e9a8b00>
2025-08-27 14:20:27,377 [DEBUG] numba.core.ssa: on stmt: i = $phi326.2
2025-08-27 14:20:27,377 [DEBUG] numba.core.ssa: on stmt: $binop_mul334.7 = i * index_step
2025-08-27 14:20:27,377 [DEBUG] numba.core.ssa: on stmt: $binop_add338.8 = offset + $binop_mul334.7
2025-08-27 14:20:27,377 [DEBUG] numba.core.ssa: find_def var='offset' stmt=$binop_add338.8 = offset + $binop_mul334.7
2025-08-27 14:20:27,377 [DEBUG] numba.core.ssa: find_def_from_top label 326
2025-08-27 14:20:27,377 [DEBUG] numba.core.ssa: idom 322 from label 326
2025-08-27 14:20:27,377 [DEBUG] numba.core.ssa: find_def_from_bottom label 322
2025-08-27 14:20:27,377 [DEBUG] numba.core.ssa: find_def_from_top label 322
2025-08-27 14:20:27,377 [DEBUG] numba.core.ssa: idom 174 from label 322
2025-08-27 14:20:27,377 [DEBUG] numba.core.ssa: find_def_from_bottom label 174
2025-08-27 14:20:27,377 [DEBUG] numba.core.ssa: replaced with: $binop_add338.8 = offset.1 + $binop_mul334.7
2025-08-27 14:20:27,377 [DEBUG] numba.core.ssa: on stmt: $342binary_subscr.9 = getitem(value=interp_win, index=$binop_add338.8, fn=<built-in function getitem>)
2025-08-27 14:20:27,377 [DEBUG] numba.core.ssa: on stmt: $binop_mul354.15 = i * index_step
2025-08-27 14:20:27,377 [DEBUG] numba.core.ssa: on stmt: $binop_add358.16 = offset + $binop_mul354.15
2025-08-27 14:20:27,377 [DEBUG] numba.core.ssa: find_def var='offset' stmt=$binop_add358.16 = offset + $binop_mul354.15
2025-08-27 14:20:27,377 [DEBUG] numba.core.ssa: find_def_from_top label 326
2025-08-27 14:20:27,378 [DEBUG] numba.core.ssa: idom 322 from label 326
2025-08-27 14:20:27,378 [DEBUG] numba.core.ssa: find_def_from_bottom label 322
2025-08-27 14:20:27,378 [DEBUG] numba.core.ssa: find_def_from_top label 322
2025-08-27 14:20:27,378 [DEBUG] numba.core.ssa: idom 174 from label 322
2025-08-27 14:20:27,378 [DEBUG] numba.core.ssa: find_def_from_bottom label 174
2025-08-27 14:20:27,378 [DEBUG] numba.core.ssa: replaced with: $binop_add358.16 = offset.1 + $binop_mul354.15
2025-08-27 14:20:27,378 [DEBUG] numba.core.ssa: on stmt: $362binary_subscr.17 = getitem(value=interp_delta, index=$binop_add358.16, fn=<built-in function getitem>)
2025-08-27 14:20:27,378 [DEBUG] numba.core.ssa: on stmt: $binop_mul366.18 = eta * $362binary_subscr.17
2025-08-27 14:20:27,378 [DEBUG] numba.core.ssa: on stmt: weight = $342binary_subscr.9 + $binop_mul366.18
2025-08-27 14:20:27,378 [DEBUG] numba.core.ssa: on stmt: $384binary_subscr.22 = getitem(value=y, index=t, fn=<built-in function getitem>)
2025-08-27 14:20:27,378 [DEBUG] numba.core.ssa: on stmt: $binop_sub394.27 = n.1 - i
2025-08-27 14:20:27,378 [DEBUG] numba.core.ssa: on stmt: $398binary_subscr.28 = getitem(value=x, index=$binop_sub394.27, fn=<built-in function getitem>)
2025-08-27 14:20:27,378 [DEBUG] numba.core.ssa: on stmt: $binop_mul402.29 = weight * $398binary_subscr.28
2025-08-27 14:20:27,378 [DEBUG] numba.core.ssa: on stmt: $binop_iadd406.30 = inplace_binop(fn=<built-in function iadd>, immutable_fn=<built-in function add>, lhs=$384binary_subscr.22, rhs=$binop_mul402.29, static_lhs=Undefined, static_rhs=Undefined)
2025-08-27 14:20:27,378 [DEBUG] numba.core.ssa: on stmt: y[t] = $binop_iadd406.30
2025-08-27 14:20:27,378 [DEBUG] numba.core.ssa: on stmt: jump 322
2025-08-27 14:20:27,378 [DEBUG] numba.core.ssa: ==== SSA block rewrite pass on 422
2025-08-27 14:20:27,378 [DEBUG] numba.core.ssa: Running <numba.core.ssa._FixSSAVars object at 0x32e9a8b00>
2025-08-27 14:20:27,378 [DEBUG] numba.core.ssa: on stmt: $binop_sub428.5 = scale - frac.1
2025-08-27 14:20:27,378 [DEBUG] numba.core.ssa: on stmt: frac.2 = $binop_sub428.5
2025-08-27 14:20:27,378 [DEBUG] numba.core.ssa: on stmt: index_frac.2 = frac.2 * num_table
2025-08-27 14:20:27,378 [DEBUG] numba.core.ssa: on stmt: $442load_global.9 = global(int: <class 'int'>)
2025-08-27 14:20:27,378 [DEBUG] numba.core.ssa: on stmt: offset.2 = call $442load_global.9(index_frac.2, func=$442load_global.9, args=[Var(index_frac.2, interpn.py:53)], kws=(), vararg=None, varkwarg=None, target=None)
2025-08-27 14:20:27,378 [DEBUG] numba.core.ssa: on stmt: eta = index_frac.2 - offset
2025-08-27 14:20:27,378 [DEBUG] numba.core.ssa: find_def var='offset' stmt=eta = index_frac.2 - offset
2025-08-27 14:20:27,378 [DEBUG] numba.core.ssa: replaced with: eta = index_frac.2 - offset.2
2025-08-27 14:20:27,378 [DEBUG] numba.core.ssa: on stmt: $472load_global.16 = global(min: <built-in function min>)
2025-08-27 14:20:27,378 [DEBUG] numba.core.ssa: on stmt: $binop_sub486.20 = n_orig - n.1
2025-08-27 14:20:27,378 [DEBUG] numba.core.ssa: on stmt: $const490.21.3 = const(int, 1)
2025-08-27 14:20:27,378 [DEBUG] numba.core.ssa: on stmt: $binop_sub492.22 = $binop_sub486.20 - $const490.21.3
2025-08-27 14:20:27,378 [DEBUG] numba.core.ssa: on stmt: $binop_sub498.25 = nwin - offset
2025-08-27 14:20:27,378 [DEBUG] numba.core.ssa: find_def var='offset' stmt=$binop_sub498.25 = nwin - offset
2025-08-27 14:20:27,378 [DEBUG] numba.core.ssa: replaced with: $binop_sub498.25 = nwin - offset.2
2025-08-27 14:20:27,378 [DEBUG] numba.core.ssa: on stmt: $binop_floordiv504.27 = $binop_sub498.25 // index_step
2025-08-27 14:20:27,378 [DEBUG] numba.core.ssa: on stmt: k_max = call $472load_global.16($binop_sub492.22, $binop_floordiv504.27, func=$472load_global.16, args=[Var($binop_sub492.22, interpn.py:60), Var($binop_floordiv504.27, interpn.py:60)], kws=(), vararg=None, varkwarg=None, target=None)
2025-08-27 14:20:27,378 [DEBUG] numba.core.ssa: on stmt: $518load_global.29 = global(range: <class 'range'>)
2025-08-27 14:20:27,378 [DEBUG] numba.core.ssa: on stmt: $530call.32 = call $518load_global.29(k_max, func=$518load_global.29, args=[Var(k_max, interpn.py:60)], kws=(), vararg=None, varkwarg=None, target=None)
2025-08-27 14:20:27,378 [DEBUG] numba.core.ssa: on stmt: $538get_iter.33 = getiter(value=$530call.32)
2025-08-27 14:20:27,378 [DEBUG] numba.core.ssa: on stmt: $phi540.1 = $538get_iter.33
2025-08-27 14:20:27,378 [DEBUG] numba.core.ssa: on stmt: jump 540
2025-08-27 14:20:27,378 [DEBUG] numba.core.ssa: ==== SSA block rewrite pass on 540
2025-08-27 14:20:27,378 [DEBUG] numba.core.ssa: Running <numba.core.ssa._FixSSAVars object at 0x32e9a8b00>
2025-08-27 14:20:27,378 [DEBUG] numba.core.ssa: on stmt: $540for_iter.2 = iternext(value=$phi540.1)
2025-08-27 14:20:27,378 [DEBUG] numba.core.ssa: on stmt: $540for_iter.3 = pair_first(value=$540for_iter.2)
2025-08-27 14:20:27,378 [DEBUG] numba.core.ssa: on stmt: $540for_iter.4 = pair_second(value=$540for_iter.2)
2025-08-27 14:20:27,378 [DEBUG] numba.core.ssa: on stmt: $phi544.2 = $540for_iter.3
2025-08-27 14:20:27,378 [DEBUG] numba.core.ssa: on stmt: branch $540for_iter.4, 544, 646
2025-08-27 14:20:27,378 [DEBUG] numba.core.ssa: ==== SSA block rewrite pass on 544
2025-08-27 14:20:27,378 [DEBUG] numba.core.ssa: Running <numba.core.ssa._FixSSAVars object at 0x32e9a8b00>
2025-08-27 14:20:27,378 [DEBUG] numba.core.ssa: on stmt: k = $phi544.2
2025-08-27 14:20:27,378 [DEBUG] numba.core.ssa: on stmt: $binop_mul552.7 = k * index_step
2025-08-27 14:20:27,378 [DEBUG] numba.core.ssa: on stmt: $binop_add556.8 = offset + $binop_mul552.7
2025-08-27 14:20:27,378 [DEBUG] numba.core.ssa: find_def var='offset' stmt=$binop_add556.8 = offset + $binop_mul552.7
2025-08-27 14:20:27,378 [DEBUG] numba.core.ssa: find_def_from_top label 544
2025-08-27 14:20:27,378 [DEBUG] numba.core.ssa: idom 540 from label 544
2025-08-27 14:20:27,378 [DEBUG] numba.core.ssa: find_def_from_bottom label 540
2025-08-27 14:20:27,378 [DEBUG] numba.core.ssa: find_def_from_top label 540
2025-08-27 14:20:27,378 [DEBUG] numba.core.ssa: idom 422 from label 540
2025-08-27 14:20:27,378 [DEBUG] numba.core.ssa: find_def_from_bottom label 422
2025-08-27 14:20:27,378 [DEBUG] numba.core.ssa: replaced with: $binop_add556.8 = offset.2 + $binop_mul552.7
2025-08-27 14:20:27,378 [DEBUG] numba.core.ssa: on stmt: $560binary_subscr.9 = getitem(value=interp_win, index=$binop_add556.8, fn=<built-in function getitem>)
2025-08-27 14:20:27,378 [DEBUG] numba.core.ssa: on stmt: $binop_mul572.15 = k * index_step
2025-08-27 14:20:27,378 [DEBUG] numba.core.ssa: on stmt: $binop_add576.16 = offset + $binop_mul572.15
2025-08-27 14:20:27,378 [DEBUG] numba.core.ssa: find_def var='offset' stmt=$binop_add576.16 = offset + $binop_mul572.15
2025-08-27 14:20:27,378 [DEBUG] numba.core.ssa: find_def_from_top label 544
2025-08-27 14:20:27,378 [DEBUG] numba.core.ssa: idom 540 from label 544
2025-08-27 14:20:27,378 [DEBUG] numba.core.ssa: find_def_from_bottom label 540
2025-08-27 14:20:27,378 [DEBUG] numba.core.ssa: find_def_from_top label 540
2025-08-27 14:20:27,378 [DEBUG] numba.core.ssa: idom 422 from label 540
2025-08-27 14:20:27,378 [DEBUG] numba.core.ssa: find_def_from_bottom label 422
2025-08-27 14:20:27,378 [DEBUG] numba.core.ssa: replaced with: $binop_add576.16 = offset.2 + $binop_mul572.15
2025-08-27 14:20:27,379 [DEBUG] numba.core.ssa: on stmt: $580binary_subscr.17 = getitem(value=interp_delta, index=$binop_add576.16, fn=<built-in function getitem>)
2025-08-27 14:20:27,379 [DEBUG] numba.core.ssa: on stmt: $binop_mul584.18 = eta * $580binary_subscr.17
2025-08-27 14:20:27,379 [DEBUG] numba.core.ssa: on stmt: weight = $560binary_subscr.9 + $binop_mul584.18
2025-08-27 14:20:27,379 [DEBUG] numba.core.ssa: on stmt: $602binary_subscr.22 = getitem(value=y, index=t, fn=<built-in function getitem>)
2025-08-27 14:20:27,379 [DEBUG] numba.core.ssa: on stmt: $binop_add612.27 = n.1 + k
2025-08-27 14:20:27,379 [DEBUG] numba.core.ssa: on stmt: $const616.28.3 = const(int, 1)
2025-08-27 14:20:27,379 [DEBUG] numba.core.ssa: on stmt: $binop_add618.29 = $binop_add612.27 + $const616.28.3
2025-08-27 14:20:27,379 [DEBUG] numba.core.ssa: on stmt: $622binary_subscr.30 = getitem(value=x, index=$binop_add618.29, fn=<built-in function getitem>)
2025-08-27 14:20:27,379 [DEBUG] numba.core.ssa: on stmt: $binop_mul626.31 = weight * $622binary_subscr.30
2025-08-27 14:20:27,379 [DEBUG] numba.core.ssa: on stmt: $binop_iadd630.32 = inplace_binop(fn=<built-in function iadd>, immutable_fn=<built-in function add>, lhs=$602binary_subscr.22, rhs=$binop_mul626.31, static_lhs=Undefined, static_rhs=Undefined)
2025-08-27 14:20:27,379 [DEBUG] numba.core.ssa: on stmt: y[t] = $binop_iadd630.32
2025-08-27 14:20:27,379 [DEBUG] numba.core.ssa: on stmt: jump 540
2025-08-27 14:20:27,379 [DEBUG] numba.core.ssa: ==== SSA block rewrite pass on 646
2025-08-27 14:20:27,379 [DEBUG] numba.core.ssa: Running <numba.core.ssa._FixSSAVars object at 0x32e9a8b00>
2025-08-27 14:20:27,379 [DEBUG] numba.core.ssa: on stmt: jump 170
2025-08-27 14:20:27,379 [DEBUG] numba.core.ssa: ==== SSA block rewrite pass on 654
2025-08-27 14:20:27,379 [DEBUG] numba.core.ssa: Running <numba.core.ssa._FixSSAVars object at 0x32e9a8b00>
2025-08-27 14:20:27,379 [DEBUG] numba.core.ssa: on stmt: $const658.2 = const(NoneType, None)
2025-08-27 14:20:27,379 [DEBUG] numba.core.ssa: on stmt: $658return_const.3 = cast(value=$const658.2)
2025-08-27 14:20:27,379 [DEBUG] numba.core.ssa: on stmt: return $658return_const.3
2025-08-27 14:20:27,379 [DEBUG] numba.core.ssa: Fix SSA violator on var eta
2025-08-27 14:20:27,379 [DEBUG] numba.core.ssa: ==== SSA block rewrite pass on 0
2025-08-27 14:20:27,379 [DEBUG] numba.core.ssa: Running <numba.core.ssa._FreshVarHandler object at 0x32ea1c170>
2025-08-27 14:20:27,379 [DEBUG] numba.core.ssa: on stmt: x = arg(0, name=x)
2025-08-27 14:20:27,379 [DEBUG] numba.core.ssa: on stmt: t_out = arg(1, name=t_out)
2025-08-27 14:20:27,379 [DEBUG] numba.core.ssa: on stmt: interp_win = arg(2, name=interp_win)
2025-08-27 14:20:27,379 [DEBUG] numba.core.ssa: on stmt: interp_delta = arg(3, name=interp_delta)
2025-08-27 14:20:27,379 [DEBUG] numba.core.ssa: on stmt: num_table = arg(4, name=num_table)
2025-08-27 14:20:27,379 [DEBUG] numba.core.ssa: on stmt: scale = arg(5, name=scale)
2025-08-27 14:20:27,379 [DEBUG] numba.core.ssa: on stmt: y = arg(6, name=y)
2025-08-27 14:20:27,379 [DEBUG] numba.core.ssa: on stmt: $4load_global.0 = global(int: <class 'int'>)
2025-08-27 14:20:27,379 [DEBUG] numba.core.ssa: on stmt: $binop_mul16.4 = scale * num_table
2025-08-27 14:20:27,379 [DEBUG] numba.core.ssa: on stmt: index_step = call $4load_global.0($binop_mul16.4, func=$4load_global.0, args=[Var($binop_mul16.4, interpn.py:9)], kws=(), vararg=None, varkwarg=None, target=None)
2025-08-27 14:20:27,379 [DEBUG] numba.core.ssa: on stmt: time_register = const(float, 0.0)
2025-08-27 14:20:27,379 [DEBUG] numba.core.ssa: on stmt: n = const(int, 0)
2025-08-27 14:20:27,379 [DEBUG] numba.core.ssa: on stmt: frac = const(float, 0.0)
2025-08-27 14:20:27,379 [DEBUG] numba.core.ssa: on stmt: index_frac = const(float, 0.0)
2025-08-27 14:20:27,379 [DEBUG] numba.core.ssa: on stmt: offset = const(int, 0)
2025-08-27 14:20:27,379 [DEBUG] numba.core.ssa: on stmt: eta = const(float, 0.0)
2025-08-27 14:20:27,379 [DEBUG] numba.core.ssa: first assign: eta
2025-08-27 14:20:27,379 [DEBUG] numba.core.ssa: replaced with: eta = const(float, 0.0)
2025-08-27 14:20:27,379 [DEBUG] numba.core.ssa: on stmt: weight = const(float, 0.0)
2025-08-27 14:20:27,379 [DEBUG] numba.core.ssa: on stmt: $60load_attr.14 = getattr(value=interp_win, attr=shape)
2025-08-27 14:20:27,379 [DEBUG] numba.core.ssa: on stmt: $const80.15.2 = const(int, 0)
2025-08-27 14:20:27,379 [DEBUG] numba.core.ssa: on stmt: nwin = static_getitem(value=$60load_attr.14, index=0, index_var=$const80.15.2, fn=<built-in function getitem>)
2025-08-27 14:20:27,379 [DEBUG] numba.core.ssa: on stmt: $90load_attr.18 = getattr(value=x, attr=shape)
2025-08-27 14:20:27,379 [DEBUG] numba.core.ssa: on stmt: $const110.19.2 = const(int, 0)
2025-08-27 14:20:27,379 [DEBUG] numba.core.ssa: on stmt: n_orig = static_getitem(value=$90load_attr.18, index=0, index_var=$const110.19.2, fn=<built-in function getitem>)
2025-08-27 14:20:27,379 [DEBUG] numba.core.ssa: on stmt: $120load_attr.22 = getattr(value=t_out, attr=shape)
2025-08-27 14:20:27,379 [DEBUG] numba.core.ssa: on stmt: $const140.23.2 = const(int, 0)
2025-08-27 14:20:27,379 [DEBUG] numba.core.ssa: on stmt: n_out = static_getitem(value=$120load_attr.22, index=0, index_var=$const140.23.2, fn=<built-in function getitem>)
2025-08-27 14:20:27,379 [DEBUG] numba.core.ssa: on stmt: $148load_global.25 = global(prange: <class 'numba.misc.special.prange'>)
2025-08-27 14:20:27,379 [DEBUG] numba.core.ssa: on stmt: $160call.28 = call $148load_global.25(n_out, func=$148load_global.25, args=[Var(n_out, interpn.py:21)], kws=(), vararg=None, varkwarg=None, target=None)
2025-08-27 14:20:27,379 [DEBUG] numba.core.ssa: on stmt: $168get_iter.29 = getiter(value=$160call.28)
2025-08-27 14:20:27,379 [DEBUG] numba.core.ssa: on stmt: $phi170.0 = $168get_iter.29
2025-08-27 14:20:27,379 [DEBUG] numba.core.ssa: on stmt: jump 170
2025-08-27 14:20:27,379 [DEBUG] numba.core.ssa: ==== SSA block rewrite pass on 170
2025-08-27 14:20:27,379 [DEBUG] numba.core.ssa: Running <numba.core.ssa._FreshVarHandler object at 0x32ea1c170>
2025-08-27 14:20:27,379 [DEBUG] numba.core.ssa: on stmt: $170for_iter.1 = iternext(value=$phi170.0)
2025-08-27 14:20:27,379 [DEBUG] numba.core.ssa: on stmt: $170for_iter.2 = pair_first(value=$170for_iter.1)
2025-08-27 14:20:27,379 [DEBUG] numba.core.ssa: on stmt: $170for_iter.3 = pair_second(value=$170for_iter.1)
2025-08-27 14:20:27,379 [DEBUG] numba.core.ssa: on stmt: $phi174.1 = $170for_iter.2
2025-08-27 14:20:27,379 [DEBUG] numba.core.ssa: on stmt: branch $170for_iter.3, 174, 654
2025-08-27 14:20:27,379 [DEBUG] numba.core.ssa: ==== SSA block rewrite pass on 174
2025-08-27 14:20:27,379 [DEBUG] numba.core.ssa: Running <numba.core.ssa._FreshVarHandler object at 0x32ea1c170>
2025-08-27 14:20:27,379 [DEBUG] numba.core.ssa: on stmt: t = $phi174.1
2025-08-27 14:20:27,379 [DEBUG] numba.core.ssa: on stmt: time_register.1 = getitem(value=t_out, index=t, fn=<built-in function getitem>)
2025-08-27 14:20:27,379 [DEBUG] numba.core.ssa: on stmt: $186load_global.5 = global(int: <class 'int'>)
2025-08-27 14:20:27,379 [DEBUG] numba.core.ssa: on stmt: n.1 = call $186load_global.5(time_register.1, func=$186load_global.5, args=[Var(time_register.1, interpn.py:24)], kws=(), vararg=None, varkwarg=None, target=None)
2025-08-27 14:20:27,379 [DEBUG] numba.core.ssa: on stmt: $binop_sub212.12 = time_register.1 - n.1
2025-08-27 14:20:27,379 [DEBUG] numba.core.ssa: on stmt: frac.1 = scale * $binop_sub212.12
2025-08-27 14:20:27,379 [DEBUG] numba.core.ssa: on stmt: index_frac.1 = frac.1 * num_table
2025-08-27 14:20:27,379 [DEBUG] numba.core.ssa: on stmt: $230load_global.17 = global(int: <class 'int'>)
2025-08-27 14:20:27,379 [DEBUG] numba.core.ssa: on stmt: offset.1 = call $230load_global.17(index_frac.1, func=$230load_global.17, args=[Var(index_frac.1, interpn.py:33)], kws=(), vararg=None, varkwarg=None, target=None)
2025-08-27 14:20:27,379 [DEBUG] numba.core.ssa: on stmt: eta = index_frac.1 - offset.1
2025-08-27 14:20:27,380 [DEBUG] numba.core.ssa: replaced with: eta.1 = index_frac.1 - offset.1
2025-08-27 14:20:27,380 [DEBUG] numba.core.ssa: on stmt: $260load_global.24 = global(min: <built-in function min>)
2025-08-27 14:20:27,380 [DEBUG] numba.core.ssa: on stmt: $const272.27.3 = const(int, 1)
2025-08-27 14:20:27,380 [DEBUG] numba.core.ssa: on stmt: $binop_add274.28 = n.1 + $const272.27.3
2025-08-27 14:20:27,380 [DEBUG] numba.core.ssa: on stmt: $binop_sub280.31 = nwin - offset.1
2025-08-27 14:20:27,380 [DEBUG] numba.core.ssa: on stmt: $binop_floordiv286.33 = $binop_sub280.31 // index_step
2025-08-27 14:20:27,380 [DEBUG] numba.core.ssa: on stmt: i_max = call $260load_global.24($binop_add274.28, $binop_floordiv286.33, func=$260load_global.24, args=[Var($binop_add274.28, interpn.py:40), Var($binop_floordiv286.33, interpn.py:40)], kws=(), vararg=None, varkwarg=None, target=None)
2025-08-27 14:20:27,380 [DEBUG] numba.core.ssa: on stmt: $300load_global.35 = global(range: <class 'range'>)
2025-08-27 14:20:27,380 [DEBUG] numba.core.ssa: on stmt: $312call.38 = call $300load_global.35(i_max, func=$300load_global.35, args=[Var(i_max, interpn.py:40)], kws=(), vararg=None, varkwarg=None, target=None)
2025-08-27 14:20:27,380 [DEBUG] numba.core.ssa: on stmt: $320get_iter.39 = getiter(value=$312call.38)
2025-08-27 14:20:27,380 [DEBUG] numba.core.ssa: on stmt: $phi322.1 = $320get_iter.39
2025-08-27 14:20:27,380 [DEBUG] numba.core.ssa: on stmt: jump 322
2025-08-27 14:20:27,380 [DEBUG] numba.core.ssa: ==== SSA block rewrite pass on 322
2025-08-27 14:20:27,380 [DEBUG] numba.core.ssa: Running <numba.core.ssa._FreshVarHandler object at 0x32ea1c170>
2025-08-27 14:20:27,380 [DEBUG] numba.core.ssa: on stmt: $322for_iter.2 = iternext(value=$phi322.1)
2025-08-27 14:20:27,380 [DEBUG] numba.core.ssa: on stmt: $322for_iter.3 = pair_first(value=$322for_iter.2)
2025-08-27 14:20:27,380 [DEBUG] numba.core.ssa: on stmt: $322for_iter.4 = pair_second(value=$322for_iter.2)
2025-08-27 14:20:27,380 [DEBUG] numba.core.ssa: on stmt: $phi326.2 = $322for_iter.3
2025-08-27 14:20:27,380 [DEBUG] numba.core.ssa: on stmt: branch $322for_iter.4, 326, 422
2025-08-27 14:20:27,380 [DEBUG] numba.core.ssa: ==== SSA block rewrite pass on 326
2025-08-27 14:20:27,380 [DEBUG] numba.core.ssa: Running <numba.core.ssa._FreshVarHandler object at 0x32ea1c170>
2025-08-27 14:20:27,380 [DEBUG] numba.core.ssa: on stmt: i = $phi326.2
2025-08-27 14:20:27,380 [DEBUG] numba.core.ssa: on stmt: $binop_mul334.7 = i * index_step
2025-08-27 14:20:27,380 [DEBUG] numba.core.ssa: on stmt: $binop_add338.8 = offset.1 + $binop_mul334.7
2025-08-27 14:20:27,380 [DEBUG] numba.core.ssa: on stmt: $342binary_subscr.9 = getitem(value=interp_win, index=$binop_add338.8, fn=<built-in function getitem>)
2025-08-27 14:20:27,380 [DEBUG] numba.core.ssa: on stmt: $binop_mul354.15 = i * index_step
2025-08-27 14:20:27,380 [DEBUG] numba.core.ssa: on stmt: $binop_add358.16 = offset.1 + $binop_mul354.15
2025-08-27 14:20:27,380 [DEBUG] numba.core.ssa: on stmt: $362binary_subscr.17 = getitem(value=interp_delta, index=$binop_add358.16, fn=<built-in function getitem>)
2025-08-27 14:20:27,380 [DEBUG] numba.core.ssa: on stmt: $binop_mul366.18 = eta * $362binary_subscr.17
2025-08-27 14:20:27,380 [DEBUG] numba.core.ssa: on stmt: weight = $342binary_subscr.9 + $binop_mul366.18
2025-08-27 14:20:27,380 [DEBUG] numba.core.ssa: on stmt: $384binary_subscr.22 = getitem(value=y, index=t, fn=<built-in function getitem>)
2025-08-27 14:20:27,380 [DEBUG] numba.core.ssa: on stmt: $binop_sub394.27 = n.1 - i
2025-08-27 14:20:27,380 [DEBUG] numba.core.ssa: on stmt: $398binary_subscr.28 = getitem(value=x, index=$binop_sub394.27, fn=<built-in function getitem>)
2025-08-27 14:20:27,380 [DEBUG] numba.core.ssa: on stmt: $binop_mul402.29 = weight * $398binary_subscr.28
2025-08-27 14:20:27,380 [DEBUG] numba.core.ssa: on stmt: $binop_iadd406.30 = inplace_binop(fn=<built-in function iadd>, immutable_fn=<built-in function add>, lhs=$384binary_subscr.22, rhs=$binop_mul402.29, static_lhs=Undefined, static_rhs=Undefined)
2025-08-27 14:20:27,380 [DEBUG] numba.core.ssa: on stmt: y[t] = $binop_iadd406.30
2025-08-27 14:20:27,380 [DEBUG] numba.core.ssa: on stmt: jump 322
2025-08-27 14:20:27,380 [DEBUG] numba.core.ssa: ==== SSA block rewrite pass on 422
2025-08-27 14:20:27,380 [DEBUG] numba.core.ssa: Running <numba.core.ssa._FreshVarHandler object at 0x32ea1c170>
2025-08-27 14:20:27,380 [DEBUG] numba.core.ssa: on stmt: $binop_sub428.5 = scale - frac.1
2025-08-27 14:20:27,380 [DEBUG] numba.core.ssa: on stmt: frac.2 = $binop_sub428.5
2025-08-27 14:20:27,380 [DEBUG] numba.core.ssa: on stmt: index_frac.2 = frac.2 * num_table
2025-08-27 14:20:27,380 [DEBUG] numba.core.ssa: on stmt: $442load_global.9 = global(int: <class 'int'>)
2025-08-27 14:20:27,380 [DEBUG] numba.core.ssa: on stmt: offset.2 = call $442load_global.9(index_frac.2, func=$442load_global.9, args=[Var(index_frac.2, interpn.py:53)], kws=(), vararg=None, varkwarg=None, target=None)
2025-08-27 14:20:27,380 [DEBUG] numba.core.ssa: on stmt: eta = index_frac.2 - offset.2
2025-08-27 14:20:27,380 [DEBUG] numba.core.ssa: replaced with: eta.2 = index_frac.2 - offset.2
2025-08-27 14:20:27,380 [DEBUG] numba.core.ssa: on stmt: $472load_global.16 = global(min: <built-in function min>)
2025-08-27 14:20:27,380 [DEBUG] numba.core.ssa: on stmt: $binop_sub486.20 = n_orig - n.1
2025-08-27 14:20:27,380 [DEBUG] numba.core.ssa: on stmt: $const490.21.3 = const(int, 1)
2025-08-27 14:20:27,380 [DEBUG] numba.core.ssa: on stmt: $binop_sub492.22 = $binop_sub486.20 - $const490.21.3
2025-08-27 14:20:27,380 [DEBUG] numba.core.ssa: on stmt: $binop_sub498.25 = nwin - offset.2
2025-08-27 14:20:27,380 [DEBUG] numba.core.ssa: on stmt: $binop_floordiv504.27 = $binop_sub498.25 // index_step
2025-08-27 14:20:27,380 [DEBUG] numba.core.ssa: on stmt: k_max = call $472load_global.16($binop_sub492.22, $binop_floordiv504.27, func=$472load_global.16, args=[Var($binop_sub492.22, interpn.py:60), Var($binop_floordiv504.27, interpn.py:60)], kws=(), vararg=None, varkwarg=None, target=None)
2025-08-27 14:20:27,380 [DEBUG] numba.core.ssa: on stmt: $518load_global.29 = global(range: <class 'range'>)
2025-08-27 14:20:27,380 [DEBUG] numba.core.ssa: on stmt: $530call.32 = call $518load_global.29(k_max, func=$518load_global.29, args=[Var(k_max, interpn.py:60)], kws=(), vararg=None, varkwarg=None, target=None)
2025-08-27 14:20:27,380 [DEBUG] numba.core.ssa: on stmt: $538get_iter.33 = getiter(value=$530call.32)
2025-08-27 14:20:27,380 [DEBUG] numba.core.ssa: on stmt: $phi540.1 = $538get_iter.33
2025-08-27 14:20:27,380 [DEBUG] numba.core.ssa: on stmt: jump 540
2025-08-27 14:20:27,380 [DEBUG] numba.core.ssa: ==== SSA block rewrite pass on 540
2025-08-27 14:20:27,380 [DEBUG] numba.core.ssa: Running <numba.core.ssa._FreshVarHandler object at 0x32ea1c170>
2025-08-27 14:20:27,380 [DEBUG] numba.core.ssa: on stmt: $540for_iter.2 = iternext(value=$phi540.1)
2025-08-27 14:20:27,380 [DEBUG] numba.core.ssa: on stmt: $540for_iter.3 = pair_first(value=$540for_iter.2)
2025-08-27 14:20:27,380 [DEBUG] numba.core.ssa: on stmt: $540for_iter.4 = pair_second(value=$540for_iter.2)
2025-08-27 14:20:27,380 [DEBUG] numba.core.ssa: on stmt: $phi544.2 = $540for_iter.3
2025-08-27 14:20:27,380 [DEBUG] numba.core.ssa: on stmt: branch $540for_iter.4, 544, 646
2025-08-27 14:20:27,380 [DEBUG] numba.core.ssa: ==== SSA block rewrite pass on 544
2025-08-27 14:20:27,380 [DEBUG] numba.core.ssa: Running <numba.core.ssa._FreshVarHandler object at 0x32ea1c170>
2025-08-27 14:20:27,380 [DEBUG] numba.core.ssa: on stmt: k = $phi544.2
2025-08-27 14:20:27,380 [DEBUG] numba.core.ssa: on stmt: $binop_mul552.7 = k * index_step
2025-08-27 14:20:27,380 [DEBUG] numba.core.ssa: on stmt: $binop_add556.8 = offset.2 + $binop_mul552.7
2025-08-27 14:20:27,380 [DEBUG] numba.core.ssa: on stmt: $560binary_subscr.9 = getitem(value=interp_win, index=$binop_add556.8, fn=<built-in function getitem>)
2025-08-27 14:20:27,380 [DEBUG] numba.core.ssa: on stmt: $binop_mul572.15 = k * index_step
2025-08-27 14:20:27,380 [DEBUG] numba.core.ssa: on stmt: $binop_add576.16 = offset.2 + $binop_mul572.15
2025-08-27 14:20:27,380 [DEBUG] numba.core.ssa: on stmt: $580binary_subscr.17 = getitem(value=interp_delta, index=$binop_add576.16, fn=<built-in function getitem>)
2025-08-27 14:20:27,381 [DEBUG] numba.core.ssa: on stmt: $binop_mul584.18 = eta * $580binary_subscr.17
2025-08-27 14:20:27,381 [DEBUG] numba.core.ssa: on stmt: weight = $560binary_subscr.9 + $binop_mul584.18
2025-08-27 14:20:27,381 [DEBUG] numba.core.ssa: on stmt: $602binary_subscr.22 = getitem(value=y, index=t, fn=<built-in function getitem>)
2025-08-27 14:20:27,381 [DEBUG] numba.core.ssa: on stmt: $binop_add612.27 = n.1 + k
2025-08-27 14:20:27,381 [DEBUG] numba.core.ssa: on stmt: $const616.28.3 = const(int, 1)
2025-08-27 14:20:27,381 [DEBUG] numba.core.ssa: on stmt: $binop_add618.29 = $binop_add612.27 + $const616.28.3
2025-08-27 14:20:27,381 [DEBUG] numba.core.ssa: on stmt: $622binary_subscr.30 = getitem(value=x, index=$binop_add618.29, fn=<built-in function getitem>)
2025-08-27 14:20:27,381 [DEBUG] numba.core.ssa: on stmt: $binop_mul626.31 = weight * $622binary_subscr.30
2025-08-27 14:20:27,381 [DEBUG] numba.core.ssa: on stmt: $binop_iadd630.32 = inplace_binop(fn=<built-in function iadd>, immutable_fn=<built-in function add>, lhs=$602binary_subscr.22, rhs=$binop_mul626.31, static_lhs=Undefined, static_rhs=Undefined)
2025-08-27 14:20:27,381 [DEBUG] numba.core.ssa: on stmt: y[t] = $binop_iadd630.32
2025-08-27 14:20:27,381 [DEBUG] numba.core.ssa: on stmt: jump 540
2025-08-27 14:20:27,381 [DEBUG] numba.core.ssa: ==== SSA block rewrite pass on 646
2025-08-27 14:20:27,381 [DEBUG] numba.core.ssa: Running <numba.core.ssa._FreshVarHandler object at 0x32ea1c170>
2025-08-27 14:20:27,381 [DEBUG] numba.core.ssa: on stmt: jump 170
2025-08-27 14:20:27,381 [DEBUG] numba.core.ssa: ==== SSA block rewrite pass on 654
2025-08-27 14:20:27,381 [DEBUG] numba.core.ssa: Running <numba.core.ssa._FreshVarHandler object at 0x32ea1c170>
2025-08-27 14:20:27,381 [DEBUG] numba.core.ssa: on stmt: $const658.2 = const(NoneType, None)
2025-08-27 14:20:27,381 [DEBUG] numba.core.ssa: on stmt: $658return_const.3 = cast(value=$const658.2)
2025-08-27 14:20:27,381 [DEBUG] numba.core.ssa: on stmt: return $658return_const.3
2025-08-27 14:20:27,381 [DEBUG] numba.core.ssa: Replaced assignments: defaultdict(<class 'list'>,
            {0: [<numba.core.ir.Assign object at 0x32ea04290>],
             174: [<numba.core.ir.Assign object at 0x32ea04950>],
             422: [<numba.core.ir.Assign object at 0x32ea05d90>]})
2025-08-27 14:20:27,381 [DEBUG] numba.core.ssa: ==== SSA block rewrite pass on 0
2025-08-27 14:20:27,381 [DEBUG] numba.core.ssa: Running <numba.core.ssa._FixSSAVars object at 0x32ea1c290>
2025-08-27 14:20:27,381 [DEBUG] numba.core.ssa: on stmt: x = arg(0, name=x)
2025-08-27 14:20:27,381 [DEBUG] numba.core.ssa: on stmt: t_out = arg(1, name=t_out)
2025-08-27 14:20:27,381 [DEBUG] numba.core.ssa: on stmt: interp_win = arg(2, name=interp_win)
2025-08-27 14:20:27,381 [DEBUG] numba.core.ssa: on stmt: interp_delta = arg(3, name=interp_delta)
2025-08-27 14:20:27,381 [DEBUG] numba.core.ssa: on stmt: num_table = arg(4, name=num_table)
2025-08-27 14:20:27,381 [DEBUG] numba.core.ssa: on stmt: scale = arg(5, name=scale)
2025-08-27 14:20:27,381 [DEBUG] numba.core.ssa: on stmt: y = arg(6, name=y)
2025-08-27 14:20:27,381 [DEBUG] numba.core.ssa: on stmt: $4load_global.0 = global(int: <class 'int'>)
2025-08-27 14:20:27,381 [DEBUG] numba.core.ssa: on stmt: $binop_mul16.4 = scale * num_table
2025-08-27 14:20:27,381 [DEBUG] numba.core.ssa: on stmt: index_step = call $4load_global.0($binop_mul16.4, func=$4load_global.0, args=[Var($binop_mul16.4, interpn.py:9)], kws=(), vararg=None, varkwarg=None, target=None)
2025-08-27 14:20:27,381 [DEBUG] numba.core.ssa: on stmt: time_register = const(float, 0.0)
2025-08-27 14:20:27,381 [DEBUG] numba.core.ssa: on stmt: n = const(int, 0)
2025-08-27 14:20:27,381 [DEBUG] numba.core.ssa: on stmt: frac = const(float, 0.0)
2025-08-27 14:20:27,381 [DEBUG] numba.core.ssa: on stmt: index_frac = const(float, 0.0)
2025-08-27 14:20:27,381 [DEBUG] numba.core.ssa: on stmt: offset = const(int, 0)
2025-08-27 14:20:27,381 [DEBUG] numba.core.ssa: on stmt: eta = const(float, 0.0)
2025-08-27 14:20:27,381 [DEBUG] numba.core.ssa: on stmt: weight = const(float, 0.0)
2025-08-27 14:20:27,381 [DEBUG] numba.core.ssa: on stmt: $60load_attr.14 = getattr(value=interp_win, attr=shape)
2025-08-27 14:20:27,381 [DEBUG] numba.core.ssa: on stmt: $const80.15.2 = const(int, 0)
2025-08-27 14:20:27,381 [DEBUG] numba.core.ssa: on stmt: nwin = static_getitem(value=$60load_attr.14, index=0, index_var=$const80.15.2, fn=<built-in function getitem>)
2025-08-27 14:20:27,381 [DEBUG] numba.core.ssa: on stmt: $90load_attr.18 = getattr(value=x, attr=shape)
2025-08-27 14:20:27,381 [DEBUG] numba.core.ssa: on stmt: $const110.19.2 = const(int, 0)
2025-08-27 14:20:27,381 [DEBUG] numba.core.ssa: on stmt: n_orig = static_getitem(value=$90load_attr.18, index=0, index_var=$const110.19.2, fn=<built-in function getitem>)
2025-08-27 14:20:27,381 [DEBUG] numba.core.ssa: on stmt: $120load_attr.22 = getattr(value=t_out, attr=shape)
2025-08-27 14:20:27,381 [DEBUG] numba.core.ssa: on stmt: $const140.23.2 = const(int, 0)
2025-08-27 14:20:27,381 [DEBUG] numba.core.ssa: on stmt: n_out = static_getitem(value=$120load_attr.22, index=0, index_var=$const140.23.2, fn=<built-in function getitem>)
2025-08-27 14:20:27,381 [DEBUG] numba.core.ssa: on stmt: $148load_global.25 = global(prange: <class 'numba.misc.special.prange'>)
2025-08-27 14:20:27,381 [DEBUG] numba.core.ssa: on stmt: $160call.28 = call $148load_global.25(n_out, func=$148load_global.25, args=[Var(n_out, interpn.py:21)], kws=(), vararg=None, varkwarg=None, target=None)
2025-08-27 14:20:27,381 [DEBUG] numba.core.ssa: on stmt: $168get_iter.29 = getiter(value=$160call.28)
2025-08-27 14:20:27,381 [DEBUG] numba.core.ssa: on stmt: $phi170.0 = $168get_iter.29
2025-08-27 14:20:27,381 [DEBUG] numba.core.ssa: on stmt: jump 170
2025-08-27 14:20:27,381 [DEBUG] numba.core.ssa: ==== SSA block rewrite pass on 170
2025-08-27 14:20:27,381 [DEBUG] numba.core.ssa: Running <numba.core.ssa._FixSSAVars object at 0x32ea1c290>
2025-08-27 14:20:27,381 [DEBUG] numba.core.ssa: on stmt: $170for_iter.1 = iternext(value=$phi170.0)
2025-08-27 14:20:27,381 [DEBUG] numba.core.ssa: on stmt: $170for_iter.2 = pair_first(value=$170for_iter.1)
2025-08-27 14:20:27,381 [DEBUG] numba.core.ssa: on stmt: $170for_iter.3 = pair_second(value=$170for_iter.1)
2025-08-27 14:20:27,381 [DEBUG] numba.core.ssa: on stmt: $phi174.1 = $170for_iter.2
2025-08-27 14:20:27,381 [DEBUG] numba.core.ssa: on stmt: branch $170for_iter.3, 174, 654
2025-08-27 14:20:27,381 [DEBUG] numba.core.ssa: ==== SSA block rewrite pass on 174
2025-08-27 14:20:27,381 [DEBUG] numba.core.ssa: Running <numba.core.ssa._FixSSAVars object at 0x32ea1c290>
2025-08-27 14:20:27,381 [DEBUG] numba.core.ssa: on stmt: t = $phi174.1
2025-08-27 14:20:27,381 [DEBUG] numba.core.ssa: on stmt: time_register.1 = getitem(value=t_out, index=t, fn=<built-in function getitem>)
2025-08-27 14:20:27,381 [DEBUG] numba.core.ssa: on stmt: $186load_global.5 = global(int: <class 'int'>)
2025-08-27 14:20:27,381 [DEBUG] numba.core.ssa: on stmt: n.1 = call $186load_global.5(time_register.1, func=$186load_global.5, args=[Var(time_register.1, interpn.py:24)], kws=(), vararg=None, varkwarg=None, target=None)
2025-08-27 14:20:27,381 [DEBUG] numba.core.ssa: on stmt: $binop_sub212.12 = time_register.1 - n.1
2025-08-27 14:20:27,381 [DEBUG] numba.core.ssa: on stmt: frac.1 = scale * $binop_sub212.12
2025-08-27 14:20:27,381 [DEBUG] numba.core.ssa: on stmt: index_frac.1 = frac.1 * num_table
2025-08-27 14:20:27,381 [DEBUG] numba.core.ssa: on stmt: $230load_global.17 = global(int: <class 'int'>)
2025-08-27 14:20:27,381 [DEBUG] numba.core.ssa: on stmt: offset.1 = call $230load_global.17(index_frac.1, func=$230load_global.17, args=[Var(index_frac.1, interpn.py:33)], kws=(), vararg=None, varkwarg=None, target=None)
2025-08-27 14:20:27,381 [DEBUG] numba.core.ssa: on stmt: eta.1 = index_frac.1 - offset.1
2025-08-27 14:20:27,382 [DEBUG] numba.core.ssa: on stmt: $260load_global.24 = global(min: <built-in function min>)
2025-08-27 14:20:27,382 [DEBUG] numba.core.ssa: on stmt: $const272.27.3 = const(int, 1)
2025-08-27 14:20:27,382 [DEBUG] numba.core.ssa: on stmt: $binop_add274.28 = n.1 + $const272.27.3
2025-08-27 14:20:27,382 [DEBUG] numba.core.ssa: on stmt: $binop_sub280.31 = nwin - offset.1
2025-08-27 14:20:27,382 [DEBUG] numba.core.ssa: on stmt: $binop_floordiv286.33 = $binop_sub280.31 // index_step
2025-08-27 14:20:27,382 [DEBUG] numba.core.ssa: on stmt: i_max = call $260load_global.24($binop_add274.28, $binop_floordiv286.33, func=$260load_global.24, args=[Var($binop_add274.28, interpn.py:40), Var($binop_floordiv286.33, interpn.py:40)], kws=(), vararg=None, varkwarg=None, target=None)
2025-08-27 14:20:27,382 [DEBUG] numba.core.ssa: on stmt: $300load_global.35 = global(range: <class 'range'>)
2025-08-27 14:20:27,382 [DEBUG] numba.core.ssa: on stmt: $312call.38 = call $300load_global.35(i_max, func=$300load_global.35, args=[Var(i_max, interpn.py:40)], kws=(), vararg=None, varkwarg=None, target=None)
2025-08-27 14:20:27,382 [DEBUG] numba.core.ssa: on stmt: $320get_iter.39 = getiter(value=$312call.38)
2025-08-27 14:20:27,382 [DEBUG] numba.core.ssa: on stmt: $phi322.1 = $320get_iter.39
2025-08-27 14:20:27,382 [DEBUG] numba.core.ssa: on stmt: jump 322
2025-08-27 14:20:27,382 [DEBUG] numba.core.ssa: ==== SSA block rewrite pass on 322
2025-08-27 14:20:27,382 [DEBUG] numba.core.ssa: Running <numba.core.ssa._FixSSAVars object at 0x32ea1c290>
2025-08-27 14:20:27,382 [DEBUG] numba.core.ssa: on stmt: $322for_iter.2 = iternext(value=$phi322.1)
2025-08-27 14:20:27,382 [DEBUG] numba.core.ssa: on stmt: $322for_iter.3 = pair_first(value=$322for_iter.2)
2025-08-27 14:20:27,382 [DEBUG] numba.core.ssa: on stmt: $322for_iter.4 = pair_second(value=$322for_iter.2)
2025-08-27 14:20:27,382 [DEBUG] numba.core.ssa: on stmt: $phi326.2 = $322for_iter.3
2025-08-27 14:20:27,382 [DEBUG] numba.core.ssa: on stmt: branch $322for_iter.4, 326, 422
2025-08-27 14:20:27,382 [DEBUG] numba.core.ssa: ==== SSA block rewrite pass on 326
2025-08-27 14:20:27,382 [DEBUG] numba.core.ssa: Running <numba.core.ssa._FixSSAVars object at 0x32ea1c290>
2025-08-27 14:20:27,382 [DEBUG] numba.core.ssa: on stmt: i = $phi326.2
2025-08-27 14:20:27,382 [DEBUG] numba.core.ssa: on stmt: $binop_mul334.7 = i * index_step
2025-08-27 14:20:27,382 [DEBUG] numba.core.ssa: on stmt: $binop_add338.8 = offset.1 + $binop_mul334.7
2025-08-27 14:20:27,382 [DEBUG] numba.core.ssa: on stmt: $342binary_subscr.9 = getitem(value=interp_win, index=$binop_add338.8, fn=<built-in function getitem>)
2025-08-27 14:20:27,382 [DEBUG] numba.core.ssa: on stmt: $binop_mul354.15 = i * index_step
2025-08-27 14:20:27,382 [DEBUG] numba.core.ssa: on stmt: $binop_add358.16 = offset.1 + $binop_mul354.15
2025-08-27 14:20:27,382 [DEBUG] numba.core.ssa: on stmt: $362binary_subscr.17 = getitem(value=interp_delta, index=$binop_add358.16, fn=<built-in function getitem>)
2025-08-27 14:20:27,382 [DEBUG] numba.core.ssa: on stmt: $binop_mul366.18 = eta * $362binary_subscr.17
2025-08-27 14:20:27,382 [DEBUG] numba.core.ssa: find_def var='eta' stmt=$binop_mul366.18 = eta * $362binary_subscr.17
2025-08-27 14:20:27,382 [DEBUG] numba.core.ssa: find_def_from_top label 326
2025-08-27 14:20:27,382 [DEBUG] numba.core.ssa: idom 322 from label 326
2025-08-27 14:20:27,382 [DEBUG] numba.core.ssa: find_def_from_bottom label 322
2025-08-27 14:20:27,382 [DEBUG] numba.core.ssa: find_def_from_top label 322
2025-08-27 14:20:27,382 [DEBUG] numba.core.ssa: idom 174 from label 322
2025-08-27 14:20:27,382 [DEBUG] numba.core.ssa: find_def_from_bottom label 174
2025-08-27 14:20:27,382 [DEBUG] numba.core.ssa: replaced with: $binop_mul366.18 = eta.1 * $362binary_subscr.17
2025-08-27 14:20:27,382 [DEBUG] numba.core.ssa: on stmt: weight = $342binary_subscr.9 + $binop_mul366.18
2025-08-27 14:20:27,382 [DEBUG] numba.core.ssa: on stmt: $384binary_subscr.22 = getitem(value=y, index=t, fn=<built-in function getitem>)
2025-08-27 14:20:27,382 [DEBUG] numba.core.ssa: on stmt: $binop_sub394.27 = n.1 - i
2025-08-27 14:20:27,382 [DEBUG] numba.core.ssa: on stmt: $398binary_subscr.28 = getitem(value=x, index=$binop_sub394.27, fn=<built-in function getitem>)
2025-08-27 14:20:27,382 [DEBUG] numba.core.ssa: on stmt: $binop_mul402.29 = weight * $398binary_subscr.28
2025-08-27 14:20:27,382 [DEBUG] numba.core.ssa: on stmt: $binop_iadd406.30 = inplace_binop(fn=<built-in function iadd>, immutable_fn=<built-in function add>, lhs=$384binary_subscr.22, rhs=$binop_mul402.29, static_lhs=Undefined, static_rhs=Undefined)
2025-08-27 14:20:27,382 [DEBUG] numba.core.ssa: on stmt: y[t] = $binop_iadd406.30
2025-08-27 14:20:27,382 [DEBUG] numba.core.ssa: on stmt: jump 322
2025-08-27 14:20:27,384 [DEBUG] numba.core.ssa: ==== SSA block rewrite pass on 422
2025-08-27 14:20:27,384 [DEBUG] numba.core.ssa: Running <numba.core.ssa._FixSSAVars object at 0x32ea1c290>
2025-08-27 14:20:27,384 [DEBUG] numba.core.ssa: on stmt: $binop_sub428.5 = scale - frac.1
2025-08-27 14:20:27,384 [DEBUG] numba.core.ssa: on stmt: frac.2 = $binop_sub428.5
2025-08-27 14:20:27,384 [DEBUG] numba.core.ssa: on stmt: index_frac.2 = frac.2 * num_table
2025-08-27 14:20:27,384 [DEBUG] numba.core.ssa: on stmt: $442load_global.9 = global(int: <class 'int'>)
2025-08-27 14:20:27,384 [DEBUG] numba.core.ssa: on stmt: offset.2 = call $442load_global.9(index_frac.2, func=$442load_global.9, args=[Var(index_frac.2, interpn.py:53)], kws=(), vararg=None, varkwarg=None, target=None)
2025-08-27 14:20:27,384 [DEBUG] numba.core.ssa: on stmt: eta.2 = index_frac.2 - offset.2
2025-08-27 14:20:27,384 [DEBUG] numba.core.ssa: on stmt: $472load_global.16 = global(min: <built-in function min>)
2025-08-27 14:20:27,384 [DEBUG] numba.core.ssa: on stmt: $binop_sub486.20 = n_orig - n.1
2025-08-27 14:20:27,384 [DEBUG] numba.core.ssa: on stmt: $const490.21.3 = const(int, 1)
2025-08-27 14:20:27,384 [DEBUG] numba.core.ssa: on stmt: $binop_sub492.22 = $binop_sub486.20 - $const490.21.3
2025-08-27 14:20:27,384 [DEBUG] numba.core.ssa: on stmt: $binop_sub498.25 = nwin - offset.2
2025-08-27 14:20:27,384 [DEBUG] numba.core.ssa: on stmt: $binop_floordiv504.27 = $binop_sub498.25 // index_step
2025-08-27 14:20:27,384 [DEBUG] numba.core.ssa: on stmt: k_max = call $472load_global.16($binop_sub492.22, $binop_floordiv504.27, func=$472load_global.16, args=[Var($binop_sub492.22, interpn.py:60), Var($binop_floordiv504.27, interpn.py:60)], kws=(), vararg=None, varkwarg=None, target=None)
2025-08-27 14:20:27,384 [DEBUG] numba.core.ssa: on stmt: $518load_global.29 = global(range: <class 'range'>)
2025-08-27 14:20:27,384 [DEBUG] numba.core.ssa: on stmt: $530call.32 = call $518load_global.29(k_max, func=$518load_global.29, args=[Var(k_max, interpn.py:60)], kws=(), vararg=None, varkwarg=None, target=None)
2025-08-27 14:20:27,384 [DEBUG] numba.core.ssa: on stmt: $538get_iter.33 = getiter(value=$530call.32)
2025-08-27 14:20:27,384 [DEBUG] numba.core.ssa: on stmt: $phi540.1 = $538get_iter.33
2025-08-27 14:20:27,384 [DEBUG] numba.core.ssa: on stmt: jump 540
2025-08-27 14:20:27,384 [DEBUG] numba.core.ssa: ==== SSA block rewrite pass on 540
2025-08-27 14:20:27,384 [DEBUG] numba.core.ssa: Running <numba.core.ssa._FixSSAVars object at 0x32ea1c290>
2025-08-27 14:20:27,384 [DEBUG] numba.core.ssa: on stmt: $540for_iter.2 = iternext(value=$phi540.1)
2025-08-27 14:20:27,384 [DEBUG] numba.core.ssa: on stmt: $540for_iter.3 = pair_first(value=$540for_iter.2)
2025-08-27 14:20:27,384 [DEBUG] numba.core.ssa: on stmt: $540for_iter.4 = pair_second(value=$540for_iter.2)
2025-08-27 14:20:27,384 [DEBUG] numba.core.ssa: on stmt: $phi544.2 = $540for_iter.3
2025-08-27 14:20:27,384 [DEBUG] numba.core.ssa: on stmt: branch $540for_iter.4, 544, 646
2025-08-27 14:20:27,384 [DEBUG] numba.core.ssa: ==== SSA block rewrite pass on 544
2025-08-27 14:20:27,384 [DEBUG] numba.core.ssa: Running <numba.core.ssa._FixSSAVars object at 0x32ea1c290>
2025-08-27 14:20:27,384 [DEBUG] numba.core.ssa: on stmt: k = $phi544.2
2025-08-27 14:20:27,384 [DEBUG] numba.core.ssa: on stmt: $binop_mul552.7 = k * index_step
2025-08-27 14:20:27,384 [DEBUG] numba.core.ssa: on stmt: $binop_add556.8 = offset.2 + $binop_mul552.7
2025-08-27 14:20:27,384 [DEBUG] numba.core.ssa: on stmt: $560binary_subscr.9 = getitem(value=interp_win, index=$binop_add556.8, fn=<built-in function getitem>)
2025-08-27 14:20:27,384 [DEBUG] numba.core.ssa: on stmt: $binop_mul572.15 = k * index_step
2025-08-27 14:20:27,384 [DEBUG] numba.core.ssa: on stmt: $binop_add576.16 = offset.2 + $binop_mul572.15
2025-08-27 14:20:27,384 [DEBUG] numba.core.ssa: on stmt: $580binary_subscr.17 = getitem(value=interp_delta, index=$binop_add576.16, fn=<built-in function getitem>)
2025-08-27 14:20:27,384 [DEBUG] numba.core.ssa: on stmt: $binop_mul584.18 = eta * $580binary_subscr.17
2025-08-27 14:20:27,384 [DEBUG] numba.core.ssa: find_def var='eta' stmt=$binop_mul584.18 = eta * $580binary_subscr.17
2025-08-27 14:20:27,384 [DEBUG] numba.core.ssa: find_def_from_top label 544
2025-08-27 14:20:27,384 [DEBUG] numba.core.ssa: idom 540 from label 544
2025-08-27 14:20:27,384 [DEBUG] numba.core.ssa: find_def_from_bottom label 540
2025-08-27 14:20:27,384 [DEBUG] numba.core.ssa: find_def_from_top label 540
2025-08-27 14:20:27,384 [DEBUG] numba.core.ssa: idom 422 from label 540
2025-08-27 14:20:27,384 [DEBUG] numba.core.ssa: find_def_from_bottom label 422
2025-08-27 14:20:27,384 [DEBUG] numba.core.ssa: replaced with: $binop_mul584.18 = eta.2 * $580binary_subscr.17
2025-08-27 14:20:27,384 [DEBUG] numba.core.ssa: on stmt: weight = $560binary_subscr.9 + $binop_mul584.18
2025-08-27 14:20:27,384 [DEBUG] numba.core.ssa: on stmt: $602binary_subscr.22 = getitem(value=y, index=t, fn=<built-in function getitem>)
2025-08-27 14:20:27,384 [DEBUG] numba.core.ssa: on stmt: $binop_add612.27 = n.1 + k
2025-08-27 14:20:27,384 [DEBUG] numba.core.ssa: on stmt: $const616.28.3 = const(int, 1)
2025-08-27 14:20:27,384 [DEBUG] numba.core.ssa: on stmt: $binop_add618.29 = $binop_add612.27 + $const616.28.3
2025-08-27 14:20:27,384 [DEBUG] numba.core.ssa: on stmt: $622binary_subscr.30 = getitem(value=x, index=$binop_add618.29, fn=<built-in function getitem>)
2025-08-27 14:20:27,384 [DEBUG] numba.core.ssa: on stmt: $binop_mul626.31 = weight * $622binary_subscr.30
2025-08-27 14:20:27,385 [DEBUG] numba.core.ssa: on stmt: $binop_iadd630.32 = inplace_binop(fn=<built-in function iadd>, immutable_fn=<built-in function add>, lhs=$602binary_subscr.22, rhs=$binop_mul626.31, static_lhs=Undefined, static_rhs=Undefined)
2025-08-27 14:20:27,385 [DEBUG] numba.core.ssa: on stmt: y[t] = $binop_iadd630.32
2025-08-27 14:20:27,385 [DEBUG] numba.core.ssa: on stmt: jump 540
2025-08-27 14:20:27,385 [DEBUG] numba.core.ssa: ==== SSA block rewrite pass on 646
2025-08-27 14:20:27,385 [DEBUG] numba.core.ssa: Running <numba.core.ssa._FixSSAVars object at 0x32ea1c290>
2025-08-27 14:20:27,385 [DEBUG] numba.core.ssa: on stmt: jump 170
2025-08-27 14:20:27,385 [DEBUG] numba.core.ssa: ==== SSA block rewrite pass on 654
2025-08-27 14:20:27,385 [DEBUG] numba.core.ssa: Running <numba.core.ssa._FixSSAVars object at 0x32ea1c290>
2025-08-27 14:20:27,385 [DEBUG] numba.core.ssa: on stmt: $const658.2 = const(NoneType, None)
2025-08-27 14:20:27,385 [DEBUG] numba.core.ssa: on stmt: $658return_const.3 = cast(value=$const658.2)
2025-08-27 14:20:27,385 [DEBUG] numba.core.ssa: on stmt: return $658return_const.3
2025-08-27 14:20:27,385 [DEBUG] numba.core.ssa: Fix SSA violator on var weight
2025-08-27 14:20:27,385 [DEBUG] numba.core.ssa: ==== SSA block rewrite pass on 0
2025-08-27 14:20:27,385 [DEBUG] numba.core.ssa: Running <numba.core.ssa._FreshVarHandler object at 0x32e5da250>
2025-08-27 14:20:27,385 [DEBUG] numba.core.ssa: on stmt: x = arg(0, name=x)
2025-08-27 14:20:27,385 [DEBUG] numba.core.ssa: on stmt: t_out = arg(1, name=t_out)
2025-08-27 14:20:27,385 [DEBUG] numba.core.ssa: on stmt: interp_win = arg(2, name=interp_win)
2025-08-27 14:20:27,385 [DEBUG] numba.core.ssa: on stmt: interp_delta = arg(3, name=interp_delta)
2025-08-27 14:20:27,385 [DEBUG] numba.core.ssa: on stmt: num_table = arg(4, name=num_table)
2025-08-27 14:20:27,385 [DEBUG] numba.core.ssa: on stmt: scale = arg(5, name=scale)
2025-08-27 14:20:27,385 [DEBUG] numba.core.ssa: on stmt: y = arg(6, name=y)
2025-08-27 14:20:27,385 [DEBUG] numba.core.ssa: on stmt: $4load_global.0 = global(int: <class 'int'>)
2025-08-27 14:20:27,385 [DEBUG] numba.core.ssa: on stmt: $binop_mul16.4 = scale * num_table
2025-08-27 14:20:27,385 [DEBUG] numba.core.ssa: on stmt: index_step = call $4load_global.0($binop_mul16.4, func=$4load_global.0, args=[Var($binop_mul16.4, interpn.py:9)], kws=(), vararg=None, varkwarg=None, target=None)
2025-08-27 14:20:27,385 [DEBUG] numba.core.ssa: on stmt: time_register = const(float, 0.0)
2025-08-27 14:20:27,385 [DEBUG] numba.core.ssa: on stmt: n = const(int, 0)
2025-08-27 14:20:27,385 [DEBUG] numba.core.ssa: on stmt: frac = const(float, 0.0)
2025-08-27 14:20:27,385 [DEBUG] numba.core.ssa: on stmt: index_frac = const(float, 0.0)
2025-08-27 14:20:27,385 [DEBUG] numba.core.ssa: on stmt: offset = const(int, 0)
2025-08-27 14:20:27,385 [DEBUG] numba.core.ssa: on stmt: eta = const(float, 0.0)
2025-08-27 14:20:27,385 [DEBUG] numba.core.ssa: on stmt: weight = const(float, 0.0)
2025-08-27 14:20:27,385 [DEBUG] numba.core.ssa: first assign: weight
2025-08-27 14:20:27,385 [DEBUG] numba.core.ssa: replaced with: weight = const(float, 0.0)
2025-08-27 14:20:27,385 [DEBUG] numba.core.ssa: on stmt: $60load_attr.14 = getattr(value=interp_win, attr=shape)
2025-08-27 14:20:27,385 [DEBUG] numba.core.ssa: on stmt: $const80.15.2 = const(int, 0)
2025-08-27 14:20:27,385 [DEBUG] numba.core.ssa: on stmt: nwin = static_getitem(value=$60load_attr.14, index=0, index_var=$const80.15.2, fn=<built-in function getitem>)
2025-08-27 14:20:27,385 [DEBUG] numba.core.ssa: on stmt: $90load_attr.18 = getattr(value=x, attr=shape)
2025-08-27 14:20:27,385 [DEBUG] numba.core.ssa: on stmt: $const110.19.2 = const(int, 0)
2025-08-27 14:20:27,385 [DEBUG] numba.core.ssa: on stmt: n_orig = static_getitem(value=$90load_attr.18, index=0, index_var=$const110.19.2, fn=<built-in function getitem>)
2025-08-27 14:20:27,385 [DEBUG] numba.core.ssa: on stmt: $120load_attr.22 = getattr(value=t_out, attr=shape)
2025-08-27 14:20:27,385 [DEBUG] numba.core.ssa: on stmt: $const140.23.2 = const(int, 0)
2025-08-27 14:20:27,385 [DEBUG] numba.core.ssa: on stmt: n_out = static_getitem(value=$120load_attr.22, index=0, index_var=$const140.23.2, fn=<built-in function getitem>)
2025-08-27 14:20:27,385 [DEBUG] numba.core.ssa: on stmt: $148load_global.25 = global(prange: <class 'numba.misc.special.prange'>)
2025-08-27 14:20:27,385 [DEBUG] numba.core.ssa: on stmt: $160call.28 = call $148load_global.25(n_out, func=$148load_global.25, args=[Var(n_out, interpn.py:21)], kws=(), vararg=None, varkwarg=None, target=None)
2025-08-27 14:20:27,385 [DEBUG] numba.core.ssa: on stmt: $168get_iter.29 = getiter(value=$160call.28)
2025-08-27 14:20:27,385 [DEBUG] numba.core.ssa: on stmt: $phi170.0 = $168get_iter.29
2025-08-27 14:20:27,385 [DEBUG] numba.core.ssa: on stmt: jump 170
2025-08-27 14:20:27,385 [DEBUG] numba.core.ssa: ==== SSA block rewrite pass on 170
2025-08-27 14:20:27,385 [DEBUG] numba.core.ssa: Running <numba.core.ssa._FreshVarHandler object at 0x32e5da250>
2025-08-27 14:20:27,385 [DEBUG] numba.core.ssa: on stmt: $170for_iter.1 = iternext(value=$phi170.0)
2025-08-27 14:20:27,385 [DEBUG] numba.core.ssa: on stmt: $170for_iter.2 = pair_first(value=$170for_iter.1)
2025-08-27 14:20:27,385 [DEBUG] numba.core.ssa: on stmt: $170for_iter.3 = pair_second(value=$170for_iter.1)
2025-08-27 14:20:27,385 [DEBUG] numba.core.ssa: on stmt: $phi174.1 = $170for_iter.2
2025-08-27 14:20:27,385 [DEBUG] numba.core.ssa: on stmt: branch $170for_iter.3, 174, 654
2025-08-27 14:20:27,385 [DEBUG] numba.core.ssa: ==== SSA block rewrite pass on 174
2025-08-27 14:20:27,385 [DEBUG] numba.core.ssa: Running <numba.core.ssa._FreshVarHandler object at 0x32e5da250>
2025-08-27 14:20:27,385 [DEBUG] numba.core.ssa: on stmt: t = $phi174.1
2025-08-27 14:20:27,385 [DEBUG] numba.core.ssa: on stmt: time_register.1 = getitem(value=t_out, index=t, fn=<built-in function getitem>)
2025-08-27 14:20:27,385 [DEBUG] numba.core.ssa: on stmt: $186load_global.5 = global(int: <class 'int'>)
2025-08-27 14:20:27,385 [DEBUG] numba.core.ssa: on stmt: n.1 = call $186load_global.5(time_register.1, func=$186load_global.5, args=[Var(time_register.1, interpn.py:24)], kws=(), vararg=None, varkwarg=None, target=None)
2025-08-27 14:20:27,385 [DEBUG] numba.core.ssa: on stmt: $binop_sub212.12 = time_register.1 - n.1
2025-08-27 14:20:27,385 [DEBUG] numba.core.ssa: on stmt: frac.1 = scale * $binop_sub212.12
2025-08-27 14:20:27,385 [DEBUG] numba.core.ssa: on stmt: index_frac.1 = frac.1 * num_table
2025-08-27 14:20:27,385 [DEBUG] numba.core.ssa: on stmt: $230load_global.17 = global(int: <class 'int'>)
2025-08-27 14:20:27,385 [DEBUG] numba.core.ssa: on stmt: offset.1 = call $230load_global.17(index_frac.1, func=$230load_global.17, args=[Var(index_frac.1, interpn.py:33)], kws=(), vararg=None, varkwarg=None, target=None)
2025-08-27 14:20:27,385 [DEBUG] numba.core.ssa: on stmt: eta.1 = index_frac.1 - offset.1
2025-08-27 14:20:27,385 [DEBUG] numba.core.ssa: on stmt: $260load_global.24 = global(min: <built-in function min>)
2025-08-27 14:20:27,385 [DEBUG] numba.core.ssa: on stmt: $const272.27.3 = const(int, 1)
2025-08-27 14:20:27,385 [DEBUG] numba.core.ssa: on stmt: $binop_add274.28 = n.1 + $const272.27.3
2025-08-27 14:20:27,385 [DEBUG] numba.core.ssa: on stmt: $binop_sub280.31 = nwin - offset.1
2025-08-27 14:20:27,385 [DEBUG] numba.core.ssa: on stmt: $binop_floordiv286.33 = $binop_sub280.31 // index_step
2025-08-27 14:20:27,385 [DEBUG] numba.core.ssa: on stmt: i_max = call $260load_global.24($binop_add274.28, $binop_floordiv286.33, func=$260load_global.24, args=[Var($binop_add274.28, interpn.py:40), Var($binop_floordiv286.33, interpn.py:40)], kws=(), vararg=None, varkwarg=None, target=None)
2025-08-27 14:20:27,385 [DEBUG] numba.core.ssa: on stmt: $300load_global.35 = global(range: <class 'range'>)
2025-08-27 14:20:27,385 [DEBUG] numba.core.ssa: on stmt: $312call.38 = call $300load_global.35(i_max, func=$300load_global.35, args=[Var(i_max, interpn.py:40)], kws=(), vararg=None, varkwarg=None, target=None)
2025-08-27 14:20:27,386 [DEBUG] numba.core.ssa: on stmt: $320get_iter.39 = getiter(value=$312call.38)
2025-08-27 14:20:27,386 [DEBUG] numba.core.ssa: on stmt: $phi322.1 = $320get_iter.39
2025-08-27 14:20:27,386 [DEBUG] numba.core.ssa: on stmt: jump 322
2025-08-27 14:20:27,386 [DEBUG] numba.core.ssa: ==== SSA block rewrite pass on 322
2025-08-27 14:20:27,386 [DEBUG] numba.core.ssa: Running <numba.core.ssa._FreshVarHandler object at 0x32e5da250>
2025-08-27 14:20:27,386 [DEBUG] numba.core.ssa: on stmt: $322for_iter.2 = iternext(value=$phi322.1)
2025-08-27 14:20:27,386 [DEBUG] numba.core.ssa: on stmt: $322for_iter.3 = pair_first(value=$322for_iter.2)
2025-08-27 14:20:27,386 [DEBUG] numba.core.ssa: on stmt: $322for_iter.4 = pair_second(value=$322for_iter.2)
2025-08-27 14:20:27,386 [DEBUG] numba.core.ssa: on stmt: $phi326.2 = $322for_iter.3
2025-08-27 14:20:27,386 [DEBUG] numba.core.ssa: on stmt: branch $322for_iter.4, 326, 422
2025-08-27 14:20:27,386 [DEBUG] numba.core.ssa: ==== SSA block rewrite pass on 326
2025-08-27 14:20:27,386 [DEBUG] numba.core.ssa: Running <numba.core.ssa._FreshVarHandler object at 0x32e5da250>
2025-08-27 14:20:27,386 [DEBUG] numba.core.ssa: on stmt: i = $phi326.2
2025-08-27 14:20:27,386 [DEBUG] numba.core.ssa: on stmt: $binop_mul334.7 = i * index_step
2025-08-27 14:20:27,386 [DEBUG] numba.core.ssa: on stmt: $binop_add338.8 = offset.1 + $binop_mul334.7
2025-08-27 14:20:27,386 [DEBUG] numba.core.ssa: on stmt: $342binary_subscr.9 = getitem(value=interp_win, index=$binop_add338.8, fn=<built-in function getitem>)
2025-08-27 14:20:27,386 [DEBUG] numba.core.ssa: on stmt: $binop_mul354.15 = i * index_step
2025-08-27 14:20:27,386 [DEBUG] numba.core.ssa: on stmt: $binop_add358.16 = offset.1 + $binop_mul354.15
2025-08-27 14:20:27,386 [DEBUG] numba.core.ssa: on stmt: $362binary_subscr.17 = getitem(value=interp_delta, index=$binop_add358.16, fn=<built-in function getitem>)
2025-08-27 14:20:27,386 [DEBUG] numba.core.ssa: on stmt: $binop_mul366.18 = eta.1 * $362binary_subscr.17
2025-08-27 14:20:27,386 [DEBUG] numba.core.ssa: on stmt: weight = $342binary_subscr.9 + $binop_mul366.18
2025-08-27 14:20:27,386 [DEBUG] numba.core.ssa: replaced with: weight.1 = $342binary_subscr.9 + $binop_mul366.18
2025-08-27 14:20:27,386 [DEBUG] numba.core.ssa: on stmt: $384binary_subscr.22 = getitem(value=y, index=t, fn=<built-in function getitem>)
2025-08-27 14:20:27,386 [DEBUG] numba.core.ssa: on stmt: $binop_sub394.27 = n.1 - i
2025-08-27 14:20:27,386 [DEBUG] numba.core.ssa: on stmt: $398binary_subscr.28 = getitem(value=x, index=$binop_sub394.27, fn=<built-in function getitem>)
2025-08-27 14:20:27,386 [DEBUG] numba.core.ssa: on stmt: $binop_mul402.29 = weight * $398binary_subscr.28
2025-08-27 14:20:27,386 [DEBUG] numba.core.ssa: on stmt: $binop_iadd406.30 = inplace_binop(fn=<built-in function iadd>, immutable_fn=<built-in function add>, lhs=$384binary_subscr.22, rhs=$binop_mul402.29, static_lhs=Undefined, static_rhs=Undefined)
2025-08-27 14:20:27,386 [DEBUG] numba.core.ssa: on stmt: y[t] = $binop_iadd406.30
2025-08-27 14:20:27,386 [DEBUG] numba.core.ssa: on stmt: jump 322
2025-08-27 14:20:27,386 [DEBUG] numba.core.ssa: ==== SSA block rewrite pass on 422
2025-08-27 14:20:27,386 [DEBUG] numba.core.ssa: Running <numba.core.ssa._FreshVarHandler object at 0x32e5da250>
2025-08-27 14:20:27,386 [DEBUG] numba.core.ssa: on stmt: $binop_sub428.5 = scale - frac.1
2025-08-27 14:20:27,386 [DEBUG] numba.core.ssa: on stmt: frac.2 = $binop_sub428.5
2025-08-27 14:20:27,386 [DEBUG] numba.core.ssa: on stmt: index_frac.2 = frac.2 * num_table
2025-08-27 14:20:27,386 [DEBUG] numba.core.ssa: on stmt: $442load_global.9 = global(int: <class 'int'>)
2025-08-27 14:20:27,386 [DEBUG] numba.core.ssa: on stmt: offset.2 = call $442load_global.9(index_frac.2, func=$442load_global.9, args=[Var(index_frac.2, interpn.py:53)], kws=(), vararg=None, varkwarg=None, target=None)
2025-08-27 14:20:27,386 [DEBUG] numba.core.ssa: on stmt: eta.2 = index_frac.2 - offset.2
2025-08-27 14:20:27,386 [DEBUG] numba.core.ssa: on stmt: $472load_global.16 = global(min: <built-in function min>)
2025-08-27 14:20:27,386 [DEBUG] numba.core.ssa: on stmt: $binop_sub486.20 = n_orig - n.1
2025-08-27 14:20:27,386 [DEBUG] numba.core.ssa: on stmt: $const490.21.3 = const(int, 1)
2025-08-27 14:20:27,386 [DEBUG] numba.core.ssa: on stmt: $binop_sub492.22 = $binop_sub486.20 - $const490.21.3
2025-08-27 14:20:27,386 [DEBUG] numba.core.ssa: on stmt: $binop_sub498.25 = nwin - offset.2
2025-08-27 14:20:27,386 [DEBUG] numba.core.ssa: on stmt: $binop_floordiv504.27 = $binop_sub498.25 // index_step
2025-08-27 14:20:27,386 [DEBUG] numba.core.ssa: on stmt: k_max = call $472load_global.16($binop_sub492.22, $binop_floordiv504.27, func=$472load_global.16, args=[Var($binop_sub492.22, interpn.py:60), Var($binop_floordiv504.27, interpn.py:60)], kws=(), vararg=None, varkwarg=None, target=None)
2025-08-27 14:20:27,386 [DEBUG] numba.core.ssa: on stmt: $518load_global.29 = global(range: <class 'range'>)
2025-08-27 14:20:27,386 [DEBUG] numba.core.ssa: on stmt: $530call.32 = call $518load_global.29(k_max, func=$518load_global.29, args=[Var(k_max, interpn.py:60)], kws=(), vararg=None, varkwarg=None, target=None)
2025-08-27 14:20:27,386 [DEBUG] numba.core.ssa: on stmt: $538get_iter.33 = getiter(value=$530call.32)
2025-08-27 14:20:27,386 [DEBUG] numba.core.ssa: on stmt: $phi540.1 = $538get_iter.33
2025-08-27 14:20:27,386 [DEBUG] numba.core.ssa: on stmt: jump 540
2025-08-27 14:20:27,386 [DEBUG] numba.core.ssa: ==== SSA block rewrite pass on 540
2025-08-27 14:20:27,386 [DEBUG] numba.core.ssa: Running <numba.core.ssa._FreshVarHandler object at 0x32e5da250>
2025-08-27 14:20:27,386 [DEBUG] numba.core.ssa: on stmt: $540for_iter.2 = iternext(value=$phi540.1)
2025-08-27 14:20:27,386 [DEBUG] numba.core.ssa: on stmt: $540for_iter.3 = pair_first(value=$540for_iter.2)
2025-08-27 14:20:27,386 [DEBUG] numba.core.ssa: on stmt: $540for_iter.4 = pair_second(value=$540for_iter.2)
2025-08-27 14:20:27,386 [DEBUG] numba.core.ssa: on stmt: $phi544.2 = $540for_iter.3
2025-08-27 14:20:27,386 [DEBUG] numba.core.ssa: on stmt: branch $540for_iter.4, 544, 646
2025-08-27 14:20:27,386 [DEBUG] numba.core.ssa: ==== SSA block rewrite pass on 544
2025-08-27 14:20:27,386 [DEBUG] numba.core.ssa: Running <numba.core.ssa._FreshVarHandler object at 0x32e5da250>
2025-08-27 14:20:27,386 [DEBUG] numba.core.ssa: on stmt: k = $phi544.2
2025-08-27 14:20:27,386 [DEBUG] numba.core.ssa: on stmt: $binop_mul552.7 = k * index_step
2025-08-27 14:20:27,386 [DEBUG] numba.core.ssa: on stmt: $binop_add556.8 = offset.2 + $binop_mul552.7
2025-08-27 14:20:27,386 [DEBUG] numba.core.ssa: on stmt: $560binary_subscr.9 = getitem(value=interp_win, index=$binop_add556.8, fn=<built-in function getitem>)
2025-08-27 14:20:27,386 [DEBUG] numba.core.ssa: on stmt: $binop_mul572.15 = k * index_step
2025-08-27 14:20:27,386 [DEBUG] numba.core.ssa: on stmt: $binop_add576.16 = offset.2 + $binop_mul572.15
2025-08-27 14:20:27,386 [DEBUG] numba.core.ssa: on stmt: $580binary_subscr.17 = getitem(value=interp_delta, index=$binop_add576.16, fn=<built-in function getitem>)
2025-08-27 14:20:27,386 [DEBUG] numba.core.ssa: on stmt: $binop_mul584.18 = eta.2 * $580binary_subscr.17
2025-08-27 14:20:27,386 [DEBUG] numba.core.ssa: on stmt: weight = $560binary_subscr.9 + $binop_mul584.18
2025-08-27 14:20:27,386 [DEBUG] numba.core.ssa: replaced with: weight.2 = $560binary_subscr.9 + $binop_mul584.18
2025-08-27 14:20:27,386 [DEBUG] numba.core.ssa: on stmt: $602binary_subscr.22 = getitem(value=y, index=t, fn=<built-in function getitem>)
2025-08-27 14:20:27,386 [DEBUG] numba.core.ssa: on stmt: $binop_add612.27 = n.1 + k
2025-08-27 14:20:27,386 [DEBUG] numba.core.ssa: on stmt: $const616.28.3 = const(int, 1)
2025-08-27 14:20:27,386 [DEBUG] numba.core.ssa: on stmt: $binop_add618.29 = $binop_add612.27 + $const616.28.3
2025-08-27 14:20:27,387 [DEBUG] numba.core.ssa: on stmt: $622binary_subscr.30 = getitem(value=x, index=$binop_add618.29, fn=<built-in function getitem>)
2025-08-27 14:20:27,387 [DEBUG] numba.core.ssa: on stmt: $binop_mul626.31 = weight * $622binary_subscr.30
2025-08-27 14:20:27,387 [DEBUG] numba.core.ssa: on stmt: $binop_iadd630.32 = inplace_binop(fn=<built-in function iadd>, immutable_fn=<built-in function add>, lhs=$602binary_subscr.22, rhs=$binop_mul626.31, static_lhs=Undefined, static_rhs=Undefined)
2025-08-27 14:20:27,387 [DEBUG] numba.core.ssa: on stmt: y[t] = $binop_iadd630.32
2025-08-27 14:20:27,387 [DEBUG] numba.core.ssa: on stmt: jump 540
2025-08-27 14:20:27,387 [DEBUG] numba.core.ssa: ==== SSA block rewrite pass on 646
2025-08-27 14:20:27,387 [DEBUG] numba.core.ssa: Running <numba.core.ssa._FreshVarHandler object at 0x32e5da250>
2025-08-27 14:20:27,387 [DEBUG] numba.core.ssa: on stmt: jump 170
2025-08-27 14:20:27,387 [DEBUG] numba.core.ssa: ==== SSA block rewrite pass on 654
2025-08-27 14:20:27,387 [DEBUG] numba.core.ssa: Running <numba.core.ssa._FreshVarHandler object at 0x32e5da250>
2025-08-27 14:20:27,387 [DEBUG] numba.core.ssa: on stmt: $const658.2 = const(NoneType, None)
2025-08-27 14:20:27,387 [DEBUG] numba.core.ssa: on stmt: $658return_const.3 = cast(value=$const658.2)
2025-08-27 14:20:27,387 [DEBUG] numba.core.ssa: on stmt: return $658return_const.3
2025-08-27 14:20:27,387 [DEBUG] numba.core.ssa: Replaced assignments: defaultdict(<class 'list'>,
            {0: [<numba.core.ir.Assign object at 0x32ea06810>],
             326: [<numba.core.ir.Assign object at 0x32ea045f0>],
             544: [<numba.core.ir.Assign object at 0x32ea06b10>]})
2025-08-27 14:20:27,387 [DEBUG] numba.core.ssa: ==== SSA block rewrite pass on 0
2025-08-27 14:20:27,387 [DEBUG] numba.core.ssa: Running <numba.core.ssa._FixSSAVars object at 0x32e5da250>
2025-08-27 14:20:27,387 [DEBUG] numba.core.ssa: on stmt: x = arg(0, name=x)
2025-08-27 14:20:27,387 [DEBUG] numba.core.ssa: on stmt: t_out = arg(1, name=t_out)
2025-08-27 14:20:27,387 [DEBUG] numba.core.ssa: on stmt: interp_win = arg(2, name=interp_win)
2025-08-27 14:20:27,387 [DEBUG] numba.core.ssa: on stmt: interp_delta = arg(3, name=interp_delta)
2025-08-27 14:20:27,387 [DEBUG] numba.core.ssa: on stmt: num_table = arg(4, name=num_table)
2025-08-27 14:20:27,387 [DEBUG] numba.core.ssa: on stmt: scale = arg(5, name=scale)
2025-08-27 14:20:27,387 [DEBUG] numba.core.ssa: on stmt: y = arg(6, name=y)
2025-08-27 14:20:27,387 [DEBUG] numba.core.ssa: on stmt: $4load_global.0 = global(int: <class 'int'>)
2025-08-27 14:20:27,387 [DEBUG] numba.core.ssa: on stmt: $binop_mul16.4 = scale * num_table
2025-08-27 14:20:27,387 [DEBUG] numba.core.ssa: on stmt: index_step = call $4load_global.0($binop_mul16.4, func=$4load_global.0, args=[Var($binop_mul16.4, interpn.py:9)], kws=(), vararg=None, varkwarg=None, target=None)
2025-08-27 14:20:27,387 [DEBUG] numba.core.ssa: on stmt: time_register = const(float, 0.0)
2025-08-27 14:20:27,387 [DEBUG] numba.core.ssa: on stmt: n = const(int, 0)
2025-08-27 14:20:27,387 [DEBUG] numba.core.ssa: on stmt: frac = const(float, 0.0)
2025-08-27 14:20:27,387 [DEBUG] numba.core.ssa: on stmt: index_frac = const(float, 0.0)
2025-08-27 14:20:27,387 [DEBUG] numba.core.ssa: on stmt: offset = const(int, 0)
2025-08-27 14:20:27,387 [DEBUG] numba.core.ssa: on stmt: eta = const(float, 0.0)
2025-08-27 14:20:27,387 [DEBUG] numba.core.ssa: on stmt: weight = const(float, 0.0)
2025-08-27 14:20:27,387 [DEBUG] numba.core.ssa: on stmt: $60load_attr.14 = getattr(value=interp_win, attr=shape)
2025-08-27 14:20:27,387 [DEBUG] numba.core.ssa: on stmt: $const80.15.2 = const(int, 0)
2025-08-27 14:20:27,387 [DEBUG] numba.core.ssa: on stmt: nwin = static_getitem(value=$60load_attr.14, index=0, index_var=$const80.15.2, fn=<built-in function getitem>)
2025-08-27 14:20:27,387 [DEBUG] numba.core.ssa: on stmt: $90load_attr.18 = getattr(value=x, attr=shape)
2025-08-27 14:20:27,387 [DEBUG] numba.core.ssa: on stmt: $const110.19.2 = const(int, 0)
2025-08-27 14:20:27,387 [DEBUG] numba.core.ssa: on stmt: n_orig = static_getitem(value=$90load_attr.18, index=0, index_var=$const110.19.2, fn=<built-in function getitem>)
2025-08-27 14:20:27,387 [DEBUG] numba.core.ssa: on stmt: $120load_attr.22 = getattr(value=t_out, attr=shape)
2025-08-27 14:20:27,387 [DEBUG] numba.core.ssa: on stmt: $const140.23.2 = const(int, 0)
2025-08-27 14:20:27,387 [DEBUG] numba.core.ssa: on stmt: n_out = static_getitem(value=$120load_attr.22, index=0, index_var=$const140.23.2, fn=<built-in function getitem>)
2025-08-27 14:20:27,387 [DEBUG] numba.core.ssa: on stmt: $148load_global.25 = global(prange: <class 'numba.misc.special.prange'>)
2025-08-27 14:20:27,387 [DEBUG] numba.core.ssa: on stmt: $160call.28 = call $148load_global.25(n_out, func=$148load_global.25, args=[Var(n_out, interpn.py:21)], kws=(), vararg=None, varkwarg=None, target=None)
2025-08-27 14:20:27,387 [DEBUG] numba.core.ssa: on stmt: $168get_iter.29 = getiter(value=$160call.28)
2025-08-27 14:20:27,387 [DEBUG] numba.core.ssa: on stmt: $phi170.0 = $168get_iter.29
2025-08-27 14:20:27,387 [DEBUG] numba.core.ssa: on stmt: jump 170
2025-08-27 14:20:27,387 [DEBUG] numba.core.ssa: ==== SSA block rewrite pass on 170
2025-08-27 14:20:27,387 [DEBUG] numba.core.ssa: Running <numba.core.ssa._FixSSAVars object at 0x32e5da250>
2025-08-27 14:20:27,387 [DEBUG] numba.core.ssa: on stmt: $170for_iter.1 = iternext(value=$phi170.0)
2025-08-27 14:20:27,387 [DEBUG] numba.core.ssa: on stmt: $170for_iter.2 = pair_first(value=$170for_iter.1)
2025-08-27 14:20:27,387 [DEBUG] numba.core.ssa: on stmt: $170for_iter.3 = pair_second(value=$170for_iter.1)
2025-08-27 14:20:27,387 [DEBUG] numba.core.ssa: on stmt: $phi174.1 = $170for_iter.2
2025-08-27 14:20:27,387 [DEBUG] numba.core.ssa: on stmt: branch $170for_iter.3, 174, 654
2025-08-27 14:20:27,387 [DEBUG] numba.core.ssa: ==== SSA block rewrite pass on 174
2025-08-27 14:20:27,387 [DEBUG] numba.core.ssa: Running <numba.core.ssa._FixSSAVars object at 0x32e5da250>
2025-08-27 14:20:27,387 [DEBUG] numba.core.ssa: on stmt: t = $phi174.1
2025-08-27 14:20:27,387 [DEBUG] numba.core.ssa: on stmt: time_register.1 = getitem(value=t_out, index=t, fn=<built-in function getitem>)
2025-08-27 14:20:27,387 [DEBUG] numba.core.ssa: on stmt: $186load_global.5 = global(int: <class 'int'>)
2025-08-27 14:20:27,387 [DEBUG] numba.core.ssa: on stmt: n.1 = call $186load_global.5(time_register.1, func=$186load_global.5, args=[Var(time_register.1, interpn.py:24)], kws=(), vararg=None, varkwarg=None, target=None)
2025-08-27 14:20:27,387 [DEBUG] numba.core.ssa: on stmt: $binop_sub212.12 = time_register.1 - n.1
2025-08-27 14:20:27,387 [DEBUG] numba.core.ssa: on stmt: frac.1 = scale * $binop_sub212.12
2025-08-27 14:20:27,387 [DEBUG] numba.core.ssa: on stmt: index_frac.1 = frac.1 * num_table
2025-08-27 14:20:27,387 [DEBUG] numba.core.ssa: on stmt: $230load_global.17 = global(int: <class 'int'>)
2025-08-27 14:20:27,387 [DEBUG] numba.core.ssa: on stmt: offset.1 = call $230load_global.17(index_frac.1, func=$230load_global.17, args=[Var(index_frac.1, interpn.py:33)], kws=(), vararg=None, varkwarg=None, target=None)
2025-08-27 14:20:27,387 [DEBUG] numba.core.ssa: on stmt: eta.1 = index_frac.1 - offset.1
2025-08-27 14:20:27,387 [DEBUG] numba.core.ssa: on stmt: $260load_global.24 = global(min: <built-in function min>)
2025-08-27 14:20:27,387 [DEBUG] numba.core.ssa: on stmt: $const272.27.3 = const(int, 1)
2025-08-27 14:20:27,387 [DEBUG] numba.core.ssa: on stmt: $binop_add274.28 = n.1 + $const272.27.3
2025-08-27 14:20:27,387 [DEBUG] numba.core.ssa: on stmt: $binop_sub280.31 = nwin - offset.1
2025-08-27 14:20:27,387 [DEBUG] numba.core.ssa: on stmt: $binop_floordiv286.33 = $binop_sub280.31 // index_step
2025-08-27 14:20:27,387 [DEBUG] numba.core.ssa: on stmt: i_max = call $260load_global.24($binop_add274.28, $binop_floordiv286.33, func=$260load_global.24, args=[Var($binop_add274.28, interpn.py:40), Var($binop_floordiv286.33, interpn.py:40)], kws=(), vararg=None, varkwarg=None, target=None)
2025-08-27 14:20:27,388 [DEBUG] numba.core.ssa: on stmt: $300load_global.35 = global(range: <class 'range'>)
2025-08-27 14:20:27,388 [DEBUG] numba.core.ssa: on stmt: $312call.38 = call $300load_global.35(i_max, func=$300load_global.35, args=[Var(i_max, interpn.py:40)], kws=(), vararg=None, varkwarg=None, target=None)
2025-08-27 14:20:27,388 [DEBUG] numba.core.ssa: on stmt: $320get_iter.39 = getiter(value=$312call.38)
2025-08-27 14:20:27,388 [DEBUG] numba.core.ssa: on stmt: $phi322.1 = $320get_iter.39
2025-08-27 14:20:27,388 [DEBUG] numba.core.ssa: on stmt: jump 322
2025-08-27 14:20:27,388 [DEBUG] numba.core.ssa: ==== SSA block rewrite pass on 322
2025-08-27 14:20:27,388 [DEBUG] numba.core.ssa: Running <numba.core.ssa._FixSSAVars object at 0x32e5da250>
2025-08-27 14:20:27,388 [DEBUG] numba.core.ssa: on stmt: $322for_iter.2 = iternext(value=$phi322.1)
2025-08-27 14:20:27,388 [DEBUG] numba.core.ssa: on stmt: $322for_iter.3 = pair_first(value=$322for_iter.2)
2025-08-27 14:20:27,388 [DEBUG] numba.core.ssa: on stmt: $322for_iter.4 = pair_second(value=$322for_iter.2)
2025-08-27 14:20:27,388 [DEBUG] numba.core.ssa: on stmt: $phi326.2 = $322for_iter.3
2025-08-27 14:20:27,388 [DEBUG] numba.core.ssa: on stmt: branch $322for_iter.4, 326, 422
2025-08-27 14:20:27,388 [DEBUG] numba.core.ssa: ==== SSA block rewrite pass on 326
2025-08-27 14:20:27,388 [DEBUG] numba.core.ssa: Running <numba.core.ssa._FixSSAVars object at 0x32e5da250>
2025-08-27 14:20:27,388 [DEBUG] numba.core.ssa: on stmt: i = $phi326.2
2025-08-27 14:20:27,388 [DEBUG] numba.core.ssa: on stmt: $binop_mul334.7 = i * index_step
2025-08-27 14:20:27,388 [DEBUG] numba.core.ssa: on stmt: $binop_add338.8 = offset.1 + $binop_mul334.7
2025-08-27 14:20:27,388 [DEBUG] numba.core.ssa: on stmt: $342binary_subscr.9 = getitem(value=interp_win, index=$binop_add338.8, fn=<built-in function getitem>)
2025-08-27 14:20:27,388 [DEBUG] numba.core.ssa: on stmt: $binop_mul354.15 = i * index_step
2025-08-27 14:20:27,388 [DEBUG] numba.core.ssa: on stmt: $binop_add358.16 = offset.1 + $binop_mul354.15
2025-08-27 14:20:27,388 [DEBUG] numba.core.ssa: on stmt: $362binary_subscr.17 = getitem(value=interp_delta, index=$binop_add358.16, fn=<built-in function getitem>)
2025-08-27 14:20:27,388 [DEBUG] numba.core.ssa: on stmt: $binop_mul366.18 = eta.1 * $362binary_subscr.17
2025-08-27 14:20:27,388 [DEBUG] numba.core.ssa: on stmt: weight.1 = $342binary_subscr.9 + $binop_mul366.18
2025-08-27 14:20:27,388 [DEBUG] numba.core.ssa: on stmt: $384binary_subscr.22 = getitem(value=y, index=t, fn=<built-in function getitem>)
2025-08-27 14:20:27,388 [DEBUG] numba.core.ssa: on stmt: $binop_sub394.27 = n.1 - i
2025-08-27 14:20:27,388 [DEBUG] numba.core.ssa: on stmt: $398binary_subscr.28 = getitem(value=x, index=$binop_sub394.27, fn=<built-in function getitem>)
2025-08-27 14:20:27,388 [DEBUG] numba.core.ssa: on stmt: $binop_mul402.29 = weight * $398binary_subscr.28
2025-08-27 14:20:27,388 [DEBUG] numba.core.ssa: find_def var='weight' stmt=$binop_mul402.29 = weight * $398binary_subscr.28
2025-08-27 14:20:27,388 [DEBUG] numba.core.ssa: replaced with: $binop_mul402.29 = weight.1 * $398binary_subscr.28
2025-08-27 14:20:27,388 [DEBUG] numba.core.ssa: on stmt: $binop_iadd406.30 = inplace_binop(fn=<built-in function iadd>, immutable_fn=<built-in function add>, lhs=$384binary_subscr.22, rhs=$binop_mul402.29, static_lhs=Undefined, static_rhs=Undefined)
2025-08-27 14:20:27,388 [DEBUG] numba.core.ssa: on stmt: y[t] = $binop_iadd406.30
2025-08-27 14:20:27,388 [DEBUG] numba.core.ssa: on stmt: jump 322
2025-08-27 14:20:27,388 [DEBUG] numba.core.ssa: ==== SSA block rewrite pass on 422
2025-08-27 14:20:27,388 [DEBUG] numba.core.ssa: Running <numba.core.ssa._FixSSAVars object at 0x32e5da250>
2025-08-27 14:20:27,388 [DEBUG] numba.core.ssa: on stmt: $binop_sub428.5 = scale - frac.1
2025-08-27 14:20:27,388 [DEBUG] numba.core.ssa: on stmt: frac.2 = $binop_sub428.5
2025-08-27 14:20:27,388 [DEBUG] numba.core.ssa: on stmt: index_frac.2 = frac.2 * num_table
2025-08-27 14:20:27,388 [DEBUG] numba.core.ssa: on stmt: $442load_global.9 = global(int: <class 'int'>)
2025-08-27 14:20:27,388 [DEBUG] numba.core.ssa: on stmt: offset.2 = call $442load_global.9(index_frac.2, func=$442load_global.9, args=[Var(index_frac.2, interpn.py:53)], kws=(), vararg=None, varkwarg=None, target=None)
2025-08-27 14:20:27,388 [DEBUG] numba.core.ssa: on stmt: eta.2 = index_frac.2 - offset.2
2025-08-27 14:20:27,388 [DEBUG] numba.core.ssa: on stmt: $472load_global.16 = global(min: <built-in function min>)
2025-08-27 14:20:27,388 [DEBUG] numba.core.ssa: on stmt: $binop_sub486.20 = n_orig - n.1
2025-08-27 14:20:27,388 [DEBUG] numba.core.ssa: on stmt: $const490.21.3 = const(int, 1)
2025-08-27 14:20:27,388 [DEBUG] numba.core.ssa: on stmt: $binop_sub492.22 = $binop_sub486.20 - $const490.21.3
2025-08-27 14:20:27,388 [DEBUG] numba.core.ssa: on stmt: $binop_sub498.25 = nwin - offset.2
2025-08-27 14:20:27,388 [DEBUG] numba.core.ssa: on stmt: $binop_floordiv504.27 = $binop_sub498.25 // index_step
2025-08-27 14:20:27,388 [DEBUG] numba.core.ssa: on stmt: k_max = call $472load_global.16($binop_sub492.22, $binop_floordiv504.27, func=$472load_global.16, args=[Var($binop_sub492.22, interpn.py:60), Var($binop_floordiv504.27, interpn.py:60)], kws=(), vararg=None, varkwarg=None, target=None)
2025-08-27 14:20:27,388 [DEBUG] numba.core.ssa: on stmt: $518load_global.29 = global(range: <class 'range'>)
2025-08-27 14:20:27,388 [DEBUG] numba.core.ssa: on stmt: $530call.32 = call $518load_global.29(k_max, func=$518load_global.29, args=[Var(k_max, interpn.py:60)], kws=(), vararg=None, varkwarg=None, target=None)
2025-08-27 14:20:27,388 [DEBUG] numba.core.ssa: on stmt: $538get_iter.33 = getiter(value=$530call.32)
2025-08-27 14:20:27,388 [DEBUG] numba.core.ssa: on stmt: $phi540.1 = $538get_iter.33
2025-08-27 14:20:27,388 [DEBUG] numba.core.ssa: on stmt: jump 540
2025-08-27 14:20:27,388 [DEBUG] numba.core.ssa: ==== SSA block rewrite pass on 540
2025-08-27 14:20:27,388 [DEBUG] numba.core.ssa: Running <numba.core.ssa._FixSSAVars object at 0x32e5da250>
2025-08-27 14:20:27,388 [DEBUG] numba.core.ssa: on stmt: $540for_iter.2 = iternext(value=$phi540.1)
2025-08-27 14:20:27,388 [DEBUG] numba.core.ssa: on stmt: $540for_iter.3 = pair_first(value=$540for_iter.2)
2025-08-27 14:20:27,388 [DEBUG] numba.core.ssa: on stmt: $540for_iter.4 = pair_second(value=$540for_iter.2)
2025-08-27 14:20:27,388 [DEBUG] numba.core.ssa: on stmt: $phi544.2 = $540for_iter.3
2025-08-27 14:20:27,388 [DEBUG] numba.core.ssa: on stmt: branch $540for_iter.4, 544, 646
2025-08-27 14:20:27,388 [DEBUG] numba.core.ssa: ==== SSA block rewrite pass on 544
2025-08-27 14:20:27,388 [DEBUG] numba.core.ssa: Running <numba.core.ssa._FixSSAVars object at 0x32e5da250>
2025-08-27 14:20:27,388 [DEBUG] numba.core.ssa: on stmt: k = $phi544.2
2025-08-27 14:20:27,388 [DEBUG] numba.core.ssa: on stmt: $binop_mul552.7 = k * index_step
2025-08-27 14:20:27,388 [DEBUG] numba.core.ssa: on stmt: $binop_add556.8 = offset.2 + $binop_mul552.7
2025-08-27 14:20:27,388 [DEBUG] numba.core.ssa: on stmt: $560binary_subscr.9 = getitem(value=interp_win, index=$binop_add556.8, fn=<built-in function getitem>)
2025-08-27 14:20:27,388 [DEBUG] numba.core.ssa: on stmt: $binop_mul572.15 = k * index_step
2025-08-27 14:20:27,388 [DEBUG] numba.core.ssa: on stmt: $binop_add576.16 = offset.2 + $binop_mul572.15
2025-08-27 14:20:27,388 [DEBUG] numba.core.ssa: on stmt: $580binary_subscr.17 = getitem(value=interp_delta, index=$binop_add576.16, fn=<built-in function getitem>)
2025-08-27 14:20:27,388 [DEBUG] numba.core.ssa: on stmt: $binop_mul584.18 = eta.2 * $580binary_subscr.17
2025-08-27 14:20:27,388 [DEBUG] numba.core.ssa: on stmt: weight.2 = $560binary_subscr.9 + $binop_mul584.18
2025-08-27 14:20:27,388 [DEBUG] numba.core.ssa: on stmt: $602binary_subscr.22 = getitem(value=y, index=t, fn=<built-in function getitem>)
2025-08-27 14:20:27,388 [DEBUG] numba.core.ssa: on stmt: $binop_add612.27 = n.1 + k
2025-08-27 14:20:27,389 [DEBUG] numba.core.ssa: on stmt: $const616.28.3 = const(int, 1)
2025-08-27 14:20:27,389 [DEBUG] numba.core.ssa: on stmt: $binop_add618.29 = $binop_add612.27 + $const616.28.3
2025-08-27 14:20:27,389 [DEBUG] numba.core.ssa: on stmt: $622binary_subscr.30 = getitem(value=x, index=$binop_add618.29, fn=<built-in function getitem>)
2025-08-27 14:20:27,389 [DEBUG] numba.core.ssa: on stmt: $binop_mul626.31 = weight * $622binary_subscr.30
2025-08-27 14:20:27,389 [DEBUG] numba.core.ssa: find_def var='weight' stmt=$binop_mul626.31 = weight * $622binary_subscr.30
2025-08-27 14:20:27,389 [DEBUG] numba.core.ssa: replaced with: $binop_mul626.31 = weight.2 * $622binary_subscr.30
2025-08-27 14:20:27,389 [DEBUG] numba.core.ssa: on stmt: $binop_iadd630.32 = inplace_binop(fn=<built-in function iadd>, immutable_fn=<built-in function add>, lhs=$602binary_subscr.22, rhs=$binop_mul626.31, static_lhs=Undefined, static_rhs=Undefined)
2025-08-27 14:20:27,389 [DEBUG] numba.core.ssa: on stmt: y[t] = $binop_iadd630.32
2025-08-27 14:20:27,389 [DEBUG] numba.core.ssa: on stmt: jump 540
2025-08-27 14:20:27,389 [DEBUG] numba.core.ssa: ==== SSA block rewrite pass on 646
2025-08-27 14:20:27,389 [DEBUG] numba.core.ssa: Running <numba.core.ssa._FixSSAVars object at 0x32e5da250>
2025-08-27 14:20:27,389 [DEBUG] numba.core.ssa: on stmt: jump 170
2025-08-27 14:20:27,389 [DEBUG] numba.core.ssa: ==== SSA block rewrite pass on 654
2025-08-27 14:20:27,389 [DEBUG] numba.core.ssa: Running <numba.core.ssa._FixSSAVars object at 0x32e5da250>
2025-08-27 14:20:27,389 [DEBUG] numba.core.ssa: on stmt: $const658.2 = const(NoneType, None)
2025-08-27 14:20:27,389 [DEBUG] numba.core.ssa: on stmt: $658return_const.3 = cast(value=$const658.2)
2025-08-27 14:20:27,389 [DEBUG] numba.core.ssa: on stmt: return $658return_const.3
2025-08-27 14:20:27,698 [WARNING] root: 高CPU使用率: 136.4%
2025-08-27 14:20:27,698 [WARNING] root: 线程数量异常: 23
2025-08-27 14:20:28,204 [WARNING] root: 高CPU使用率: 189.8%
2025-08-27 14:20:28,204 [WARNING] root: 线程数量异常: 23
2025-08-27 14:20:28,709 [WARNING] root: 高CPU使用率: 196.8%
2025-08-27 14:20:28,709 [WARNING] root: 线程数量异常: 23
2025-08-27 14:20:29,217 [WARNING] root: 高CPU使用率: 193.6%
2025-08-27 14:20:29,217 [WARNING] root: 线程数量异常: 23
2025-08-27 14:20:29,522 [INFO] root: 第 1 轮输入完成
2025-08-27 14:20:29,522 [INFO] root: --- 第 2 轮输入 ---
2025-08-27 14:20:29,528 [INFO] root: 生成长语音数据: 4秒
2025-08-27 14:20:29,528 [INFO] root: 开始输入 188 个音频块...
2025-08-27 14:20:29,725 [WARNING] root: 高CPU使用率: 117.6%
2025-08-27 14:20:29,725 [WARNING] root: 线程数量异常: 23
2025-08-27 14:20:30,235 [WARNING] root: 线程数量异常: 23
2025-08-27 14:20:30,746 [WARNING] root: 线程数量异常: 23
2025-08-27 14:20:31,255 [WARNING] root: 线程数量异常: 23
2025-08-27 14:20:31,757 [WARNING] root: 高CPU使用率: 158.7%
2025-08-27 14:20:31,757 [WARNING] root: 线程数量异常: 23
2025-08-27 14:20:32,122 [INFO] root: 添加句子间停顿...
2025-08-27 14:20:32,267 [WARNING] root: 高CPU使用率: 196.7%
2025-08-27 14:20:32,267 [WARNING] root: 线程数量异常: 23
2025-08-27 14:20:32,777 [WARNING] root: 高CPU使用率: 196.3%
2025-08-27 14:20:32,777 [WARNING] root: 线程数量异常: 23
2025-08-27 14:20:32,792 [INFO] root: 等待当前轮次处理完成...
2025-08-27 14:20:33,282 [WARNING] root: 高CPU使用率: 176.7%
2025-08-27 14:20:33,282 [WARNING] root: 线程数量异常: 23
2025-08-27 14:20:33,793 [WARNING] root: 高CPU使用率: 135.4%
2025-08-27 14:20:33,793 [WARNING] root: 线程数量异常: 23
2025-08-27 14:20:34,298 [WARNING] root: 高CPU使用率: 142.8%
2025-08-27 14:20:34,299 [WARNING] root: 线程数量异常: 23
2025-08-27 14:20:34,807 [WARNING] root: 高CPU使用率: 137.0%
2025-08-27 14:20:34,808 [WARNING] root: 线程数量异常: 23
2025-08-27 14:20:35,317 [WARNING] root: 高CPU使用率: 133.7%
2025-08-27 14:20:35,317 [WARNING] root: 线程数量异常: 23
2025-08-27 14:20:35,796 [INFO] root: 第 2 轮输入完成
2025-08-27 14:20:35,796 [INFO] root: --- 第 3 轮输入 ---
2025-08-27 14:20:35,799 [INFO] root: 生成短语音数据: 2秒
2025-08-27 14:20:35,799 [INFO] root: 开始输入 94 个音频块...
2025-08-27 14:20:35,822 [WARNING] root: 高CPU使用率: 142.0%
2025-08-27 14:20:35,822 [WARNING] root: 线程数量异常: 23
2025-08-27 14:20:36,331 [WARNING] root: 高CPU使用率: 144.8%
2025-08-27 14:20:36,331 [WARNING] root: 线程数量异常: 23
2025-08-27 14:20:36,839 [WARNING] root: 高CPU使用率: 140.6%
2025-08-27 14:20:36,839 [WARNING] root: 线程数量异常: 23
2025-08-27 14:20:37,108 [INFO] root: 添加句子间停顿...
2025-08-27 14:20:37,342 [WARNING] root: 高CPU使用率: 142.9%
2025-08-27 14:20:37,342 [WARNING] root: 线程数量异常: 23
2025-08-27 14:20:37,795 [INFO] root: 等待当前轮次处理完成...
2025-08-27 14:20:37,852 [WARNING] root: 高CPU使用率: 144.5%
2025-08-27 14:20:37,852 [WARNING] root: 线程数量异常: 23
2025-08-27 14:20:38,362 [WARNING] root: 高CPU使用率: 145.7%
2025-08-27 14:20:38,362 [WARNING] root: 线程数量异常: 23
2025-08-27 14:20:38,873 [WARNING] root: 高CPU使用率: 145.3%
2025-08-27 14:20:38,873 [WARNING] root: 线程数量异常: 23
2025-08-27 14:20:39,378 [WARNING] root: 高CPU使用率: 147.2%
2025-08-27 14:20:39,378 [WARNING] root: 线程数量异常: 23
2025-08-27 14:20:39,882 [WARNING] root: 高CPU使用率: 148.6%
2025-08-27 14:20:39,883 [WARNING] root: 线程数量异常: 23
2025-08-27 14:20:40,386 [WARNING] root: 高CPU使用率: 149.0%
2025-08-27 14:20:40,386 [WARNING] root: 线程数量异常: 23
2025-08-27 14:20:40,800 [INFO] root: 第 3 轮输入完成
2025-08-27 14:20:40,800 [INFO] root: --- 第 4 轮输入 ---
2025-08-27 14:20:40,806 [INFO] root: 生成长语音数据: 4秒
2025-08-27 14:20:40,806 [INFO] root: 开始输入 188 个音频块...
2025-08-27 14:20:40,896 [WARNING] root: 高CPU使用率: 149.3%
2025-08-27 14:20:40,896 [WARNING] root: 线程数量异常: 23
2025-08-27 14:20:41,400 [WARNING] root: 高CPU使用率: 136.0%
2025-08-27 14:20:41,401 [WARNING] root: 线程数量异常: 23
2025-08-27 14:20:41,908 [WARNING] root: 高CPU使用率: 150.3%
2025-08-27 14:20:41,908 [WARNING] root: 线程数量异常: 23
2025-08-27 14:20:42,418 [WARNING] root: 高CPU使用率: 149.0%
2025-08-27 14:20:42,418 [WARNING] root: 线程数量异常: 23
2025-08-27 14:20:42,925 [WARNING] root: 高CPU使用率: 150.4%
2025-08-27 14:20:42,925 [WARNING] root: 线程数量异常: 23
2025-08-27 14:20:43,435 [WARNING] root: 高CPU使用率: 198.1%
2025-08-27 14:20:43,436 [WARNING] root: 线程数量异常: 23
2025-08-27 14:20:43,438 [INFO] root: 添加句子间停顿...
2025-08-27 14:20:43,938 [WARNING] root: 高CPU使用率: 195.0%
2025-08-27 14:20:43,938 [WARNING] root: 线程数量异常: 23
2025-08-27 14:20:44,096 [INFO] root: 等待当前轮次处理完成...
2025-08-27 14:20:44,448 [WARNING] root: 高CPU使用率: 187.9%
2025-08-27 14:20:44,448 [WARNING] root: 线程数量异常: 23
2025-08-27 14:20:44,958 [WARNING] root: 高CPU使用率: 165.0%
2025-08-27 14:20:44,958 [WARNING] root: 线程数量异常: 23
2025-08-27 14:20:45,320 [DEBUG] openai._base_client: Request options: {'method': 'post', 'url': '/chat/completions', 'files': None, 'idempotency_key': 'stainless-python-retry-cca00ff2-883a-4370-9bf6-a6cc7e7fda7e', 'json_data': {'messages': [{'role': 'system', 'content': '你是一个专业的同声传译员。请将以下zh文本翻译成en。\n\n翻译要求：\n1. 保持原意的准确性和完整性\n2. 使用自然流畅的表达方式\n3. 适合口语交流的语调\n4. 如果是专业术语，请使用准确的专业词汇\n5. 只返回翻译结果，不要额外说明\n\n文本：'}, {'role': 'user', 'content': 'by bwd6'}], 'model': 'gpt-4o-mini', 'frequency_penalty': 0.0, 'max_tokens': 300, 'presence_penalty': 0.0, 'temperature': 0.1, 'top_p': 0.9}}
2025-08-27 14:20:45,432 [DEBUG] openai._base_client: Sending HTTP Request: POST https://api.openai.com/v1/chat/completions
2025-08-27 14:20:45,432 [DEBUG] httpcore.connection: connect_tcp.started host='127.0.0.1' port=7890 local_address=None timeout=5.0 socket_options=None
2025-08-27 14:20:45,435 [DEBUG] httpcore.connection: connect_tcp.complete return_value=<httpcore._backends.sync.SyncStream object at 0x334b957f0>
2025-08-27 14:20:45,435 [DEBUG] httpcore.http11: send_request_headers.started request=<Request [b'CONNECT']>
2025-08-27 14:20:45,435 [DEBUG] httpcore.http11: send_request_headers.complete
2025-08-27 14:20:45,435 [DEBUG] httpcore.http11: send_request_body.started request=<Request [b'CONNECT']>
2025-08-27 14:20:45,435 [DEBUG] httpcore.http11: send_request_body.complete
2025-08-27 14:20:45,435 [DEBUG] httpcore.http11: receive_response_headers.started request=<Request [b'CONNECT']>
2025-08-27 14:20:45,436 [DEBUG] httpcore.http11: receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'Connection established', [])
2025-08-27 14:20:45,436 [DEBUG] httpcore.proxy: start_tls.started ssl_context=<ssl.SSLContext object at 0x1103439b0> server_hostname='api.openai.com' timeout=5.0
2025-08-27 14:20:45,467 [WARNING] root: 高CPU使用率: 174.5%
2025-08-27 14:20:45,467 [WARNING] root: 线程数量异常: 23
2025-08-27 14:20:45,723 [DEBUG] httpcore.proxy: start_tls.complete return_value=<httpcore._backends.sync.SyncStream object at 0x33473de50>
2025-08-27 14:20:45,724 [DEBUG] httpcore.http11: send_request_headers.started request=<Request [b'POST']>
2025-08-27 14:20:45,724 [DEBUG] httpcore.http11: send_request_headers.complete
2025-08-27 14:20:45,724 [DEBUG] httpcore.http11: send_request_body.started request=<Request [b'POST']>
2025-08-27 14:20:45,724 [DEBUG] httpcore.http11: send_request_body.complete
2025-08-27 14:20:45,724 [DEBUG] httpcore.http11: receive_response_headers.started request=<Request [b'POST']>
2025-08-27 14:20:45,975 [WARNING] root: 高CPU使用率: 197.5%
2025-08-27 14:20:45,976 [WARNING] root: 线程数量异常: 23
2025-08-27 14:20:46,486 [WARNING] root: 高CPU使用率: 190.1%
2025-08-27 14:20:46,486 [WARNING] root: 线程数量异常: 23
2025-08-27 14:20:46,733 [DEBUG] httpcore.http11: receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Wed, 27 Aug 2025 06:20:48 GMT'), (b'Content-Type', b'application/json'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'access-control-expose-headers', b'X-Request-ID'), (b'openai-organization', b'user-pxdpchcqnnsqauie4s2st7qc'), (b'openai-processing-ms', b'431'), (b'openai-project', b'proj_EHp5HQe3tuOWJuBIvRMUhI4P'), (b'openai-version', b'2020-10-01'), (b'x-envoy-upstream-service-time', b'437'), (b'x-ratelimit-limit-requests', b'5000'), (b'x-ratelimit-limit-tokens', b'2000000'), (b'x-ratelimit-remaining-requests', b'4999'), (b'x-ratelimit-remaining-tokens', b'1999917'), (b'x-ratelimit-reset-requests', b'12ms'), (b'x-ratelimit-reset-tokens', b'2ms'), (b'x-request-id', b'req_ea180eeeb8984f4d81a7d8ede67236d9'), (b'cf-cache-status', b'DYNAMIC'), (b'Set-Cookie', b'__cf_bm=wpirouHxJZgx5rfLzZSzQ5RurXhl0RCOaR.wVjSGaq0-1756275648-*******-HeKsgzcYx5VwPP4hRBdgQLELoKsjPuk5xkn2h8hS7pNMwEKBSG68xy9eNLWOhnrPFsn7FtknCOLIJDJb_PXUrWYLRXvJI21uO3OtqoipH1w; path=/; expires=Wed, 27-Aug-25 06:50:48 GMT; domain=.api.openai.com; HttpOnly; Secure; SameSite=None'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'X-Content-Type-Options', b'nosniff'), (b'Set-Cookie', b'_cfuvid=EfzOZT33.hAuCMD1whqUKhiFhElGIXicD3ehdawwxkA-1756275648815-*******-604800000; path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None'), (b'Server', b'cloudflare'), (b'CF-RAY', b'9759770f4d3835b2-SIN'), (b'Content-Encoding', b'gzip'), (b'alt-svc', b'h3=":443"; ma=86400')])
2025-08-27 14:20:46,734 [INFO] httpx: HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-27 14:20:46,734 [DEBUG] httpcore.http11: receive_response_body.started request=<Request [b'POST']>
2025-08-27 14:20:46,737 [DEBUG] httpcore.http11: receive_response_body.complete
2025-08-27 14:20:46,737 [DEBUG] httpcore.http11: response_closed.started
2025-08-27 14:20:46,737 [DEBUG] httpcore.http11: response_closed.complete
2025-08-27 14:20:46,737 [DEBUG] openai._base_client: HTTP Response: POST https://api.openai.com/v1/chat/completions "200 OK" Headers([('date', 'Wed, 27 Aug 2025 06:20:48 GMT'), ('content-type', 'application/json'), ('transfer-encoding', 'chunked'), ('connection', 'keep-alive'), ('access-control-expose-headers', 'X-Request-ID'), ('openai-organization', 'user-pxdpchcqnnsqauie4s2st7qc'), ('openai-processing-ms', '431'), ('openai-project', 'proj_EHp5HQe3tuOWJuBIvRMUhI4P'), ('openai-version', '2020-10-01'), ('x-envoy-upstream-service-time', '437'), ('x-ratelimit-limit-requests', '5000'), ('x-ratelimit-limit-tokens', '2000000'), ('x-ratelimit-remaining-requests', '4999'), ('x-ratelimit-remaining-tokens', '1999917'), ('x-ratelimit-reset-requests', '12ms'), ('x-ratelimit-reset-tokens', '2ms'), ('x-request-id', 'req_ea180eeeb8984f4d81a7d8ede67236d9'), ('cf-cache-status', 'DYNAMIC'), ('set-cookie', '__cf_bm=wpirouHxJZgx5rfLzZSzQ5RurXhl0RCOaR.wVjSGaq0-1756275648-*******-HeKsgzcYx5VwPP4hRBdgQLELoKsjPuk5xkn2h8hS7pNMwEKBSG68xy9eNLWOhnrPFsn7FtknCOLIJDJb_PXUrWYLRXvJI21uO3OtqoipH1w; path=/; expires=Wed, 27-Aug-25 06:50:48 GMT; domain=.api.openai.com; HttpOnly; Secure; SameSite=None'), ('strict-transport-security', 'max-age=31536000; includeSubDomains; preload'), ('x-content-type-options', 'nosniff'), ('set-cookie', '_cfuvid=EfzOZT33.hAuCMD1whqUKhiFhElGIXicD3ehdawwxkA-1756275648815-*******-604800000; path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None'), ('server', 'cloudflare'), ('cf-ray', '9759770f4d3835b2-SIN'), ('content-encoding', 'gzip'), ('alt-svc', 'h3=":443"; ma=86400')])
2025-08-27 14:20:46,737 [DEBUG] openai._base_client: request_id: req_ea180eeeb8984f4d81a7d8ede67236d9
2025-08-27 14:20:46,996 [WARNING] root: 高CPU使用率: 191.1%
2025-08-27 14:20:46,997 [WARNING] root: 线程数量异常: 23
2025-08-27 14:20:47,104 [INFO] root: 第 4 轮输入完成
2025-08-27 14:20:47,104 [INFO] root: --- 第 5 轮输入 ---
2025-08-27 14:20:47,107 [INFO] root: 生成短语音数据: 2秒
2025-08-27 14:20:47,107 [INFO] root: 开始输入 94 个音频块...
2025-08-27 14:20:47,507 [WARNING] root: 高CPU使用率: 143.2%
2025-08-27 14:20:47,507 [WARNING] root: 线程数量异常: 23
2025-08-27 14:20:48,015 [WARNING] root: 高CPU使用率: 204.6%
2025-08-27 14:20:48,016 [WARNING] root: 线程数量异常: 23
2025-08-27 14:20:48,384 [INFO] root: 添加句子间停顿...
2025-08-27 14:20:48,526 [WARNING] root: 高CPU使用率: 192.8%
2025-08-27 14:20:48,526 [WARNING] root: 线程数量异常: 23
2025-08-27 14:20:49,036 [WARNING] root: 高CPU使用率: 190.1%
2025-08-27 14:20:49,036 [WARNING] root: 线程数量异常: 23
2025-08-27 14:20:49,067 [INFO] root: 等待当前轮次处理完成...
2025-08-27 14:20:49,546 [WARNING] root: 高CPU使用率: 163.5%
2025-08-27 14:20:49,547 [WARNING] root: 线程数量异常: 23
2025-08-27 14:20:50,057 [WARNING] root: 高CPU使用率: 178.4%
2025-08-27 14:20:50,057 [WARNING] root: 线程数量异常: 23
2025-08-27 14:20:50,565 [WARNING] root: 高CPU使用率: 196.0%
2025-08-27 14:20:50,565 [WARNING] root: 线程数量异常: 23
2025-08-27 14:20:51,075 [WARNING] root: 高CPU使用率: 193.3%
2025-08-27 14:20:51,076 [WARNING] root: 线程数量异常: 23
2025-08-27 14:20:51,581 [WARNING] root: 高CPU使用率: 181.9%
2025-08-27 14:20:51,581 [WARNING] root: 线程数量异常: 23
2025-08-27 14:20:52,077 [INFO] root: 第 5 轮输入完成
2025-08-27 14:20:52,077 [INFO] root: 连续输入测试通过，未检测到冻结
2025-08-27 14:20:52,077 [INFO] root: === 测试3: 压力场景测试 ===
2025-08-27 14:20:52,085 [WARNING] root: 高CPU使用率: 143.5%
2025-08-27 14:20:52,086 [WARNING] root: 线程数量异常: 23
2025-08-27 14:20:52,593 [WARNING] root: 高CPU使用率: 143.8%
2025-08-27 14:20:52,596 [WARNING] root: 线程数量异常: 23
2025-08-27 14:20:53,135 [WARNING] root: 高CPU使用率: 148.7%
2025-08-27 14:20:53,148 [WARNING] root: 线程数量异常: 23
2025-08-27 14:20:53,656 [WARNING] root: 高CPU使用率: 221.6%
2025-08-27 14:20:53,656 [WARNING] root: 线程数量异常: 23
2025-08-27 14:20:54,165 [WARNING] root: 高CPU使用率: 273.1%
2025-08-27 14:20:54,166 [WARNING] root: 线程数量异常: 23
2025-08-27 14:20:54,667 [WARNING] root: 高CPU使用率: 243.9%
2025-08-27 14:20:54,668 [WARNING] root: 线程数量异常: 23
2025-08-27 14:20:55,174 [WARNING] root: 高CPU使用率: 156.0%
2025-08-27 14:20:55,176 [WARNING] root: 线程数量异常: 23
2025-08-27 14:20:55,679 [WARNING] root: 高CPU使用率: 170.0%
2025-08-27 14:20:55,679 [WARNING] root: 线程数量异常: 23
2025-08-27 14:20:56,181 [WARNING] root: 高CPU使用率: 168.2%
2025-08-27 14:20:56,181 [WARNING] root: 线程数量异常: 23
2025-08-27 14:20:56,689 [WARNING] root: 高CPU使用率: 95.6%
2025-08-27 14:20:56,689 [WARNING] root: 线程数量异常: 23
2025-08-27 14:20:57,200 [WARNING] root: 高CPU使用率: 232.2%
2025-08-27 14:20:57,200 [WARNING] root: 线程数量异常: 23
2025-08-27 14:20:57,698 [INFO] root: 场景1: 快速连续输入
2025-08-27 14:20:57,710 [WARNING] root: 高CPU使用率: 330.4%
2025-08-27 14:20:57,710 [WARNING] root: 线程数量异常: 27
2025-08-27 14:20:58,038 [WARNING] root: 线程数量过多: 18
2025-08-27 14:20:58,222 [WARNING] root: 线程数量异常: 27
2025-08-27 14:20:58,724 [WARNING] root: 线程数量异常: 27
2025-08-27 14:20:59,234 [WARNING] root: 线程数量异常: 27
2025-08-27 14:20:59,743 [WARNING] root: 线程数量异常: 27
2025-08-27 14:21:00,043 [WARNING] root: 线程数量过多: 18
2025-08-27 14:21:00,249 [WARNING] root: 线程数量异常: 27
2025-08-27 14:21:00,296 [INFO] root: 场景2: 长时间连续语音
2025-08-27 14:21:00,756 [WARNING] root: 线程数量异常: 27
2025-08-27 14:21:01,267 [WARNING] root: 线程数量异常: 27
2025-08-27 14:21:01,769 [WARNING] root: 线程数量异常: 27
2025-08-27 14:21:02,047 [WARNING] root: 线程数量过多: 18
2025-08-27 14:21:02,277 [WARNING] root: 线程数量异常: 27
2025-08-27 14:21:02,779 [WARNING] root: 高CPU使用率: 114.0%
2025-08-27 14:21:02,779 [WARNING] root: 线程数量异常: 30
2025-08-27 14:21:03,287 [WARNING] root: 高CPU使用率: 162.0%
2025-08-27 14:21:03,287 [WARNING] root: 线程数量异常: 30
2025-08-27 14:21:03,788 [WARNING] root: 高CPU使用率: 143.8%
2025-08-27 14:21:03,788 [WARNING] root: 线程数量异常: 31
2025-08-27 14:21:04,048 [WARNING] root: 线程数量过多: 18
2025-08-27 14:21:04,299 [WARNING] root: 高CPU使用率: 178.2%
2025-08-27 14:21:04,299 [WARNING] root: 线程数量异常: 31
2025-08-27 14:21:04,809 [WARNING] root: 高CPU使用率: 175.8%
2025-08-27 14:21:04,809 [WARNING] root: 线程数量异常: 31
2025-08-27 14:21:05,316 [WARNING] root: 高CPU使用率: 102.4%
2025-08-27 14:21:05,316 [WARNING] root: 线程数量异常: 31
2025-08-27 14:21:05,821 [WARNING] root: 高CPU使用率: 178.1%
2025-08-27 14:21:05,821 [WARNING] root: 线程数量异常: 31
2025-08-27 14:21:06,057 [WARNING] root: 线程数量过多: 18
2025-08-27 14:21:06,331 [WARNING] root: 高CPU使用率: 190.5%
2025-08-27 14:21:06,331 [WARNING] root: 线程数量异常: 31
2025-08-27 14:21:06,837 [WARNING] root: 高CPU使用率: 190.8%
2025-08-27 14:21:06,837 [WARNING] root: 线程数量异常: 31
2025-08-27 14:21:07,348 [WARNING] root: 高CPU使用率: 186.8%
2025-08-27 14:21:07,348 [WARNING] root: 线程数量异常: 31
2025-08-27 14:21:07,852 [WARNING] root: 高CPU使用率: 130.4%
2025-08-27 14:21:07,852 [WARNING] root: 线程数量异常: 31
2025-08-27 14:21:08,066 [WARNING] root: 线程数量过多: 18
2025-08-27 14:21:08,362 [WARNING] root: 高CPU使用率: 196.3%
2025-08-27 14:21:08,363 [WARNING] root: 线程数量异常: 31
2025-08-27 14:21:08,869 [WARNING] root: 高CPU使用率: 190.3%
2025-08-27 14:21:08,869 [WARNING] root: 线程数量异常: 31
2025-08-27 14:21:09,380 [WARNING] root: 高CPU使用率: 196.3%
2025-08-27 14:21:09,380 [WARNING] root: 线程数量异常: 31
2025-08-27 14:21:09,557 [INFO] root: 场景3: 噪声干扰测试
2025-08-27 14:21:09,888 [WARNING] root: 高CPU使用率: 167.0%
2025-08-27 14:21:09,888 [WARNING] root: 线程数量异常: 31
2025-08-27 14:21:10,068 [WARNING] root: 线程数量过多: 18
2025-08-27 14:21:10,106 [INFO] root: === 测试4: 重置功能测试 ===
2025-08-27 14:21:10,398 [WARNING] root: 高CPU使用率: 90.8%
2025-08-27 14:21:10,401 [WARNING] root: 线程数量异常: 31
2025-08-27 14:21:10,905 [WARNING] root: 线程数量异常: 31
2025-08-27 14:21:11,409 [WARNING] root: 线程数量异常: 31
2025-08-27 14:21:12,056 [WARNING] root: 高CPU使用率: 92.2%
2025-08-27 14:21:12,056 [WARNING] root: 线程数量异常: 31
2025-08-27 14:21:12,078 [WARNING] root: 线程数量过多: 18
2025-08-27 14:21:12,566 [WARNING] root: 线程数量异常: 31
2025-08-27 14:21:13,074 [WARNING] root: 高CPU使用率: 95.0%
2025-08-27 14:21:13,074 [WARNING] root: 线程数量异常: 31
2025-08-27 14:21:13,579 [WARNING] root: 高CPU使用率: 98.6%
2025-08-27 14:21:13,579 [WARNING] root: 线程数量异常: 31
2025-08-27 14:21:14,082 [WARNING] root: 线程数量过多: 18
2025-08-27 14:21:14,089 [WARNING] root: 高CPU使用率: 97.0%
2025-08-27 14:21:14,089 [WARNING] root: 线程数量异常: 31
2025-08-27 14:21:14,598 [WARNING] root: 高CPU使用率: 120.1%
2025-08-27 14:21:14,598 [WARNING] root: 线程数量异常: 31
2025-08-27 14:21:15,102 [WARNING] root: 高CPU使用率: 333.9%
2025-08-27 14:21:15,102 [WARNING] root: 线程数量异常: 31
2025-08-27 14:21:15,386 [INFO] root: 产生处理负载...
2025-08-27 14:21:15,603 [WARNING] root: 高CPU使用率: 145.6%
2025-08-27 14:21:15,603 [WARNING] root: 线程数量异常: 35
2025-08-27 14:21:16,091 [WARNING] root: 线程数量过多: 22
2025-08-27 14:21:16,108 [WARNING] root: 线程数量异常: 34
2025-08-27 14:21:16,610 [WARNING] root: 线程数量异常: 34
2025-08-27 14:21:17,121 [WARNING] root: 线程数量异常: 34
2025-08-27 14:21:17,627 [WARNING] root: 线程数量异常: 34
2025-08-27 14:21:17,669 [INFO] root: 执行第 1 次重置...
2025-08-27 14:21:17,669 [INFO] root: 重置前队列状态: {'recognition_queue': 0, 'translation_queue': 0, 'tts_queue': 0}
2025-08-27 14:21:18,100 [WARNING] root: 线程数量过多: 22
2025-08-27 14:21:18,136 [WARNING] root: 线程数量异常: 34
2025-08-27 14:21:18,641 [WARNING] root: 线程数量异常: 34
2025-08-27 14:21:18,679 [INFO] root: 重置后队列状态: {'recognition_queue': 0, 'translation_queue': 0, 'tts_queue': 0}
2025-08-27 14:21:18,679 [INFO] root: 第 1 次重置成功
2025-08-27 14:21:19,150 [WARNING] root: 线程数量异常: 34
2025-08-27 14:21:19,656 [WARNING] root: 线程数量异常: 34
2025-08-27 14:21:19,685 [INFO] root: 执行第 2 次重置...
2025-08-27 14:21:19,686 [INFO] root: 重置前队列状态: {'recognition_queue': 0, 'translation_queue': 0, 'tts_queue': 0}
2025-08-27 14:21:20,110 [WARNING] root: 线程数量过多: 22
2025-08-27 14:21:20,166 [WARNING] root: 线程数量异常: 34
2025-08-27 14:21:20,667 [WARNING] root: 线程数量异常: 34
2025-08-27 14:21:20,696 [INFO] root: 重置后队列状态: {'recognition_queue': 0, 'translation_queue': 0, 'tts_queue': 0}
2025-08-27 14:21:20,696 [INFO] root: 第 2 次重置成功
2025-08-27 14:21:21,168 [WARNING] root: 线程数量异常: 34
2025-08-27 14:21:21,675 [WARNING] root: 线程数量异常: 34
2025-08-27 14:21:21,712 [INFO] root: 执行第 3 次重置...
2025-08-27 14:21:21,712 [INFO] root: 重置前队列状态: {'recognition_queue': 0, 'translation_queue': 0, 'tts_queue': 0}
2025-08-27 14:21:22,120 [WARNING] root: 线程数量过多: 22
2025-08-27 14:21:22,185 [WARNING] root: 线程数量异常: 34
2025-08-27 14:21:22,693 [WARNING] root: 线程数量异常: 34
2025-08-27 14:21:22,717 [INFO] root: 重置后队列状态: {'recognition_queue': 0, 'translation_queue': 0, 'tts_queue': 0}
2025-08-27 14:21:22,718 [INFO] root: 第 3 次重置成功
2025-08-27 14:21:23,196 [WARNING] root: 线程数量异常: 34
2025-08-27 14:21:23,699 [WARNING] root: 线程数量异常: 34
2025-08-27 14:21:23,736 [INFO] root: === 生成测试报告 ===
2025-08-27 14:21:23,752 [INFO] root: 测试报告已保存到: freeze_test_report_20250827_142123.json
