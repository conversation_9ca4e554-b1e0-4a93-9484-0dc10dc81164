#!/usr/bin/env python3
"""
实时同声传译核心模块 - 完全修复版
解决：
1. PaMacCore AUHAL Error -10863
2. 翻译延迟问题
3. 工作线程频繁重启
"""
import asyncio
import numpy as np
from collections import deque
import webrtcvad
import whisper
from openai import OpenAI
import edge_tts
import io
import wave
import time
import os
from threading import Thread, Lock, RLock, Event
import threading
from queue import Queue, Empty
import queue
import soundfile as sf
import logging
from audio_filter import filter_audio_text
from pydub import AudioSegment
import sounddevice as sd

class RealtimeTranslatorFixed:
    """修复版实时翻译器"""
    
    def __init__(self, source_lang='zh', target_lang='en', sample_rate=48000, 
                 audio_router=None, enable_tts=True, audio_capture=None):
        self.source_lang = source_lang
        self.target_lang = target_lang
        self.sample_rate = sample_rate
        self.audio_router = audio_router
        self.enable_tts = enable_tts
        self.audio_capture = audio_capture
        
        # 状态管理
        self.is_running = True
        self.state_lock = RLock()
        
        # 修复版VAD - 基于时间而非帧数
        self.audio_buffer = deque(maxlen=int(self.sample_rate * 1.0))  # 1秒缓冲
        self.speech_segments = []
        self.is_speaking = False
        self.speech_start_time = 0
        self.last_speech_time = 0
        
        # VAD参数 - 基于实际时间，平衡灵敏度和稳定性
        self.speech_threshold = 0.015   # QA测试调整：平衡灵敏度和稳定性
        self.silence_timeout = 0.8      # 0.8秒静音后结束
        self.min_speech_duration = 0.8  # 最小语音长度0.5秒
        self.max_speech_duration = 15.0 # 最大语音长度15秒
        
        # 去抖动参数
        self.speech_debounce_time = 0.5 # 连续0.3秒超过阈值才认为是语音
        self.speech_start_pending = False
        self.speech_start_pending_time = 0
        
        # 添加调试标志
        self.debug_vad = True  # 开启VAD调试信息
        self._last_silence_print = -1  # 初始化调试变量避免getattr问题
        
        # Whisper模型
        print("[系统] 加载Whisper模型...")
        self.whisper_model = whisper.load_model("base")  # 使用base模型，更快
        
        # OpenAI客户端
        self._setup_openai_client()
        
        # 工作队列 - 使用单一队列避免复杂性
        self.work_queue = Queue(maxsize=10)
        
        # 启动单一工作线程
        self.worker_thread = Thread(target=self._worker_loop, daemon=False)
        self.worker_thread.start()
        
        print("[系统] 翻译器初始化完成")
        print(f"[系统] VAD设置:")
        print(f"  - 语音阈值: {self.speech_threshold:.5f}")
        print(f"  - 静音超时: {self.silence_timeout}秒")
        print(f"  - 去抖动时间: {self.speech_debounce_time}秒")
        print(f"  - 调试模式: {'开启' if self.debug_vad else '关闭'}")
        print(f"[提示] 如果没有结束检测，检查环境噪声是否在阈值附近波动")
    
    def _setup_openai_client(self):
        """设置OpenAI客户端"""
        api_key = os.getenv('OPENAI_API_KEY')
        if not api_key:
            raise ValueError("请设置环境变量 OPENAI_API_KEY")
        
        # 简化的客户端设置
        self.openai_client = OpenAI(api_key=api_key, timeout=30.0)
        print("[系统] OpenAI客户端已配置")
    
    def process_audio(self, audio_data):
        """处理输入音频 - 修复版VAD（基于时间）"""
        if not self.is_running or len(audio_data) == 0:
            return
        
        # 确保单声道
        if len(audio_data.shape) > 1:
            audio_data = np.mean(audio_data, axis=1)
        
        # 计算音量
        volume = np.sqrt(np.mean(audio_data**2))
        current_time = time.time()
        
        # 添加到缓冲
        self.audio_buffer.extend(audio_data)
        
        with self.state_lock:
            is_speech = volume > self.speech_threshold
            
            # 调试输出音量信息
            if self.debug_vad and int(current_time * 10) % 10 == 0:  # 每秒输出一次
                status = "🎤" if is_speech else "🔇"
                print(f"[VAD调试] {status} 音量:{volume:.5f} 阈值:{self.speech_threshold:.5f} 说话中:{self.is_speaking}")
            
            if is_speech:
                if not self.is_speaking:
                    # 去抖动：需要连续一段时间的语音才开始
                    if not self.speech_start_pending:
                        self.speech_start_pending = True
                        self.speech_start_pending_time = current_time
                    elif current_time - self.speech_start_pending_time >= self.speech_debounce_time:
                        # 连续超过阈值超过debounce时间，确认开始说话
                        self.is_speaking = True
                        self.speech_start_time = self.speech_start_pending_time  # 使用最初检测到的时间
                        self.speech_segments = list(self.audio_buffer)
                        print(f"[VAD] 🎤 开始说话 (音量: {volume:.4f}, 阈值: {self.speech_threshold:.4f})")
                        self.speech_start_pending = False
                        self.last_speech_time = current_time
                else:
                    # 已在说话状态，更新最后语音时间
                    self.last_speech_time = current_time
                    self.speech_segments.extend(audio_data)
            else:
                # 非语音
                if self.speech_start_pending:
                    # 取消pending状态
                    self.speech_start_pending = False
                
                if self.is_speaking:
                    # 静音但正在说话状态
                    self.speech_segments.extend(audio_data)
                    
                    # 检查结束条件（基于时间）
                    silence_duration = current_time - self.last_speech_time
                    total_duration = current_time - self.speech_start_time
                    
                    should_end = False
                    reason = ""
                    
                    # 调试：显示静音计时
                    if self.debug_vad:
                        # 每0.2秒输出一次，且显示更准确的时间
                        if int(silence_duration * 5) != getattr(self, '_last_silence_print', -1):
                            self._last_silence_print = int(silence_duration * 5)
                            print(f"[VAD] 静音计时: {silence_duration:.2f}s / {self.silence_timeout}s (总长:{total_duration:.1f}s)")
                    
                    if silence_duration >= self.silence_timeout:
                        should_end = True
                        reason = f"静音超时({silence_duration:.1f}s)"
                    elif total_duration >= self.max_speech_duration:
                        should_end = True
                        reason = f"语音过长({total_duration:.1f}s)"
                    
                    if should_end:
                        print(f"[VAD] 🔇 说话结束 - {reason}, 总时长: {total_duration:.1f}s")
                        self._process_speech_segment(total_duration)
                        self.is_speaking = False
                        self.speech_segments = []
                        self.speech_start_time = 0
                        self.last_speech_time = 0
    
    def _process_speech_segment(self, duration):
        """处理语音片段"""
        if duration < self.min_speech_duration:
            print(f"[VAD] ⏭️  语音过短({duration:.1f}s < {self.min_speech_duration}s)，跳过处理")
            return
        
        # 转换为numpy数组
        audio_segment = np.array(self.speech_segments, dtype=np.float32)
        
        # 添加到工作队列
        try:
            self.work_queue.put_nowait({
                'type': 'speech',
                'audio': audio_segment
            })
            print(f"[VAD] ✅ 语音片段已加入队列: {len(audio_segment)} samples ({duration:.1f}s)")
        except queue.Full:
            print("[VAD] ❌ 队列满，跳过处理")
        except Exception as e:
            print(f"[VAD] ❌ 队列操作失败: {e}")
    
    def _worker_loop(self):
        """单一工作线程处理所有任务 - 增强错误处理和卡死检测"""
        print(f"[工作线程] 启动，线程ID: {threading.current_thread().ident}")
        consecutive_errors = 0
        max_consecutive_errors = 5

        while self.is_running:
            try:
                # 获取任务
                task = self.work_queue.get(timeout=1.0)

                if task['type'] == 'speech':
                    print(f"[工作线程] 开始处理语音任务，队列剩余: {self.work_queue.qsize()}")
                    task_start_time = time.time()

                    try:
                        # 处理语音 - 添加超时保护
                        self._process_speech_with_timeout(task['audio'], timeout=60)
                        task_duration = time.time() - task_start_time
                        print(f"[工作线程] 语音任务处理完成 (耗时: {task_duration:.1f}s)")
                        consecutive_errors = 0  # 重置错误计数

                    except Exception as speech_error:
                        consecutive_errors += 1
                        print(f"[工作线程] 语音处理错误 ({consecutive_errors}/{max_consecutive_errors}): {speech_error}")

                        # 如果连续错误过多，进入恢复模式
                        if consecutive_errors >= max_consecutive_errors:
                            print(f"[工作线程] 连续错误过多，进入恢复模式...")
                            time.sleep(2)  # 恢复等待
                            consecutive_errors = 0

            except Empty:
                # 超时，继续等待新任务
                continue
            except Exception as e:
                print(f"[工作线程] 严重错误: {e}")
                import traceback
                traceback.print_exc()
                # 发生严重错误时短暂休息，避免快速循环
                time.sleep(0.5)
                continue

        print(f"[工作线程] 已退出")

    def _process_speech_with_timeout(self, audio_segment, timeout=60):
        """带超时保护的语音处理"""
        import concurrent.futures

        with concurrent.futures.ThreadPoolExecutor(max_workers=1) as executor:
            future = executor.submit(self._process_speech, audio_segment)
            try:
                future.result(timeout=timeout)
            except concurrent.futures.TimeoutError:
                print(f"[工作线程] 语音处理超时 ({timeout}s)，跳过此任务")
                raise Exception(f"语音处理超时 ({timeout}s)")

    def _process_speech(self, audio_segment):
        """处理语音：识别->翻译->TTS"""
        try:
            # 1. 语音识别
            print(f"[ASR] 开始识别...")
            text = self._recognize_speech(audio_segment)
            if not text:
                return
            
            # 2. 翻译
            print(f"[翻译] 开始翻译: {text}")
            translated = self._translate_text(text)
            if not translated:
                return
            
            # 3. TTS和播放
            if self.enable_tts:
                print(f"[TTS] 合成: {translated[:50]}...")
                # 确保TTS被调用
                try:
                    self._synthesize_and_play(translated)
                    print(f"[TTS] 完成")
                except Exception as tts_error:
                    print(f"[TTS] 错误: {tts_error}")
                
        except Exception as e:
            print(f"[处理] 错误: {e}")
    
    def _recognize_speech(self, audio_segment):
        """语音识别"""
        try:
            # 转换为float32
            audio_float = audio_segment.astype(np.float32)
            
            # 重采样到16kHz
            if self.sample_rate != 16000:
                import librosa
                audio_16k = librosa.resample(
                    audio_float, 
                    orig_sr=self.sample_rate, 
                    target_sr=16000,
                    res_type='soxr_hq'  # 高质量重采样
                )
            else:
                audio_16k = audio_float
            
            # 使用Whisper识别
            result = self.whisper_model.transcribe(
                audio_16k,
                language=self.source_lang,
                task="transcribe",
                fp16=False
            )
            
            text = result['text'].strip()
            if text:
                print(f"[ASR] 识别结果: {text}")
                return text
                
        except Exception as e:
            print(f"[ASR] 识别失败: {e}")
        return None
    
    def _translate_text(self, text):
        """翻译文本"""
        try:
            response = self.openai_client.chat.completions.create(
                model="gpt-3.5-turbo",
                messages=[
                    {"role": "system", "content": f"Translate {self.source_lang} to {self.target_lang}. Only return the translation."},
                    {"role": "user", "content": text}
                ],
                temperature=0.3,
                max_tokens=200
            )
            
            translated = response.choices[0].message.content.strip()
            print(f"[翻译] 结果: {translated}")
            return translated
            
        except Exception as e:
            print(f"[翻译] 失败: {e}")
        return None
    
    def _synthesize_and_play(self, text):
        """TTS合成和播放 - 修复版：简化事件循环处理"""
        print(f"[TTS] 开始合成语音: {text[:30]}...")

        try:
            # 简化的TTS合成 - 避免事件循环冲突
            def synthesize_sync():
                """同步方式运行异步TTS，避免事件循环冲突"""
                async def synthesize():
                    voice = "en-US-JennyNeural" if self.target_lang == 'en' else "zh-CN-XiaoxiaoNeural"
                    communicate = edge_tts.Communicate(text, voice)
                    audio_data = b""

                    async for chunk in communicate.stream():
                        if chunk["type"] == "audio":
                            audio_data += chunk["data"]
                    return audio_data

                # 始终在新的事件循环中运行，避免冲突
                return asyncio.run(synthesize())

            # 修复: 使用线程池执行TTS，完全隔离事件循环
            print("[TTS] 正在调用Edge TTS...")
            import concurrent.futures
            import threading

            with concurrent.futures.ThreadPoolExecutor(max_workers=1) as executor:
                future = executor.submit(synthesize_sync)
                try:
                    audio_data = future.result(timeout=30)  # 30秒超时
                except concurrent.futures.TimeoutError:
                    print("[TTS] 超时错误：TTS合成超过30秒")
                    return
                except Exception as e:
                    print(f"[TTS] 线程池执行错误: {e}")
                    return
            if not audio_data:
                print("[TTS] 没有音频数据")
                return
            print(f"[TTS] 合成成功，音频大小: {len(audio_data)} bytes")
            
            # 转换音频格式
            audio_segment = AudioSegment.from_mp3(io.BytesIO(audio_data))
            audio_segment = audio_segment.set_frame_rate(48000).set_channels(1)
            samples = audio_segment.get_array_of_samples()
            audio_np = np.array(samples).astype(np.float32) / 32768.0
            
            # 1. 输出到BlackHole
            if self.audio_router:
                try:
                    self.audio_router.send_to_output(audio_np)
                    print(f"[音频] 已输出到BlackHole")
                except Exception as e:
                    print(f"[音频] BlackHole输出错误: {e}")
            
            # 2. 音频播放 - 简化版本，避免复杂的进程管理
            self._play_audio_safe(audio_np)
            
        except Exception as e:
            print(f"[TTS] 错误: {e}")

    def _play_audio_safe(self, audio_np):
        """安全的音频播放方法 - 避免AUHAL错误和进程管理复杂性"""
        try:
            import tempfile
            import os
            import subprocess
            import soundfile as sf

            # 保存到临时文件
            with tempfile.NamedTemporaryFile(suffix='.wav', delete=False) as temp_file:
                temp_path = temp_file.name
                sf.write(temp_path, audio_np, 48000)

            # 使用afplay播放 - 简化版本
            try:
                # 同步播放，避免复杂的异步清理
                result = subprocess.run(
                    ['afplay', temp_path],
                    stdout=subprocess.DEVNULL,
                    stderr=subprocess.DEVNULL,
                    timeout=10,  # 10秒超时
                    check=False  # 不检查返回码
                )
                print(f"[音频] ✅ afplay播放完成 (返回码: {result.returncode})")

            except subprocess.TimeoutExpired:
                print("[音频] ⚠️ afplay播放超时")
            except Exception as e:
                print(f"[音频] afplay播放失败: {e}")
            finally:
                # 清理临时文件
                try:
                    os.remove(temp_path)
                except:
                    pass

        except Exception as e:
            print(f"[音频] 播放准备失败: {e}")

    def shutdown(self):
        """关闭翻译器"""
        print("[系统] 开始关闭...")
        self.is_running = False
        
        # 等待工作线程结束
        if self.worker_thread.is_alive():
            self.worker_thread.join(timeout=2)
        
        print("[系统] 已关闭")
    
    def reset_system(self):
        """重置VAD系统状态 - 增强版本，检查工作线程健康"""
        with self.state_lock:
            self.is_speaking = False
            self.speech_segments = []
            self.speech_start_time = 0
            self.last_speech_time = 0
            self.speech_start_pending = False
            self.speech_start_pending_time = 0

            # 清空音频缓冲区
            self.audio_buffer.clear()

            # 清空工作队列
            queue_size = self.work_queue.qsize()
            while not self.work_queue.empty():
                try:
                    self.work_queue.get_nowait()
                except:
                    break

            print(f"[系统] VAD状态已重置，清空了 {queue_size} 个队列项目")

            # 检查工作线程健康状态
            if hasattr(self, 'worker_thread') and self.worker_thread:
                if self.worker_thread.is_alive():
                    print(f"[系统] 工作线程运行正常 (ID: {self.worker_thread.ident})")
                else:
                    print(f"[系统] ⚠️  工作线程已死亡，尝试重启...")
                    try:
                        # 重启工作线程
                        self.worker_thread = Thread(target=self._worker_loop, daemon=False)
                        self.worker_thread.start()
                        print(f"[系统] ✅ 工作线程已重启 (新ID: {self.worker_thread.ident})")
                    except Exception as e:
                        print(f"[系统] ❌ 工作线程重启失败: {e}")
            else:
                print(f"[系统] ⚠️  工作线程未初始化")

# 替换原有类
RealtimeTranslator = RealtimeTranslatorFixed