#!/usr/bin/env python3
"""
诊断音频输入问题 - 找出为什么没说话也有翻译结果
"""
import sounddevice as sd
import numpy as np
import time
import matplotlib.pyplot as plt
from collections import deque

class AudioDiagnostic:
    def __init__(self):
        self.sample_rate = 48000
        self.channels = 2
        self.blocksize = 1024
        
        # 找到BlackHole设备
        self.blackhole_in = self._find_blackhole()
        
        # 数据收集
        self.volume_history = deque(maxlen=1000)  # 保存最近1000个音量值
        self.audio_samples = deque(maxlen=48000)  # 保存最近1秒的音频
        
    def _find_blackhole(self):
        """查找BlackHole设备"""
        devices = sd.query_devices()
        blackhole_in = None
        
        print("=== 可用音频设备 ===")
        for i, device in enumerate(devices):
            device_type = []
            if device['max_input_channels'] > 0:
                device_type.append("输入")
            if device['max_output_channels'] > 0:
                device_type.append("输出")
            
            print(f"{i:2d}: {device['name']} ({'/'.join(device_type)})")
            
            # 查找BlackHole输入设备
            if 'BlackHole' in device['name'] and device['max_input_channels'] > 0:
                blackhole_in = i
                print(f"    ✅ 找到BlackHole输入设备")
        
        return blackhole_in
    
    def analyze_audio_levels(self, duration=30):
        """分析音频电平，找出噪声来源"""
        print(f"\n=== 开始 {duration} 秒音频分析 ===")
        print("请保持安静，不要说话...")
        
        if self.blackhole_in is None:
            print("❌ 未找到BlackHole设备")
            return
        
        start_time = time.time()
        volume_data = []
        time_data = []
        
        def audio_callback(indata, frames, time_info, status):
            if status:
                print(f"音频状态: {status}")
            
            # 计算音量
            volume = np.sqrt(np.mean(indata**2))
            current_time = time.time() - start_time
            
            volume_data.append(volume)
            time_data.append(current_time)
            self.volume_history.append(volume)
            
            # 保存音频样本
            mono_audio = np.mean(indata, axis=1) if len(indata.shape) > 1 else indata
            self.audio_samples.extend(mono_audio)
            
            # 实时显示
            bar_length = int(volume * 100)
            bar = '█' * min(bar_length, 50) + '░' * max(0, 50 - bar_length)
            print(f'\r时间:{current_time:6.1f}s 音量:[{bar}] {volume:.6f}', end='', flush=True)
        
        # 开始录音
        with sd.InputStream(
            device=self.blackhole_in,
            channels=self.channels,
            samplerate=self.sample_rate,
            blocksize=self.blocksize,
            dtype='float32',
            callback=audio_callback
        ):
            time.sleep(duration)
        
        print(f"\n\n=== 分析结果 ===")
        
        # 统计分析
        volumes = np.array(volume_data)
        print(f"音量统计:")
        print(f"  最小值: {np.min(volumes):.6f}")
        print(f"  最大值: {np.max(volumes):.6f}")
        print(f"  平均值: {np.mean(volumes):.6f}")
        print(f"  标准差: {np.std(volumes):.6f}")
        print(f"  95%分位: {np.percentile(volumes, 95):.6f}")
        print(f"  99%分位: {np.percentile(volumes, 99):.6f}")
        
        # 当前阈值分析
        current_threshold = 0.008
        above_threshold = volumes > current_threshold
        above_count = np.sum(above_threshold)
        above_percentage = (above_count / len(volumes)) * 100
        
        print(f"\n当前阈值分析 (阈值: {current_threshold:.6f}):")
        print(f"  超过阈值的样本: {above_count}/{len(volumes)} ({above_percentage:.1f}%)")
        
        if above_percentage > 5:  # 如果超过5%的时间都在阈值以上
            print(f"  ⚠️  警告: {above_percentage:.1f}% 的时间音量超过阈值!")
            print(f"  这可能导致误触发语音检测")
            
            # 建议新阈值
            suggested_threshold = np.percentile(volumes, 99.5)  # 99.5%分位数
            print(f"  建议阈值: {suggested_threshold:.6f}")
        else:
            print(f"  ✅ 阈值设置合理")
        
        # 检查是否有持续的音频输入
        if np.mean(volumes) > 0.001:
            print(f"\n⚠️  检测到持续的音频输入!")
            print(f"可能的原因:")
            print(f"  1. 其他应用正在播放音频到BlackHole")
            print(f"  2. 系统音频被路由到BlackHole")
            print(f"  3. 麦克风有底噪")
            print(f"  4. BlackHole配置问题")
        
        return {
            'volumes': volumes,
            'times': time_data,
            'stats': {
                'min': np.min(volumes),
                'max': np.max(volumes),
                'mean': np.mean(volumes),
                'std': np.std(volumes),
                'p95': np.percentile(volumes, 95),
                'p99': np.percentile(volumes, 99)
            },
            'above_threshold_percentage': above_percentage,
            'suggested_threshold': np.percentile(volumes, 99.5)
        }
    
    def check_audio_sources(self):
        """检查可能的音频来源"""
        print("\n=== 检查音频来源 ===")
        
        # 检查系统音频设置
        try:
            import subprocess
            result = subprocess.run(['system_profiler', 'SPAudioDataType'], 
                                  capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                output = result.stdout
                if 'BlackHole' in output:
                    print("✅ 系统中找到BlackHole设备")
                else:
                    print("❌ 系统中未找到BlackHole设备")
        except:
            print("⚠️  无法检查系统音频配置")
        
        # 检查当前音频会话
        print("\n建议检查项目:")
        print("1. 打开 音频MIDI设置 应用")
        print("2. 检查BlackHole设备的输入源")
        print("3. 确认没有其他应用使用BlackHole作为输出")
        print("4. 检查系统偏好设置 → 声音 → 输入/输出设置")

def main():
    print("=== 音频输入诊断工具 ===")
    print("此工具将帮助您找出为什么没说话也有翻译结果")
    
    diagnostic = AudioDiagnostic()
    
    if diagnostic.blackhole_in is None:
        print("\n❌ 未找到BlackHole设备，请先安装BlackHole")
        return
    
    # 检查音频来源
    diagnostic.check_audio_sources()
    
    # 分析音频电平
    print(f"\n即将开始30秒的音频分析...")
    input("按Enter开始分析...")
    
    result = diagnostic.analyze_audio_levels(30)
    
    if result:
        print(f"\n=== 建议 ===")
        if result['above_threshold_percentage'] > 5:
            print(f"1. 调高语音阈值到 {result['suggested_threshold']:.6f}")
            print(f"2. 检查是否有其他应用在使用BlackHole")
            print(f"3. 确认BlackHole没有接收到不需要的音频")
        else:
            print(f"1. 当前阈值设置合理")
            print(f"2. 问题可能在其他地方，建议检查VAD逻辑")

if __name__ == "__main__":
    main()
