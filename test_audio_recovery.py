#!/usr/bin/env python3
"""
测试音频流自动恢复功能
"""
import time
import os
from obs_ai_translator_main import AITranslatorSystem

def test_audio_recovery():
    """测试音频流恢复功能"""
    print("=== 音频流恢复测试 ===")
    
    # 检查环境变量
    if not os.getenv('OPENAI_API_KEY'):
        print("❌ 请先设置 OPENAI_API_KEY 环境变量")
        return False
    
    try:
        # 创建系统实例
        system = AITranslatorSystem("")
        
        print("✅ 系统初始化成功")
        print("\n测试说明:")
        print("1. 系统将启动音频捕获")
        print("2. 如果音频流中断，系统会自动重启")
        print("3. 您可以按 'a' + Enter 手动重启音频流")
        print("4. 按 'q' + Enter 退出测试")
        print()
        
        # 启动系统
        system.start(test_mode=True)
        
        return True
        
    except KeyboardInterrupt:
        print("\n用户中断测试")
        return True
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("音频流自动恢复功能测试")
    print("此测试将验证系统在音频流中断后的自动恢复能力")
    print()
    
    success = test_audio_recovery()
    
    if success:
        print("\n✅ 测试完成")
        print("\n修复内容:")
        print("1. ✅ 音频流自动重启机制")
        print("2. ✅ 音频流健康检查")
        print("3. ✅ 手动重启命令 ('a' + Enter)")
        print("4. ✅ 增强的错误处理")
    else:
        print("\n❌ 测试失败")

if __name__ == "__main__":
    main()
