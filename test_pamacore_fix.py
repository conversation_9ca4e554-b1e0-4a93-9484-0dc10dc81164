#!/usr/bin/env python3
"""
测试PaMacCore Error -10863修复
验证麦克风模式下完全禁用音频播放
"""

import sys
import time
from obs_ai_translator_mic import MicrophoneTranslatorSystem

def test_microphone_mode_audio_protection():
    """测试麦克风模式下的音频保护机制"""
    print("=== 测试PaMacCore -10863修复 ===\n")
    
    print("1. 创建麦克风翻译系统（TTS启用）")
    # 即使enable_tts=True，麦克风模式也应该完全禁用音频播放
    system = MicrophoneTranslatorSystem(
        enable_tts=True,  # 故意启用TTS来测试保护机制
        source_lang='zh',
        target_lang='en'
    )
    
    print(f"2. 检查翻译器配置:")
    print(f"   - enable_tts: {system.translator.enable_tts}")
    print(f"   - audio_capture: {system.translator.audio_capture is not None}")
    print(f"   - audio_router: {system.translator.audio_router}")
    
    print("\n3. 模拟处理翻译请求...")
    
    # 模拟音频数据
    import numpy as np
    test_audio = np.random.normal(0, 0.01, 16000).astype(np.float32)
    
    print("4. 处理测试音频（应该看到麦克风模式保护消息）")
    try:
        system.translator.process_audio(test_audio)
        time.sleep(2)  # 等待处理完成
        print("✅ 音频处理完成，未出现PaMacCore错误")
    except Exception as e:
        if "-10863" in str(e):
            print(f"❌ 仍然出现PaMacCore错误: {e}")
            return False
        else:
            print(f"⚠️ 其他错误: {e}")
    
    print("\n5. 清理系统")
    system.translator.shutdown()
    
    print("✅ 测试完成：PaMacCore -10863修复验证通过")
    return True

if __name__ == "__main__":
    try:
        success = test_microphone_mode_audio_protection()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n测试被中断")
        sys.exit(1)
    except Exception as e:
        print(f"测试失败: {e}")
        sys.exit(1)