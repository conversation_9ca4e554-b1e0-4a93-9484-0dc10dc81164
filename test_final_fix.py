#!/usr/bin/env python3
"""
最终修复测试：VAD检测和TTS播报
"""

import sys
import time
import numpy as np
from obs_ai_translator_mic import MicrophoneTranslatorSystem

def test_tts_playback():
    """直接测试TTS播放功能"""
    print("=== 测试TTS播放功能 ===\n")
    
    system = MicrophoneTranslatorSystem(
        enable_tts=True,
        source_lang='zh',
        target_lang='en'
    )
    
    try:
        print("直接测试TTS合成和播放...")
        
        test_texts = [
            "Hello, this is a test.",
            "The weather is nice today."
        ]
        
        for i, text in enumerate(test_texts, 1):
            print(f"\n{i}. 播放: {text}")
            system.translator._synthesize_and_play(text)
            print("   等待播放完成...")
            time.sleep(5)  # 等待播放完成
        
        print("\n请确认是否听到了英文语音播报")
        
    except Exception as e:
        print(f"❌ TTS测试失败: {e}")
        return False
    finally:
        system.translator.shutdown()
    
    return True

def test_low_volume_vad():
    """测试低阈值VAD检测"""
    print("\n=== 测试低阈值VAD检测 ===\n")
    
    system = MicrophoneTranslatorSystem(
        enable_tts=False,  # 简化测试
        source_lang='zh',
        target_lang='en'
    )
    
    try:
        print(f"新的VAD阈值: {system.translator.speech_threshold}")
        
        # 生成低音量测试信号
        sample_rate = 48000
        duration = 1.5
        t = np.linspace(0, duration, int(sample_rate * duration), False)
        
        # 使用0.004的音量（之前检测不到的）
        audio = 0.004 * np.sin(2 * np.pi * 300 * t).astype(np.float32)
        envelope = np.hanning(len(audio))
        audio = audio * envelope
        
        actual_volume = np.sqrt(np.mean(audio**2))
        print(f"测试音频音量: {actual_volume:.4f}")
        print(f"应该{'能' if actual_volume > system.translator.speech_threshold else '不能'}检测到")
        
        start_time = time.time()
        
        # 发送音频
        chunk_size = 4800
        for i in range(0, len(audio), chunk_size):
            chunk = audio[i:i+chunk_size]
            system.translator.process_audio(chunk)
            time.sleep(0.02)
        
        print("音频发送完成，等待VAD结束检测...")
        
        # 发送静音
        silence = np.zeros(4800, dtype=np.float32)
        for i in range(12):  # 1.2秒静音
            system.translator.process_audio(silence)
            time.sleep(0.1)
            if not system.translator.is_speaking:
                break
        
        end_time = time.time()
        total_time = end_time - start_time
        
        if system.translator.work_queue.qsize() > 0:
            print(f"✅ 低音量检测成功! 耗时: {total_time:.1f}s")
            return True
        else:
            print(f"❌ 低音量检测失败，耗时: {total_time:.1f}s")
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False
    finally:
        system.translator.shutdown()

def main():
    """主测试"""
    print("=== 关键问题修复验证 ===\n")
    print("问题1: 用户说话没反应 → VAD阈值 0.006→0.003")
    print("问题2: TTS没有播报 → 强制本地播放")
    print("\n" + "="*50 + "\n")
    
    results = []
    
    # 测试1: TTS播放
    results.append(("TTS播放", test_tts_playback()))
    
    # 测试2: 低音量VAD
    results.append(("低音量VAD", test_low_volume_vad()))
    
    # 结果
    print("\n" + "="*50)
    print("修复验证结果")
    print("="*50)
    
    for name, result in results:
        status = "✅ 修复成功" if result else "❌ 仍有问题"
        print(f"{name:<12} {status}")
    
    passed = sum(1 for _, r in results if r)
    print(f"\n修复完成度: {passed}/{len(results)}")

if __name__ == "__main__":
    main()