#!/usr/bin/env python3
"""
Aggressive Freeze Testing
使用更激进的测试策略来重现冻结问题
"""

import sys
import time
import threading
import numpy as np
import logging
import signal
from datetime import datetime
import concurrent.futures

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('aggressive_freeze_test.log')
    ]
)

class AggressiveAudioGenerator:
    """激进的音频数据生成器"""
    
    @staticmethod
    def generate_complex_speech(duration_seconds: float, sample_rate: int = 48000) -> np.ndarray:
        """生成复杂的语音信号"""
        samples = int(duration_seconds * sample_rate)
        t = np.linspace(0, duration_seconds, samples)
        
        # 复杂的多频信号（模拟真实语音）
        base_freq = 120  # 基频
        signal = np.zeros(samples)
        
        # 添加多个谐波
        for harmonic in [1, 2, 3, 4, 5]:
            freq = base_freq * harmonic
            amplitude = 0.3 / harmonic
            signal += amplitude * np.sin(2 * np.pi * freq * t)
        
        # 添加语音特有的共振峰
        formant1 = 0.2 * np.sin(2 * np.pi * 800 * t)  # 第一共振峰
        formant2 = 0.15 * np.sin(2 * np.pi * 1200 * t)  # 第二共振峰
        signal += formant1 + formant2
        
        # 添加语音的时间变化
        modulation = np.sin(2 * np.pi * 4 * t)
        signal *= (0.7 + 0.3 * modulation)
        
        # 添加噪声
        noise = 0.05 * np.random.normal(0, 1, samples)
        signal += noise
        
        # 动态音量包络
        envelope = np.exp(-0.5 * ((t - duration_seconds/2) / (duration_seconds/4))**2)
        signal *= envelope
        
        return signal.astype(np.float32)
    
    @staticmethod
    def generate_overlapping_speech(duration_seconds: float, sample_rate: int = 48000) -> np.ndarray:
        """生成重叠的语音信号（模拟连续说话）"""
        samples = int(duration_seconds * sample_rate)
        t = np.linspace(0, duration_seconds, samples)
        
        signal = np.zeros(samples)
        
        # 生成多个重叠的语音段
        num_segments = int(duration_seconds * 2)  # 每0.5秒一段
        
        for i in range(num_segments):
            start_time = i * 0.3  # 重叠开始
            segment_duration = 1.0  # 每段1秒
            
            if start_time + segment_duration <= duration_seconds:
                start_sample = int(start_time * sample_rate)
                segment_samples = int(segment_duration * sample_rate)
                
                # 生成该段的语音
                segment_t = np.linspace(0, segment_duration, segment_samples)
                base_freq = 150 + i * 10  # 频率稍有变化
                segment_signal = 0.2 * np.sin(2 * np.pi * base_freq * segment_t)
                
                # 添加到总信号中
                end_sample = min(start_sample + segment_samples, samples)
                actual_samples = end_sample - start_sample
                signal[start_sample:end_sample] += segment_signal[:actual_samples]
        
        return signal.astype(np.float32)

class ThreadDeadlockDetector:
    """线程死锁检测器"""
    
    def __init__(self):
        self.thread_states = {}
        self.monitoring = False
        
    def start_monitoring(self):
        """开始监控线程状态"""
        self.monitoring = True
        self.monitor_thread = threading.Thread(target=self._monitor_threads, daemon=True)
        self.monitor_thread.start()
        logging.info("线程死锁检测器启动")
        
    def stop_monitoring(self):
        """停止监控"""
        self.monitoring = False
        
    def _monitor_threads(self):
        """监控线程状态变化"""
        while self.monitoring:
            try:
                current_threads = {}
                
                for thread in threading.enumerate():
                    thread_info = {
                        'name': thread.name,
                        'ident': thread.ident,
                        'is_alive': thread.is_alive(),
                        'daemon': thread.daemon
                    }
                    current_threads[thread.ident] = thread_info
                
                # 检测长时间无响应的线程
                current_time = time.time()
                
                for thread_id, info in current_threads.items():
                    if thread_id not in self.thread_states:
                        self.thread_states[thread_id] = {
                            'last_seen': current_time,
                            'info': info
                        }
                    else:
                        # 更新最后看到时间
                        self.thread_states[thread_id]['last_seen'] = current_time
                
                # 检查是否有线程长时间无响应
                for thread_id, state in self.thread_states.items():
                    inactive_time = current_time - state['last_seen']
                    if inactive_time > 30:  # 30秒无响应
                        logging.warning(f"线程可能死锁: {state['info']['name']} (无响应 {inactive_time:.1f}秒)")
                
                time.sleep(2)
                
            except Exception as e:
                logging.error(f"线程监控错误: {e}")
                time.sleep(1)

def test_rapid_consecutive_inputs():
    """快速连续输入测试"""
    logging.info("=== 快速连续输入测试 ===")
    
    # 启动死锁检测器
    deadlock_detector = ThreadDeadlockDetector()
    deadlock_detector.start_monitoring()
    
    try:
        from obs_ai_translator_mic import MicrophoneTranslatorSystem
        
        logging.info("创建翻译系统...")
        system = MicrophoneTranslatorSystem(
            obs_password="",
            enable_tts=False
        )
        
        # 非常快速的连续输入
        logging.info("开始快速连续输入测试...")
        
        for round_num in range(10):  # 10轮快速输入
            logging.info(f"快速输入第 {round_num + 1} 轮")
            
            # 生成复杂语音数据
            audio_data = AggressiveAudioGenerator.generate_complex_speech(1.5)
            
            # 很小的块size，模拟高频率输入
            chunk_size = 256
            for i in range(0, len(audio_data), chunk_size):
                chunk = audio_data[i:i+chunk_size]
                if len(chunk) > 0:
                    system.translator.process_audio(chunk)
                
                # 极短的间隔
                time.sleep(0.005)  # 5ms
            
            # 检查队列状态
            rec_size = system.translator.recognition_queue.qsize()
            trans_size = system.translator.translation_queue.qsize()
            
            logging.info(f"第 {round_num + 1} 轮后队列状态: ASR={rec_size}, 翻译={trans_size}")
            
            # 如果队列满了，可能出现问题
            if rec_size >= 9:  # 接近maxsize=10
                logging.warning("ASR队列接近满载！")
            
            # 很短的轮间间隔
            time.sleep(0.1)
        
        logging.info("等待处理完成...")
        time.sleep(5)
        
        logging.info("✅ 快速连续输入测试完成")
        return True
        
    except Exception as e:
        logging.error(f"快速连续输入测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        deadlock_detector.stop_monitoring()

def test_overlapping_speech():
    """重叠语音测试"""
    logging.info("=== 重叠语音测试 ===")
    
    try:
        from obs_ai_translator_mic import MicrophoneTranslatorSystem
        
        system = MicrophoneTranslatorSystem(
            obs_password="",
            enable_tts=False
        )
        
        # 生成长时间重叠语音
        logging.info("生成重叠语音数据...")
        overlapping_audio = AggressiveAudioGenerator.generate_overlapping_speech(8.0)  # 8秒重叠语音
        
        logging.info("输入重叠语音数据...")
        chunk_size = 1024
        for i in range(0, len(overlapping_audio), chunk_size):
            chunk = overlapping_audio[i:i+chunk_size]
            if len(chunk) > 0:
                system.translator.process_audio(chunk)
            
            time.sleep(0.02)  # 20ms间隔
        
        # 等待处理
        logging.info("等待重叠语音处理完成...")
        time.sleep(10)
        
        # 检查最终状态
        final_rec_size = system.translator.recognition_queue.qsize()
        final_trans_size = system.translator.translation_queue.qsize()
        logging.info(f"重叠语音处理后队列状态: ASR={final_rec_size}, 翻译={final_trans_size}")
        
        logging.info("✅ 重叠语音测试完成")
        return True
        
    except Exception as e:
        logging.error(f"重叠语音测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_memory_pressure():
    """内存压力测试"""
    logging.info("=== 内存压力测试 ===")
    
    try:
        from obs_ai_translator_mic import MicrophoneTranslatorSystem
        
        system = MicrophoneTranslatorSystem(
            obs_password="",
            enable_tts=False
        )
        
        # 生成大量音频数据在内存中
        logging.info("生成大量音频数据...")
        audio_chunks = []
        
        for i in range(50):  # 生成50段音频
            chunk = AggressiveAudioGenerator.generate_complex_speech(2.0)
            audio_chunks.append(chunk)
        
        logging.info(f"生成了 {len(audio_chunks)} 段音频数据")
        
        # 快速输入所有数据
        logging.info("快速输入大量数据...")
        for i, audio_data in enumerate(audio_chunks):
            logging.info(f"输入第 {i+1}/{len(audio_chunks)} 段")
            
            # 分块输入
            sub_chunk_size = 512
            for j in range(0, len(audio_data), sub_chunk_size):
                sub_chunk = audio_data[j:j+sub_chunk_size]
                if len(sub_chunk) > 0:
                    system.translator.process_audio(sub_chunk)
                
                # 很短间隔
                time.sleep(0.001)  # 1ms
            
            # 检查内存使用情况
            if i % 10 == 0:
                try:
                    import psutil
                    process = psutil.Process()
                    memory_mb = process.memory_info().rss / 1024 / 1024
                    logging.info(f"当前内存使用: {memory_mb:.1f} MB")
                    
                    if memory_mb > 500:  # 超过500MB
                        logging.warning("内存使用过高！")
                        
                except ImportError:
                    pass
        
        logging.info("等待内存压力测试处理完成...")
        time.sleep(15)
        
        logging.info("✅ 内存压力测试完成")
        return True
        
    except Exception as e:
        logging.error(f"内存压力测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_concurrent_processing():
    """并发处理测试"""
    logging.info("=== 并发处理测试 ===")
    
    try:
        from obs_ai_translator_mic import MicrophoneTranslatorSystem
        
        system = MicrophoneTranslatorSystem(
            obs_password="",
            enable_tts=False
        )
        
        def input_worker(worker_id: int, num_inputs: int):
            """输入工作线程"""
            try:
                for i in range(num_inputs):
                    audio_data = AggressiveAudioGenerator.generate_complex_speech(1.0)
                    
                    chunk_size = 512
                    for j in range(0, len(audio_data), chunk_size):
                        chunk = audio_data[j:j+chunk_size]
                        if len(chunk) > 0:
                            system.translator.process_audio(chunk)
                        time.sleep(0.01)
                    
                    logging.info(f"工作线程 {worker_id} 完成输入 {i+1}")
                    time.sleep(0.5)
                    
            except Exception as e:
                logging.error(f"工作线程 {worker_id} 错误: {e}")
        
        # 启动多个并发输入线程
        logging.info("启动并发输入线程...")
        with concurrent.futures.ThreadPoolExecutor(max_workers=3) as executor:
            # 3个线程，每个输入5次
            futures = [
                executor.submit(input_worker, i, 5)
                for i in range(3)
            ]
            
            # 等待所有线程完成
            for i, future in enumerate(futures):
                try:
                    future.result(timeout=60)  # 1分钟超时
                    logging.info(f"并发线程 {i} 完成")
                except Exception as e:
                    logging.error(f"并发线程 {i} 异常: {e}")
        
        logging.info("等待并发处理完成...")
        time.sleep(10)
        
        logging.info("✅ 并发处理测试完成")
        return True
        
    except Exception as e:
        logging.error(f"并发处理测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_edge_cases():
    """边界情况测试"""
    logging.info("=== 边界情况测试 ===")
    
    try:
        from obs_ai_translator_mic import MicrophoneTranslatorSystem
        
        system = MicrophoneTranslatorSystem(
            obs_password="",
            enable_tts=False
        )
        
        # 测试1: 空数据
        logging.info("测试空音频数据...")
        empty_chunk = np.array([], dtype=np.float32)
        system.translator.process_audio(empty_chunk)
        
        # 测试2: 极小数据
        logging.info("测试极小音频数据...")
        tiny_chunk = np.array([0.001, -0.001], dtype=np.float32)
        system.translator.process_audio(tiny_chunk)
        
        # 测试3: 极大音量
        logging.info("测试极大音量数据...")
        loud_chunk = np.full(1024, 0.99, dtype=np.float32)
        system.translator.process_audio(loud_chunk)
        
        # 测试4: NaN值
        logging.info("测试包含NaN的数据...")
        nan_chunk = np.full(1024, 0.1, dtype=np.float32)
        nan_chunk[100] = np.nan
        system.translator.process_audio(nan_chunk)
        
        # 测试5: 无限值
        logging.info("测试包含无限值的数据...")
        inf_chunk = np.full(1024, 0.1, dtype=np.float32)
        inf_chunk[200] = np.inf
        system.translator.process_audio(inf_chunk)
        
        time.sleep(3)
        
        logging.info("✅ 边界情况测试完成")
        return True
        
    except Exception as e:
        logging.error(f"边界情况测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("AI翻译系统 - 激进冻结测试")
    print("使用更激进的策略来尝试重现冻结问题")
    print("开始测试...\n")
    
    # 设置信号处理器
    def signal_handler(sig, frame):
        logging.info("接收到中断信号，正在退出...")
        sys.exit(0)
    
    signal.signal(signal.SIGINT, signal_handler)
    
    test_results = []
    
    # 测试1: 快速连续输入
    print("【测试1】快速连续输入测试")
    try:
        result1 = test_rapid_consecutive_inputs()
        test_results.append(("快速连续输入", result1))
        print(f"结果: {'通过' if result1 else '失败'}\n")
    except Exception as e:
        logging.error(f"快速连续输入测试异常: {e}")
        test_results.append(("快速连续输入", False))
        print("结果: 异常\n")
    
    time.sleep(5)  # 恢复间隔
    
    # 测试2: 重叠语音测试
    print("【测试2】重叠语音测试")
    try:
        result2 = test_overlapping_speech()
        test_results.append(("重叠语音测试", result2))
        print(f"结果: {'通过' if result2 else '失败'}\n")
    except Exception as e:
        logging.error(f"重叠语音测试异常: {e}")
        test_results.append(("重叠语音测试", False))
        print("结果: 异常\n")
    
    time.sleep(5)  # 恢复间隔
    
    # 测试3: 内存压力测试
    print("【测试3】内存压力测试")
    try:
        result3 = test_memory_pressure()
        test_results.append(("内存压力测试", result3))
        print(f"结果: {'通过' if result3 else '失败'}\n")
    except Exception as e:
        logging.error(f"内存压力测试异常: {e}")
        test_results.append(("内存压力测试", False))
        print("结果: 异常\n")
    
    time.sleep(5)  # 恢复间隔
    
    # 测试4: 并发处理测试
    print("【测试4】并发处理测试")
    try:
        result4 = test_concurrent_processing()
        test_results.append(("并发处理测试", result4))
        print(f"结果: {'通过' if result4 else '失败'}\n")
    except Exception as e:
        logging.error(f"并发处理测试异常: {e}")
        test_results.append(("并发处理测试", False))
        print("结果: 异常\n")
    
    time.sleep(3)  # 恢复间隔
    
    # 测试5: 边界情况测试
    print("【测试5】边界情况测试")
    try:
        result5 = test_edge_cases()
        test_results.append(("边界情况测试", result5))
        print(f"结果: {'通过' if result5 else '失败'}\n")
    except Exception as e:
        logging.error(f"边界情况测试异常: {e}")
        test_results.append(("边界情况测试", False))
        print("结果: 异常\n")
    
    # 总结
    print("=" * 50)
    print("激进测试总结:")
    print("=" * 50)
    
    passed = sum(1 for _, result in test_results if result)
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
    
    print(f"\n总体结果: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("✅ 所有激进测试通过，系统在极限条件下表现稳定")
    else:
        print("⚠️  部分激进测试失败，可能发现了系统的限制或问题")
        print("请查看日志文件 aggressive_freeze_test.log 获取详细信息")
    
    return 0 if passed == total else 1

if __name__ == "__main__":
    sys.exit(main())