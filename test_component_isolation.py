#!/usr/bin/env python3
"""
Individual Component Testing Script
专门测试各个组件的独立功能，用于隔离问题
"""

import sys
import time
import logging
import traceback
import threading
import numpy as np
from queue import Queue
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(name)s: %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('component_test.log')
    ]
)

class ComponentTester:
    """组件独立测试器"""
    
    def __init__(self):
        self.test_results = {}
        
    def test_whisper_asr_isolated(self):
        """测试Whisper ASR组件独立运行"""
        logging.info("=== 测试Whisper ASR组件 ===")
        
        try:
            import whisper
            
            # 测试模型加载
            logging.info("加载Whisper模型...")
            start_time = time.time()
            model = whisper.load_model("tiny")  # 使用最小模型测试
            load_time = time.time() - start_time
            logging.info(f"模型加载耗时: {load_time:.2f}秒")
            
            # 生成测试音频
            logging.info("生成测试音频...")
            sample_rate = 16000
            duration = 3.0
            t = np.linspace(0, duration, int(sample_rate * duration))
            # 生成简单的正弦波作为测试音频
            frequency = 440  # A4 音符
            audio = 0.3 * np.sin(2 * np.pi * frequency * t).astype(np.float32)
            
            # 测试识别
            logging.info("开始语音识别...")
            start_time = time.time()
            result = model.transcribe(
                audio, 
                language='zh', 
                fp16=False,
                verbose=False,
                no_speech_threshold=0.6,
                logprob_threshold=-1.0
            )
            transcribe_time = time.time() - start_time
            
            logging.info(f"识别结果: '{result['text']}'")
            logging.info(f"识别耗时: {transcribe_time:.2f}秒")
            logging.info(f"no_speech_prob: {result.get('no_speech_prob', 'N/A')}")
            
            self.test_results['whisper_asr'] = {
                'status': 'passed',
                'load_time': load_time,
                'transcribe_time': transcribe_time,
                'result': result['text'],
                'no_speech_prob': result.get('no_speech_prob', 0)
            }
            
        except Exception as e:
            logging.error(f"Whisper ASR测试失败: {e}")
            traceback.print_exc()
            self.test_results['whisper_asr'] = {
                'status': 'failed',
                'error': str(e)
            }
    
    def test_openai_translation_isolated(self):
        """测试OpenAI翻译组件独立运行"""
        logging.info("=== 测试OpenAI翻译组件 ===")
        
        try:
            import os
            from openai import OpenAI
            
            # 检查API密钥
            api_key = os.getenv('OPENAI_API_KEY')
            if not api_key:
                raise ValueError("缺少OPENAI_API_KEY环境变量")
            
            client = OpenAI(api_key=api_key)
            
            # 测试翻译
            test_texts = [
                "你好，这是一个测试句子。",
                "今天天气很好。",
                "我正在测试翻译功能。"
            ]
            
            results = []
            
            for i, text in enumerate(test_texts):
                logging.info(f"翻译测试 {i+1}: '{text}'")
                
                start_time = time.time()
                
                response = client.chat.completions.create(
                    model="gpt-4o-mini",
                    messages=[
                        {"role": "system", "content": "你是一个专业的翻译员。请将中文翻译成英文。只返回翻译结果，不要额外说明。"},
                        {"role": "user", "content": text}
                    ],
                    temperature=0.1,
                    max_tokens=300,
                    timeout=30  # 设置超时
                )
                
                translation_time = time.time() - start_time
                translated = response.choices[0].message.content.strip()
                
                logging.info(f"翻译结果: '{translated}' (耗时: {translation_time:.2f}秒)")
                
                results.append({
                    'original': text,
                    'translated': translated,
                    'time': translation_time
                })
                
                # 添加延迟避免API限制
                time.sleep(1)
            
            avg_time = sum(r['time'] for r in results) / len(results)
            
            self.test_results['openai_translation'] = {
                'status': 'passed',
                'test_count': len(results),
                'average_time': avg_time,
                'results': results
            }
            
        except Exception as e:
            logging.error(f"OpenAI翻译测试失败: {e}")
            traceback.print_exc()
            self.test_results['openai_translation'] = {
                'status': 'failed',
                'error': str(e)
            }
    
    def test_edge_tts_isolated(self):
        """测试Edge TTS组件独立运行"""
        logging.info("=== 测试Edge TTS组件 ===")
        
        try:
            import edge_tts
            import asyncio
            
            async def test_tts():
                test_texts = [
                    "Hello, this is a test.",
                    "Today is a beautiful day.",
                    "Testing text-to-speech functionality."
                ]
                
                results = []
                
                for i, text in enumerate(test_texts):
                    logging.info(f"TTS测试 {i+1}: '{text}'")
                    
                    start_time = time.time()
                    
                    voice = "en-US-JennyNeural"
                    communicate = edge_tts.Communicate(text, voice)
                    
                    audio_data = b""
                    async for chunk in communicate.stream():
                        if chunk["type"] == "audio":
                            audio_data += chunk["data"]
                    
                    synthesis_time = time.time() - start_time
                    audio_size = len(audio_data)
                    
                    logging.info(f"TTS完成: 音频大小 {audio_size} 字节 (耗时: {synthesis_time:.2f}秒)")
                    
                    results.append({
                        'text': text,
                        'audio_size': audio_size,
                        'time': synthesis_time
                    })
                
                return results
            
            # 运行异步TTS测试
            results = asyncio.run(test_tts())
            avg_time = sum(r['time'] for r in results) / len(results)
            total_audio = sum(r['audio_size'] for r in results)
            
            self.test_results['edge_tts'] = {
                'status': 'passed',
                'test_count': len(results),
                'average_time': avg_time,
                'total_audio_bytes': total_audio,
                'results': results
            }
            
        except Exception as e:
            logging.error(f"Edge TTS测试失败: {e}")
            traceback.print_exc()
            self.test_results['edge_tts'] = {
                'status': 'failed',
                'error': str(e)
            }
    
    def test_vad_isolated(self):
        """测试VAD组件独立运行"""
        logging.info("=== 测试VAD组件 ===")
        
        try:
            import webrtcvad
            
            # 初始化VAD
            vad = webrtcvad.Vad(2)  # 中等敏感度
            sample_rate = 16000
            frame_duration = 30  # ms
            frame_size = int(sample_rate * frame_duration / 1000)
            
            logging.info(f"VAD配置: 采样率={sample_rate}, 帧时长={frame_duration}ms, 帧大小={frame_size}")
            
            # 生成测试音频
            # 1. 静音
            silence = np.zeros(frame_size, dtype=np.int16)
            
            # 2. 纯噪声
            noise = (np.random.normal(0, 100, frame_size)).astype(np.int16)
            
            # 3. 语音信号（正弦波）
            t = np.linspace(0, frame_duration/1000, frame_size)
            speech = (5000 * np.sin(2 * np.pi * 200 * t)).astype(np.int16)
            
            test_cases = [
                ('silence', silence),
                ('noise', noise),
                ('speech', speech)
            ]
            
            results = []
            
            for name, audio_frame in test_cases:
                frame_bytes = audio_frame.tobytes()
                
                start_time = time.time()
                is_speech = vad.is_speech(frame_bytes, sample_rate)
                detection_time = time.time() - start_time
                
                logging.info(f"VAD测试 - {name}: {'语音' if is_speech else '非语音'} (耗时: {detection_time*1000:.3f}ms)")
                
                results.append({
                    'type': name,
                    'is_speech': is_speech,
                    'detection_time': detection_time
                })
            
            self.test_results['vad'] = {
                'status': 'passed',
                'results': results,
                'config': {
                    'sample_rate': sample_rate,
                    'frame_duration': frame_duration,
                    'frame_size': frame_size
                }
            }
            
        except Exception as e:
            logging.error(f"VAD测试失败: {e}")
            traceback.print_exc()
            self.test_results['vad'] = {
                'status': 'failed',
                'error': str(e)
            }
    
    def test_threading_behavior(self):
        """测试多线程行为"""
        logging.info("=== 测试多线程行为 ===")
        
        try:
            # 创建测试队列
            test_queue = Queue(maxsize=5)
            results = {'produced': 0, 'consumed': 0, 'errors': []}
            
            def producer():
                """生产者线程"""
                try:
                    for i in range(10):
                        test_queue.put(f"item_{i}", timeout=2)
                        results['produced'] += 1
                        logging.debug(f"生产: item_{i}")
                        time.sleep(0.1)
                except Exception as e:
                    results['errors'].append(f"生产者错误: {e}")
                    logging.error(f"生产者错误: {e}")
            
            def consumer():
                """消费者线程"""
                try:
                    while results['consumed'] < 10:
                        item = test_queue.get(timeout=3)
                        results['consumed'] += 1
                        logging.debug(f"消费: {item}")
                        test_queue.task_done()
                        time.sleep(0.2)  # 模拟处理时间
                except Exception as e:
                    results['errors'].append(f"消费者错误: {e}")
                    logging.error(f"消费者错误: {e}")
            
            # 启动线程
            producer_thread = threading.Thread(target=producer)
            consumer_thread = threading.Thread(target=consumer)
            
            start_time = time.time()
            
            producer_thread.start()
            consumer_thread.start()
            
            # 等待完成
            producer_thread.join(timeout=10)
            consumer_thread.join(timeout=15)
            
            execution_time = time.time() - start_time
            
            # 检查结果
            success = (results['produced'] == 10 and 
                      results['consumed'] == 10 and 
                      len(results['errors']) == 0)
            
            logging.info(f"线程测试完成: 生产{results['produced']}, 消费{results['consumed']}, 错误{len(results['errors'])}")
            
            self.test_results['threading'] = {
                'status': 'passed' if success else 'failed',
                'produced': results['produced'],
                'consumed': results['consumed'],
                'errors': results['errors'],
                'execution_time': execution_time,
                'queue_empty': test_queue.empty()
            }
            
        except Exception as e:
            logging.error(f"线程测试失败: {e}")
            traceback.print_exc()
            self.test_results['threading'] = {
                'status': 'failed',
                'error': str(e)
            }
    
    def test_memory_usage(self):
        """测试内存使用情况"""
        logging.info("=== 测试内存使用 ===")
        
        try:
            import psutil
            
            process = psutil.Process()
            initial_memory = process.memory_info().rss / 1024 / 1024  # MB
            
            logging.info(f"初始内存使用: {initial_memory:.1f} MB")
            
            # 创建大量数据模拟处理
            memory_data = []
            
            for i in range(100):
                # 模拟音频数据
                audio_chunk = np.random.random(48000).astype(np.float32)  # 1秒音频
                memory_data.append(audio_chunk)
                
                if i % 20 == 0:
                    current_memory = process.memory_info().rss / 1024 / 1024
                    logging.info(f"处理 {i} 块后内存使用: {current_memory:.1f} MB")
            
            peak_memory = process.memory_info().rss / 1024 / 1024
            
            # 清理数据
            del memory_data
            time.sleep(1)  # 等待垃圾回收
            
            final_memory = process.memory_info().rss / 1024 / 1024
            
            logging.info(f"峰值内存使用: {peak_memory:.1f} MB")
            logging.info(f"清理后内存使用: {final_memory:.1f} MB")
            
            memory_increase = peak_memory - initial_memory
            memory_leaked = final_memory - initial_memory
            
            self.test_results['memory'] = {
                'status': 'passed',
                'initial_mb': initial_memory,
                'peak_mb': peak_memory,
                'final_mb': final_memory,
                'increase_mb': memory_increase,
                'potential_leak_mb': memory_leaked
            }
            
        except Exception as e:
            logging.error(f"内存测试失败: {e}")
            traceback.print_exc()
            self.test_results['memory'] = {
                'status': 'failed',
                'error': str(e)
            }
    
    def run_all_tests(self):
        """运行所有组件测试"""
        logging.info("开始执行组件隔离测试...")
        
        tests = [
            self.test_vad_isolated,
            self.test_whisper_asr_isolated,
            self.test_openai_translation_isolated,
            self.test_edge_tts_isolated,
            self.test_threading_behavior,
            self.test_memory_usage
        ]
        
        for test in tests:
            try:
                test()
            except Exception as e:
                logging.error(f"测试 {test.__name__} 执行异常: {e}")
                traceback.print_exc()
            
            # 测试间隔，防止资源冲突
            time.sleep(2)
        
        # 生成报告
        self.generate_report()
    
    def generate_report(self):
        """生成测试报告"""
        logging.info("=== 组件测试报告 ===")
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results.values() if result.get('status') == 'passed')
        failed_tests = total_tests - passed_tests
        
        print(f"\n组件隔离测试结果:")
        print(f"总测试数: {total_tests}")
        print(f"通过: {passed_tests}")
        print(f"失败: {failed_tests}")
        print(f"通过率: {(passed_tests/total_tests*100):.1f}%")
        
        print(f"\n各组件测试详情:")
        for component, result in self.test_results.items():
            status = result.get('status', 'unknown')
            print(f"  {component}: {status}")
            
            if status == 'failed' and 'error' in result:
                print(f"    错误: {result['error']}")
            
            # 显示特定指标
            if component == 'whisper_asr' and status == 'passed':
                print(f"    加载时间: {result.get('load_time', 0):.2f}s")
                print(f"    识别时间: {result.get('transcribe_time', 0):.2f}s")
            
            elif component == 'openai_translation' and status == 'passed':
                print(f"    平均翻译时间: {result.get('average_time', 0):.2f}s")
            
            elif component == 'threading' and status == 'passed':
                print(f"    生产/消费: {result.get('produced', 0)}/{result.get('consumed', 0)}")
            
            elif component == 'memory' and status == 'passed':
                print(f"    内存增长: {result.get('increase_mb', 0):.1f}MB")
                leak = result.get('potential_leak_mb', 0)
                if leak > 10:  # 超过10MB认为可能有泄漏
                    print(f"    可能的内存泄漏: {leak:.1f}MB")
        
        # 保存详细报告
        import json
        report_file = f"component_test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        try:
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(self.test_results, f, indent=2, ensure_ascii=False, default=str)
            
            print(f"\n详细报告已保存到: {report_file}")
            
        except Exception as e:
            logging.error(f"保存报告失败: {e}")

def main():
    """主函数"""
    print("AI翻译系统 - 组件隔离测试")
    print("测试各个组件的独立功能，排查潜在问题")
    
    tester = ComponentTester()
    
    try:
        tester.run_all_tests()
        print("\n组件测试完成！")
    except KeyboardInterrupt:
        print("\n测试被用户中断")
        return 1
    except Exception as e:
        print(f"测试执行异常: {e}")
        traceback.print_exc()
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())