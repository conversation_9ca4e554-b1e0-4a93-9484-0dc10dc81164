#!/usr/bin/env python3
"""
诊断VAD问题的根本原因
找出用户说话后"半天没反应"的真实原因
"""

import numpy as np
import sounddevice as sd
import time
import threading
from collections import deque

class VADDiagnoser:
    """VAD问题诊断器"""
    
    def __init__(self):
        self.sample_rate = 48000
        self.is_running = False
        self.volume_history = deque(maxlen=100)  # 10秒历史
        self.current_threshold = 0.003
        
        # 状态跟踪
        self.is_speaking = False
        self.speech_start_time = 0
        self.last_speech_time = 0
        self.silence_timeout = 0.8
        
    def diagnose_microphone(self):
        """诊断麦克风音量分布"""
        print("=== 诊断麦克风音量分布 ===")
        print("请保持正常环境，不说话...")
        
        volumes = []
        
        def audio_callback(indata, frames, time, status):
            if len(indata.shape) > 1:
                audio = np.mean(indata, axis=1)
            else:
                audio = indata[:, 0]
            
            volume = np.sqrt(np.mean(audio**2))
            volumes.append(volume)
            
            # 实时显示
            bar = "█" * min(int(volume * 2000), 50)
            print(f"\r背景音量: {volume:.5f} |{bar:<50}|", end="", flush=True)
        
        # 录制10秒背景音
        try:
            with sd.InputStream(callback=audio_callback, channels=1, 
                              samplerate=self.sample_rate, blocksize=4800):
                time.sleep(10)
        except Exception as e:
            print(f"麦克风访问失败: {e}")
            return None
        
        # 分析背景音量
        volumes = np.array(volumes)
        bg_avg = np.mean(volumes)
        bg_max = np.max(volumes)
        bg_std = np.std(volumes)
        
        print(f"\n\n背景音量分析:")
        print(f"  平均值: {bg_avg:.5f}")
        print(f"  最大值: {bg_max:.5f}")
        print(f"  标准差: {bg_std:.5f}")
        print(f"  建议阈值: {bg_max + 3*bg_std:.5f} (背景音+3倍标准差)")
        
        # 现在测试说话
        print(f"\n现在请正常说话5秒钟...")
        time.sleep(1)
        
        speech_volumes = []
        
        def speech_callback(indata, frames, time, status):
            if len(indata.shape) > 1:
                audio = np.mean(indata, axis=1)
            else:
                audio = indata[:, 0]
            
            volume = np.sqrt(np.mean(audio**2))
            speech_volumes.append(volume)
            
            # 实时显示
            bar = "█" * min(int(volume * 2000), 50)
            is_speech_detected = volume > self.current_threshold
            status_symbol = "🎤" if is_speech_detected else "🔇"
            print(f"\r{status_symbol} 语音: {volume:.5f} |{bar:<50}| 阈值:{self.current_threshold:.4f}", end="", flush=True)
        
        try:
            with sd.InputStream(callback=speech_callback, channels=1,
                              samplerate=self.sample_rate, blocksize=4800):
                time.sleep(5)
        except Exception as e:
            print(f"语音录制失败: {e}")
            return None
        
        # 分析语音音量
        speech_volumes = np.array(speech_volumes)
        speech_avg = np.mean(speech_volumes)
        speech_max = np.max(speech_volumes)
        
        print(f"\n\n语音音量分析:")
        print(f"  平均值: {speech_avg:.5f}")
        print(f"  最大值: {speech_max:.5f}")
        print(f"  当前阈值: {self.current_threshold:.5f}")
        
        # 诊断结论
        print(f"\n=== 诊断结果 ===")
        
        if speech_max < self.current_threshold:
            print("❌ 问题确认: 语音音量低于阈值")
            suggested = speech_avg * 0.7
            print(f"   建议降低阈值至: {suggested:.5f}")
            return "threshold_too_high", suggested
            
        elif bg_max > self.current_threshold:
            print("❌ 问题确认: 背景噪声高于阈值")
            suggested = bg_max + 3*bg_std
            print(f"   建议提高阈值至: {suggested:.5f}")
            return "threshold_too_low", suggested
            
        else:
            print("✅ 阈值设置合理")
            print("   问题可能在其他地方...")
            return "threshold_ok", self.current_threshold
    
    def test_vad_simulation(self, threshold):
        """模拟VAD检测过程"""
        print(f"\n=== 模拟VAD检测 (阈值: {threshold:.5f}) ===")
        
        # 重置状态
        self.current_threshold = threshold
        self.is_speaking = False
        detected_speeches = []
        
        def vad_callback(indata, frames, time, status):
            if len(indata.shape) > 1:
                audio = np.mean(indata, axis=1)
            else:
                audio = indata[:, 0]
            
            volume = np.sqrt(np.mean(audio**2))
            current_time = time.inputBufferAdcTime
            
            is_speech = volume > self.current_threshold
            
            if is_speech:
                self.last_speech_time = current_time
                if not self.is_speaking:
                    self.is_speaking = True
                    self.speech_start_time = current_time
                    print(f"\n[VAD] 🎤 开始说话 (音量: {volume:.5f})")
            elif self.is_speaking:
                # 检查静音时长
                silence_duration = current_time - self.last_speech_time
                if silence_duration >= self.silence_timeout:
                    total_duration = current_time - self.speech_start_time
                    print(f"[VAD] 🔇 说话结束 (静音: {silence_duration:.1f}s, 总长: {total_duration:.1f}s)")
                    detected_speeches.append(total_duration)
                    self.is_speaking = False
                else:
                    # 显示静音倒计时
                    remaining = self.silence_timeout - silence_duration
                    print(f"\r[VAD] 静音中... {remaining:.1f}s", end="", flush=True)
        
        print("请说几句话测试VAD检测...")
        
        try:
            with sd.InputStream(callback=vad_callback, channels=1,
                              samplerate=self.sample_rate, blocksize=4800):
                time.sleep(15)  # 15秒测试
        except Exception as e:
            print(f"VAD测试失败: {e}")
            return False
        
        print(f"\n\n检测到 {len(detected_speeches)} 段语音:")
        for i, duration in enumerate(detected_speeches, 1):
            print(f"  语音{i}: {duration:.1f}秒")
        
        return len(detected_speeches) > 0

def main():
    """主诊断流程"""
    print("=== VAD问题根本原因诊断 ===\n")
    print("这个工具将帮助找出'用户说话后半天没反应'的真实原因\n")
    
    diagnoser = VADDiagnoser()
    
    # 1. 诊断麦克风音量
    result = diagnoser.diagnose_microphone()
    
    if not result:
        print("诊断失败，请检查麦克风设置")
        return
    
    problem_type, suggested_threshold = result
    
    # 2. 根据诊断结果给出建议
    print(f"\n=== 修复建议 ===")
    
    if problem_type == "threshold_too_high":
        print("问题: VAD阈值设置过高，检测不到用户语音")
        print(f"解决: 将阈值从 {diagnoser.current_threshold:.5f} 降低到 {suggested_threshold:.5f}")
        
        response = input(f"\n是否要测试新阈值 {suggested_threshold:.5f}? (y/n): ")
        if response.lower() == 'y':
            if diagnoser.test_vad_simulation(suggested_threshold):
                print("✅ 新阈值测试成功")
                update_code_threshold(suggested_threshold)
            else:
                print("❌ 新阈值仍有问题")
                
    elif problem_type == "threshold_too_low":
        print("问题: VAD阈值设置过低，背景噪声被当作语音")
        print(f"解决: 将阈值从 {diagnoser.current_threshold:.5f} 提高到 {suggested_threshold:.5f}")
        
        response = input(f"\n是否要测试新阈值 {suggested_threshold:.5f}? (y/n): ")
        if response.lower() == 'y':
            if diagnoser.test_vad_simulation(suggested_threshold):
                print("✅ 新阈值测试成功") 
                update_code_threshold(suggested_threshold)
            else:
                print("❌ 新阈值仍有问题")
    else:
        print("VAD阈值设置合理，问题可能在:")
        print("1. 静音超时时间设置")
        print("2. 最小语音长度设置") 
        print("3. 音频处理或工作线程问题")
        print("4. 麦克风硬件问题")

def update_code_threshold(new_threshold):
    """更新代码中的阈值"""
    try:
        file_path = '/Users/<USER>/WorkSpace/obs-ai-translator/realtime_translator.py'
        with open(file_path, 'r') as f:
            content = f.read()
        
        # 找到并替换阈值行
        old_pattern = "self.speech_threshold = 0.003"
        new_line = f"self.speech_threshold = {new_threshold:.5f}"
        
        if old_pattern in content:
            content = content.replace(old_pattern, new_line)
            with open(file_path, 'w') as f:
                f.write(content)
            print(f"✅ 已更新代码中的阈值为: {new_threshold:.5f}")
        else:
            print("❌ 未找到阈值设置行，请手动更新")
            
    except Exception as e:
        print(f"❌ 更新失败: {e}")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n诊断被中断")
    except Exception as e:
        print(f"诊断异常: {e}")
        import traceback
        traceback.print_exc()