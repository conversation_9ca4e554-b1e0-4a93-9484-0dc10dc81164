#!/usr/bin/env python3
"""
修复VAD检测问题
解决用户说话后没反应的问题
"""

import time
import numpy as np
from collections import deque
from threading import RLock

class FixedVAD:
    """修复版VAD检测器"""
    
    def __init__(self, sample_rate=48000, callback=None):
        self.sample_rate = sample_rate
        self.callback = callback
        
        # 状态管理
        self.is_speaking = False
        self.speech_start_time = 0
        self.last_speech_time = 0
        self.speech_segments = []
        self.audio_buffer = deque(maxlen=int(sample_rate * 1.0))  # 1秒缓冲
        
        # 阈值参数
        self.speech_threshold = 0.01    # 语音检测阈值
        self.silence_timeout = 1.0      # 1秒静音后结束
        self.min_speech_duration = 0.5  # 最小语音长度0.5秒
        self.max_speech_duration = 15.0 # 最大语音长度15秒
        
        self.lock = RLock()
        
        print(f"[VAD] 初始化完成")
        print(f"  语音阈值: {self.speech_threshold}")
        print(f"  静音超时: {self.silence_timeout}s")
        print(f"  最小语音长度: {self.min_speech_duration}s")
    
    def process_audio(self, audio_data):
        """处理音频数据"""
        if len(audio_data) == 0:
            return
        
        # 确保单声道
        if len(audio_data.shape) > 1:
            audio_data = np.mean(audio_data, axis=1)
        
        # 计算音量
        volume = np.sqrt(np.mean(audio_data**2))
        current_time = time.time()
        
        # 添加到缓冲
        self.audio_buffer.extend(audio_data)
        
        with self.lock:
            is_speech = volume > self.speech_threshold
            
            if is_speech:
                self.last_speech_time = current_time
                
                if not self.is_speaking:
                    # 开始说话
                    self.is_speaking = True
                    self.speech_start_time = current_time
                    self.speech_segments = list(self.audio_buffer)  # 包含前面的缓冲
                    print(f"[VAD] 🎤 开始说话 (音量: {volume:.3f})")
                else:
                    # 继续说话，添加音频
                    self.speech_segments.extend(audio_data)
            
            elif self.is_speaking:
                # 静音但正在说话状态
                self.speech_segments.extend(audio_data)
                
                # 检查各种结束条件
                silence_duration = current_time - self.last_speech_time
                total_duration = current_time - self.speech_start_time
                
                should_end = False
                reason = ""
                
                if silence_duration > self.silence_timeout:
                    should_end = True
                    reason = f"静音超时({silence_duration:.1f}s)"
                elif total_duration > self.max_speech_duration:
                    should_end = True
                    reason = f"语音过长({total_duration:.1f}s)"
                
                if should_end:
                    self._end_speech(reason, total_duration)
    
    def _end_speech(self, reason, duration):
        """结束语音检测"""
        print(f"[VAD] 🔇 说话结束 - {reason}, 总时长: {duration:.1f}s")
        
        if duration >= self.min_speech_duration:
            # 语音足够长，处理它
            audio_segment = np.array(self.speech_segments, dtype=np.float32)
            print(f"[VAD] ✅ 语音片段准备处理: {len(audio_segment)} 样本 ({duration:.1f}s)")
            
            if self.callback:
                try:
                    self.callback(audio_segment)
                except Exception as e:
                    print(f"[VAD] 回调错误: {e}")
        else:
            print(f"[VAD] ⏭️  语音过短，跳过处理")
        
        # 重置状态
        self.is_speaking = False
        self.speech_segments = []
        self.speech_start_time = 0
        self.last_speech_time = 0
    
    def force_end_speech(self):
        """强制结束当前语音"""
        with self.lock:
            if self.is_speaking:
                duration = time.time() - self.speech_start_time
                self._end_speech("强制结束", duration)

def test_vad():
    """测试VAD"""
    print("=== 测试修复版VAD ===\n")
    
    def speech_callback(audio):
        print(f"[回调] 收到语音片段: {len(audio)} 样本")
    
    vad = FixedVAD(callback=speech_callback)
    
    # 模拟音频输入
    sample_rate = 48000
    
    # 1. 静音
    print("1. 发送静音...")
    silence = np.zeros(4800, dtype=np.float32)  # 0.1秒
    for _ in range(5):
        vad.process_audio(silence)
        time.sleep(0.1)
    
    # 2. 语音
    print("\n2. 发送语音...")
    t = np.linspace(0, 0.1, 4800, False)
    speech = 0.02 * np.sin(2 * np.pi * 200 * t).astype(np.float32)
    
    for i in range(15):  # 1.5秒语音
        vad.process_audio(speech)
        time.sleep(0.1)
    
    # 3. 静音（触发结束）
    print("\n3. 发送静音（应触发结束）...")
    for _ in range(12):  # 1.2秒静音，应该在1秒时触发结束
        vad.process_audio(silence)
        time.sleep(0.1)
    
    print("\n测试完成")

if __name__ == "__main__":
    test_vad()