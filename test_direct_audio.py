#!/usr/bin/env python3
"""
直接测试音频输出
"""
import sounddevice as sd
import numpy as np
import time

def test_all_speakers():
    """测试所有可能的扬声器设备"""
    print("=== 测试所有音频输出设备 ===")
    
    # 生成测试音频 (短促的蜂鸣声)
    sample_rate = 48000
    duration = 0.5
    frequency = 800
    
    t = np.linspace(0, duration, int(sample_rate * duration), False)
    audio_signal = 0.2 * np.sin(2 * np.pi * frequency * t)
    stereo_audio = np.column_stack((audio_signal, audio_signal))
    
    devices = sd.query_devices()
    
    # 重点测试的设备
    priority_devices = [
        (1, "Rockenlee的AirPods Pro #2"),  # AirPods
        (5, "MacBook Pro扬声器"),            # 内置扬声器
        (2, "RTK UHD HDR"),                 # 外部显示器
    ]
    
    print("测试优先设备:")
    for device_id, name in priority_devices:
        if device_id < len(devices):
            device = devices[device_id]
            if device['max_output_channels'] > 0:
                print(f"\n🔊 测试设备 {device_id}: {name}")
                try:
                    sd.play(stereo_audio, sample_rate, device=device_id)
                    sd.wait()
                    print(f"✓ 设备 {device_id} 播放完成")
                    
                    # 问用户是否听到了
                    print(f"你听到来自 '{name}' 的蜂鸣声了吗？")
                    time.sleep(1)
                    
                except Exception as e:
                    print(f"❌ 设备 {device_id} 播放失败: {e}")
            else:
                print(f"⚠️  设备 {device_id} 不支持输出")
        else:
            print(f"⚠️  设备 {device_id} 不存在")

def force_play_to_airpods():
    """强制播放到AirPods"""
    print("\n=== 强制播放到AirPods ===")
    
    # 生成更明显的测试音频
    sample_rate = 48000
    duration = 2.0
    
    # 创建更复杂的测试音频 (音阶)
    frequencies = [440, 554, 659, 784]  # C大调和弦
    audio_segments = []
    
    for freq in frequencies:
        t = np.linspace(0, duration/4, int(sample_rate * duration/4), False)
        segment = 0.3 * np.sin(2 * np.pi * freq * t)
        audio_segments.extend(segment)
    
    audio_signal = np.array(audio_segments)
    stereo_audio = np.column_stack((audio_signal, audio_signal))
    
    print("播放音阶到AirPods...")
    try:
        # 设备1是AirPods
        sd.play(stereo_audio, sample_rate, device=1)
        print("正在播放... (应该听到4个音符的音阶)")
        sd.wait()
        print("✓ AirPods播放完成")
        return True
    except Exception as e:
        print(f"❌ AirPods播放失败: {e}")
        return False

def test_system_audio():
    """测试系统默认音频"""
    print("\n=== 测试系统默认音频 ===")
    
    sample_rate = 48000
    duration = 1.0
    frequency = 1000
    
    t = np.linspace(0, duration, int(sample_rate * duration), False)
    audio_signal = 0.25 * np.sin(2 * np.pi * frequency * t)
    stereo_audio = np.column_stack((audio_signal, audio_signal))
    
    print("播放到系统默认设备...")
    try:
        sd.play(stereo_audio, sample_rate)  # 不指定设备，使用默认
        sd.wait()
        print("✓ 系统默认设备播放完成")
        return True
    except Exception as e:
        print(f"❌ 系统默认设备播放失败: {e}")
        return False

if __name__ == "__main__":
    print("开始音频输出测试...\n")
    
    # 首先测试系统默认
    if test_system_audio():
        print("系统默认音频工作正常")
    
    # 然后测试AirPods
    if force_play_to_airpods():
        print("AirPods音频工作正常")
    
    # 最后测试所有设备
    test_all_speakers()
    
    print("\n=== 测试完成 ===")
    print("如果所有测试都听不到声音，可能的问题:")
    print("1. 音频被静音或音量过低")
    print("2. AirPods连接有问题")
    print("3. 音频驱动问题")
    print("4. Python音频库配置问题")