#!/usr/bin/env python3
"""
交互式VAD测试工具 - 专门测试真实麦克风输入的VAD状态管理问题
"""
import sounddevice as sd
import numpy as np
import time
from collections import deque
from threading import RLock, Thread
import threading


class SimpleVAD:
    """简化版VAD，专注于状态管理测试"""

    def __init__(self, sample_rate=48000):
        self.sample_rate = sample_rate
        self.state_lock = RLock()

        # VAD状态
        self.audio_buffer = deque(maxlen=int(self.sample_rate * 1.0))
        self.is_speaking = False
        self.speech_start_time = 0
        self.last_speech_time = 0

        # VAD参数 - 与主系统一致
        self.speech_threshold = 0.008
        self.silence_timeout = 0.8
        self.min_speech_duration = 0.5
        self.max_speech_duration = 15.0

        # 去抖动参数
        self.speech_debounce_time = 0.3
        self.speech_start_pending = False
        self.speech_start_pending_time = 0

        # 调试和统计
        self.debug_vad = True
        self._last_silence_print = -1
        self.interaction_count = 0
        self.reset_count = 0

        print(f"[SimpleVAD] 初始化完成")
        print(f"  - 语音阈值: {self.speech_threshold:.5f}")
        print(f"  - 静音超时: {self.silence_timeout}秒")
        print(f"  - 去抖动时间: {self.speech_debounce_time}秒")

    def process_audio(self, audio_data):
        """处理音频数据 - 完全复制原系统逻辑"""
        if len(audio_data) == 0:
            return

        # 确保单声道
        if len(audio_data.shape) > 1:
            audio_data = np.mean(audio_data, axis=1)

        # 计算音量
        volume = np.sqrt(np.mean(audio_data**2))
        current_time = time.time()

        # 添加到缓冲
        self.audio_buffer.extend(audio_data)

        with self.state_lock:
            is_speech = volume > self.speech_threshold

            # 每隔一段时间输出状态
            if self.debug_vad and int(current_time * 3) % 3 == 0:  # 每1/3秒输出一次
                status = "🎤" if is_speech else "🔇"
                speaking_status = "✅" if self.is_speaking else "❌"
                pending_status = "⏳" if self.speech_start_pending else "⭕"
                print(f"[VAD] {status} 音量:{volume:.5f} 阈值:{self.speech_threshold:.5f} 说话:{speaking_status} 待定:{pending_status}")

            if is_speech:
                if not self.is_speaking:
                    # 去抖动：需要连续一段时间的语音才开始
                    if not self.speech_start_pending:
                        self.speech_start_pending = True
                        self.speech_start_pending_time = current_time
                        print(f"[VAD] ⏳ 检测到语音，开始去抖动计时... (音量: {volume:.5f})")
                    elif current_time - self.speech_start_pending_time >= self.speech_debounce_time:
                        # 连续超过阈值超过debounce时间，确认开始说话
                        self.is_speaking = True
                        self.speech_start_time = self.speech_start_pending_time
                        print(f"[VAD] 🎤 确认开始说话! (音量: {volume:.4f}, 去抖时间: {current_time - self.speech_start_pending_time:.2f}s)")
                        self.speech_start_pending = False
                        self.last_speech_time = current_time
                else:
                    # 已在说话状态，更新最后语音时间
                    self.last_speech_time = current_time
            else:
                # 非语音
                if self.speech_start_pending:
                    # 取消pending状态
                    print(f"[VAD] ❌ 去抖动期间检测到静音，取消语音检测 (去抖时间: {current_time - self.speech_start_pending_time:.2f}s)")
                    self.speech_start_pending = False

                if self.is_speaking:
                    # 静音但正在说话状态
                    silence_duration = current_time - self.last_speech_time
                    total_duration = current_time - self.speech_start_time

                    should_end = False
                    reason = ""

                    # 调试：显示静音计时
                    if self.debug_vad:
                        if int(silence_duration * 5) != getattr(self, '_last_silence_print', -1):
                            self._last_silence_print = int(silence_duration * 5)
                            print(f"[VAD] ⏰ 静音计时: {silence_duration:.2f}s / {self.silence_timeout}s (总长:{total_duration:.1f}s)")

                    if silence_duration >= self.silence_timeout:
                        should_end = True
                        reason = f"静音超时({silence_duration:.1f}s)"
                    elif total_duration >= self.max_speech_duration:
                        should_end = True
                        reason = f"语音过长({total_duration:.1f}s)"

                    if should_end:
                        print(f"[VAD] 🔇 说话结束 - {reason}, 总时长: {total_duration:.1f}s")
                        self._process_speech_end(total_duration)

                        # 重置VAD状态
                        self.is_speaking = False
                        self.speech_start_time = 0
                        self.last_speech_time = 0
                        self._last_silence_print = -1

                        self.interaction_count += 1
                        print(f"[统计] 🎯 第 {self.interaction_count} 次语音交互完成")

    def _process_speech_end(self, duration):
        """处理语音结束"""
        if duration < self.min_speech_duration:
            print(f"[VAD] ⏭️  语音过短({duration:.1f}s < {self.min_speech_duration}s)，跳过处理")
            return

        print(f"[VAD] ✅ 语音片段处理开始: {duration:.1f}秒")
        # 模拟处理时间
        time.sleep(0.5)
        print(f"[VAD] ✅ 语音片段处理完成")

    def reset_system(self):
        """重置VAD系统状态"""
        with self.state_lock:
            old_state = {
                'is_speaking': self.is_speaking,
                'speech_start_pending': self.speech_start_pending,
                'speech_start_time': self.speech_start_time,
                'last_speech_time': self.last_speech_time
            }

            self.is_speaking = False
            self.speech_start_time = 0
            self.last_speech_time = 0
            self.speech_start_pending = False
            self.speech_start_pending_time = 0
            self._last_silence_print = -1

            # 清空音频缓冲区
            self.audio_buffer.clear()

            self.reset_count += 1
            print(f"\n[🔄重置] VAD状态已重置 (第 {self.reset_count} 次)")
            print(f"[重置] 之前状态: {old_state}")
            print(f"[重置] 当前状态: is_speaking={self.is_speaking}, pending={self.speech_start_pending}")

    def print_status(self):
        """打印当前状态"""
        with self.state_lock:
            print(f"\n[📊状态] VAD当前状态:")
            print(f"  - 正在说话: {self.is_speaking}")
            print(f"  - 待定状态: {self.speech_start_pending}")
            print(f"  - 语音开始时间: {self.speech_start_time}")
            print(f"  - 最后语音时间: {self.last_speech_time}")
            print(f"  - 缓冲区大小: {len(self.audio_buffer)}")
            print(f"  - 交互次数: {self.interaction_count}")
            print(f"  - 重置次数: {self.reset_count}")


def find_microphone():
    """查找可用的麦克风设备"""
    devices = sd.query_devices()

    print("可用音频设备:")
    for i, device in enumerate(devices):
        if device['max_input_channels'] > 0:
            print(f"  {i}: {device['name']} (输入通道: {device['max_input_channels']})")

    # 优先选择MacBook Pro麦克风
    for i, device in enumerate(devices):
        if device['max_input_channels'] > 0:
            if 'MacBook Pro' in device['name'] or 'Built-in' in device['name']:
                print(f"\n✅ 选择设备: {device['name']} (ID: {i})")
                return i

    # 回退到默认设备
    print("\n使用默认输入设备")
    return None


def main():
    """主函数"""
    print("=== 交互式VAD测试工具 ===")
    print("专门用于测试语音活动检测的状态管理问题\n")

    # 查找麦克风
    mic_device = find_microphone()

    # 创建VAD测试器
    vad = SimpleVAD()

    print("\n📋 测试说明:")
    print("1. 对着麦克风说话，观察VAD状态变化")
    print("2. 第一次成功识别后，再次说话测试是否卡住")
    print("3. 使用 'r' 命令测试重置功能是否有效")
    print("\n⌨️  命令:")
    print("  r + Enter: 重置VAD状态")
    print("  s + Enter: 显示当前状态")
    print("  q + Enter: 退出")
    print("  直接说话: 测试VAD检测")
    print("\n🎤 开始测试... (请说话)")

    # 键盘监听线程
    def keyboard_monitor():
        while True:
            try:
                user_input = input().strip().lower()
                if user_input == 'r':
                    vad.reset_system()
                    print("✅ 重置完成，请再次说话测试")
                elif user_input == 's':
                    vad.print_status()
                elif user_input == 'q':
                    print("👋 退出测试工具")
                    return
                else:
                    print("❓ 未知命令，使用 r/s/q")
            except (EOFError, KeyboardInterrupt):
                print("\n👋 测试被中断")
                return

    keyboard_thread = threading.Thread(target=keyboard_monitor, daemon=True)
    keyboard_thread.start()

    # 音频捕获回调
    def audio_callback(indata, frames, time_info, status):
        if status:
            print(f"⚠️  音频状态: {status}")

        # 计算音量
        volume = np.sqrt(np.mean(indata**2))

        # 简单的音量条显示 (每10帧显示一次)
        if int(time_info.inputBufferAdcTime * 10) % 10 == 0:
            bar_length = int(min(volume * 50, 50))
            bar = '█' * bar_length + '░' * (50 - bar_length)
            print(f'\r🔊 音量: [{bar}] {volume:.5f}', end='', flush=True)

        # 发送到VAD处理
        if indata.shape[0] > 0:
            mono_audio = np.mean(indata, axis=1) if len(indata.shape) > 1 else indata.flatten()
            vad.process_audio(mono_audio)

    # 开始音频捕获
    try:
        with sd.InputStream(
            device=mic_device,
            channels=1,
            samplerate=48000,
            blocksize=1024,
            dtype='float32',
            latency='low',
            callback=audio_callback
        ):
            while keyboard_thread.is_alive():
                time.sleep(0.1)

    except KeyboardInterrupt:
        print("\n👋 收到中断信号，退出...")
    except Exception as e:
        print(f"❌ 错误: {e}")


if __name__ == "__main__":
    main()