# OBS AI 实时同声传译系统

## 功能特性

- 🎤 **实时语音捕获**: 通过BlackHole虚拟音频设备捕获系统音频
- 🧠 **AI语音识别**: 使用OpenAI Whisper进行语音转文字
- 🌍 **智能翻译**: 使用GPT-3.5进行实时翻译
- 🔊 **语音合成**: 使用Edge-TTS生成自然语音
- 📺 **OBS集成**: 自动更新OBS字幕源
- ⚡ **低延迟**: 2-4秒端到端延迟

## 系统要求

### macOS
- macOS 10.14+
- Python 3.8+
- BlackHole 2ch (虚拟音频设备)
- OBS Studio
- OpenAI API Key

### 安装BlackHole
```bash
brew install blackhole-2ch
```

## 快速开始

### 1. 环境准备
```bash
# 克隆项目
git clone <repository>
cd obs-ai-translator

# 创建虚拟环境
python3 -m venv venv
source venv/bin/activate

# 安装依赖
pip install -r requirements.txt
```

### 2. 配置环境变量
```bash
# 设置OpenAI API Key
export OPENAI_API_KEY="your-openai-api-key"

# 永久设置（添加到 ~/.zshrc 或 ~/.bash_profile）
echo 'export OPENAI_API_KEY="your-openai-api-key"' >> ~/.zshrc
```

### 3. OBS配置
1. 启动OBS Studio
2. 启用WebSocket服务器：
   - 工具 → WebSocket服务器设置
   - 启用WebSocket服务器
   - 设置密码（可选）
3. 添加文字源作为字幕（命名为"字幕"）

### 4. 音频路由配置
1. 系统偏好设置 → 声音 → 输出 → 选择BlackHole 2ch
2. OBS中添加音频输入捕获，选择BlackHole 2ch
3. 如需监听，使用Audio MIDI Setup创建多输出设备

### 5. 运行系统
```bash
# 使用启动脚本（推荐）
./start.sh

# 或直接运行
python3 obs_ai_translator_main.py

# 指定OBS密码
python3 obs_ai_translator_main.py "your-obs-password"
```

## 支持的语言对

- 中文 → 英文
- 英文 → 中文  
- 中文 → 日文

可在 `config.py` 中添加更多语言配置。

## 性能优化

### 延迟优化
- 使用流式API减少等待时间
- VAD检测优化音频分段
- 并行处理管道
- 本地模型缓存

### 质量优化
- 语义分段而非时间分段
- 上下文保持
- 专业术语翻译缓存
- 语音合成语调优化

## 常见问题

### Q: 找不到BlackHole设备
A: 确保已正确安装BlackHole 2ch，重启应用程序

### Q: OBS连接失败  
A: 检查OBS WebSocket服务器是否启用，端口号是否正确

### Q: OpenAI API报错
A: 检查API Key是否正确设置，账户余额是否充足

### Q: 延迟过高
A: 检查网络连接，考虑使用本地模型或优化音频分段

## 技术架构

```
音频输入 → VAD检测 → 语音识别 → 翻译 → 语音合成 → 音频输出
   ↓           ↓         ↓       ↓        ↓          ↓
BlackHole → WebRTC → Whisper → GPT → Edge-TTS → BlackHole
   ↓                                              ↓
 OBS捕获                                      OBS字幕
```

## 开发说明

### 主要模块
- `obs_ai_translator_main.py`: 主程序入口
- `realtime_translator.py`: 实时翻译引擎
- `config.py`: 配置管理
- `test_*.py`: 单元测试

### 自定义开发
```python
# 添加新的翻译服务
class CustomTranslator:
    def translate(self, text, source_lang, target_lang):
        # 实现翻译逻辑
        return translated_text

# 添加新的TTS引擎
class CustomTTS:
    def synthesize(self, text, voice):
        # 实现语音合成
        return audio_data
```

## 许可证

MIT License