#!/usr/bin/env python3
"""
测试修复后的系统是否能处理连续翻译而不冻结

Test that the fixed system can handle consecutive translations without freezing
"""

import time
import numpy as np
import threading
from realtime_translator import RealtimeTranslator
from reliability_utils import ResourceMonitor

def generate_test_audio(duration=2.0, sample_rate=48000, frequency=440):
    """生成测试音频（模拟语音）"""
    t = np.linspace(0, duration, int(sample_rate * duration))
    # Generate audio that looks like speech (with varying amplitude)
    audio = 0.1 * np.sin(2 * np.pi * frequency * t) * (0.5 + 0.5 * np.sin(2 * np.pi * 0.5 * t))
    return audio.astype(np.float32)

def test_consecutive_translations():
    """测试连续翻译场景（重现用户问题）"""
    print("=== 测试连续翻译场景 ===")
    
    # Initialize translator with reliability fixes
    translator = RealtimeTranslator(
        source_lang='zh', 
        target_lang='en', 
        enable_tts=False  # Disable TTS for faster testing
    )
    
    resource_monitor = ResourceMonitor()
    test_start_time = time.time()
    
    try:
        print("开始连续翻译测试 - 模拟用户报告的场景")
        print("场景: 输入1-2句对话后系统冻结")
        
        # Test scenario: Multiple audio inputs in succession
        for i in range(10):  # Test 10 consecutive "sentences"
            print(f"\n--- 第 {i+1} 句对话 ---")
            
            # Generate test audio that simulates speech
            test_audio = generate_test_audio(duration=1.5, frequency=440 + i*10)
            
            # Split into chunks (simulate real-time audio input)
            chunk_size = 1024
            for j in range(0, len(test_audio), chunk_size):
                chunk = test_audio[j:j+chunk_size]
                translator.process_audio(chunk)
                
                # Small delay to simulate real-time input
                time.sleep(0.01)
            
            # Check resource usage
            memory_mb = resource_monitor.get_memory_usage_mb()
            cpu_percent = resource_monitor.get_cpu_usage_percent()
            print(f"资源使用 - 内存: {memory_mb:.1f}MB, CPU: {cpu_percent:.1f}%")
            
            # Check queue sizes
            rec_size = translator.recognition_queue.qsize()
            trans_size = translator.translation_queue.qsize()
            tts_size = translator.tts_queue.qsize()
            print(f"队列状态 - ASR:{rec_size}, 翻译:{trans_size}, TTS:{tts_size}")
            
            # Wait a bit before next sentence
            time.sleep(0.5)
            
            # Check if system is responsive (this would fail in frozen system)
            try:
                translator.reset_system()
                print("✓ 系统响应正常，可以接受重置命令")
            except Exception as e:
                print(f"✗ 系统响应异常: {e}")
                break
        
        print(f"\n=== 测试完成 ===")
        print(f"总耗时: {time.time() - test_start_time:.2f} 秒")
        print("✓ 系统在连续翻译测试中保持稳定，未发生冻结")
        
    except Exception as e:
        print(f"✗ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        print("执行优雅关闭...")
        translator.shutdown()
        print("✓ 系统优雅关闭完成")

def test_resource_pressure():
    """测试高资源压力下的系统稳定性"""
    print("\n=== 测试资源压力场景 ===")
    
    translator = RealtimeTranslator(
        source_lang='zh',
        target_lang='en', 
        enable_tts=False
    )
    
    try:
        print("测试高频音频输入...")
        
        # Rapid fire audio input to stress test
        for i in range(50):  # More audio chunks
            test_audio = generate_test_audio(duration=0.5, frequency=400 + i*5)
            
            # Process without delays (stress test)
            chunk_size = 512
            for j in range(0, len(test_audio), chunk_size):
                chunk = test_audio[j:j+chunk_size]
                translator.process_audio(chunk)
            
            if i % 10 == 0:
                print(f"已处理 {i+1} 个音频段")
                
                # Check queue overflow protection
                rec_size = translator.recognition_queue.qsize()
                trans_size = translator.translation_queue.qsize()
                print(f"队列状态 - ASR:{rec_size}, 翻译:{trans_size}")
                
                if rec_size > 8 or trans_size > 8:
                    print("队列大小过大，检查溢出保护是否生效")
        
        print("✓ 高压力测试完成，系统保持稳定")
        
    except Exception as e:
        print(f"✗ 资源压力测试失败: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        translator.shutdown()

def test_timeout_protection():
    """测试超时保护机制"""
    print("\n=== 测试超时保护机制 ===")
    
    # This test would require mocking slow API calls
    # For now, just verify that the timeout decorators are in place
    
    from reliability_utils import timeout_handler
    
    @timeout_handler(1)  # 1 second timeout
    def slow_function():
        time.sleep(2)  # Should timeout
        return "This should not return"
    
    try:
        result = slow_function()
        print("✗ 超时保护未生效")
    except Exception as e:
        print(f"✓ 超时保护生效: {type(e).__name__}")
    
    print("超时保护机制测试完成")

def test_circuit_breaker():
    """测试断路器模式"""
    print("\n=== 测试断路器模式 ===")
    
    from reliability_utils import CircuitBreaker, CircuitBreakerConfig
    
    # Create a circuit breaker that fails quickly
    circuit_breaker = CircuitBreaker(
        CircuitBreakerConfig(failure_threshold=2, recovery_timeout=1)
    )
    
    def failing_function():
        raise Exception("Simulated API failure")
    
    try:
        # Trigger failures to open circuit
        for i in range(3):
            try:
                circuit_breaker.call(failing_function)
            except Exception:
                print(f"调用 {i+1} 失败")
        
        # Circuit should now be open
        try:
            circuit_breaker.call(failing_function)
            print("✗ 断路器未打开")
        except Exception as e:
            if "Circuit breaker is OPEN" in str(e):
                print("✓ 断路器正确打开")
            else:
                print(f"✓ 断路器处理: {e}")
        
        print("断路器模式测试完成")
        
    except Exception as e:
        print(f"断路器测试错误: {e}")

def main():
    """运行所有测试"""
    print("开始修复验证测试...")
    print("测试目标：验证 '输入一两句对话之后，整个应用就卡住不work了' 问题已修复")
    
    # Run tests
    test_consecutive_translations()
    test_resource_pressure() 
    test_timeout_protection()
    test_circuit_breaker()
    
    print("\n=== 所有测试完成 ===")
    print("如果所有测试都通过，说明系统冻结问题已得到修复")

if __name__ == "__main__":
    main()