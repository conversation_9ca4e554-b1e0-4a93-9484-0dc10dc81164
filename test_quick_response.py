#!/usr/bin/env python3
"""
测试VAD快速响应和TTS音频输出修复
"""

import sys
import time
import numpy as np
from obs_ai_translator_mic import MicrophoneTranslatorSystem

def test_quick_vad_response():
    """测试快速VAD响应"""
    print("=== 测试VAD快速响应 (0.6秒超时) ===\n")
    
    system = MicrophoneTranslatorSystem(
        enable_tts=True,  # 启用TTS测试音频输出
        source_lang='zh',
        target_lang='en'
    )
    
    try:
        print("VAD参数:")
        translator = system.translator
        print(f"  语音阈值: {translator.speech_threshold}")
        print(f"  静音超时: {translator.silence_timeout}s")
        print(f"  最小语音长度: {translator.min_speech_duration}s")
        
        print("\n1. 发送1.5秒语音...")
        
        # 生成测试语音 - 稍微大声一点确保检测
        sample_rate = 48000
        duration = 1.5
        t = np.linspace(0, duration, int(sample_rate * duration), False)
        
        # 多频率混合语音信号
        audio = (
            0.015 * np.sin(2 * np.pi * 200 * t) +
            0.012 * np.sin(2 * np.pi * 400 * t) +
            0.008 * np.sin(2 * np.pi * 800 * t)
        ).astype(np.float32)
        
        # 添加包络避免突兀
        envelope = np.hanning(len(audio))
        audio = audio * envelope
        
        actual_volume = np.sqrt(np.mean(audio**2))
        print(f"测试音频RMS音量: {actual_volume:.4f}")
        
        start_time = time.time()
        
        # 实时发送音频
        chunk_size = 4800  # 0.1秒
        for i in range(0, len(audio), chunk_size):
            chunk = audio[i:i+chunk_size]
            translator.process_audio(chunk)
            time.sleep(0.02)  # 稍微慢一点模拟真实
        
        speech_end_time = time.time()
        print(f"2. 语音发送完成 ({speech_end_time - start_time:.1f}s)，现在发送静音...")
        
        # 发送静音，测试0.6秒超时
        silence = np.zeros(4800, dtype=np.float32)
        silence_start = time.time()
        
        for i in range(10):  # 最多1秒静音
            translator.process_audio(silence)
            time.sleep(0.1)
            
            # 检查是否已结束
            if not translator.is_speaking:
                detection_time = time.time()
                silence_duration = detection_time - silence_start
                total_time = detection_time - start_time
                
                print(f"\n✅ VAD检测结果:")
                print(f"  检测延迟: {silence_duration:.1f}s (期望 ~0.6s)")
                print(f"  总响应时间: {total_time:.1f}s")
                print(f"  工作队列: {translator.work_queue.qsize()} 项")
                
                if silence_duration <= 0.8:  # 0.6s + 容错
                    print("✅ VAD响应速度符合预期")
                else:
                    print("❌ VAD响应仍然偏慢")
                    
                break
        else:
            print("❌ VAD未在预期时间内响应")
        
        # 等待TTS处理完成
        print("\n3. 等待TTS处理和播放...")
        time.sleep(8)  # 等待识别+翻译+TTS流程
        
        final_time = time.time()
        print(f"完整流程耗时: {final_time - start_time:.1f}s")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False
    finally:
        system.translator.shutdown()
    
    return True

def test_tts_audio_output():
    """测试TTS音频输出"""
    print("\n=== 测试TTS音频输出修复 ===\n")
    
    system = MicrophoneTranslatorSystem(
        enable_tts=True,
        source_lang='zh', 
        target_lang='en'
    )
    
    try:
        print("直接测试TTS合成和播放...")
        
        # 直接调用TTS方法
        test_text = "Hello, this is a test of audio output."
        print(f"合成文本: {test_text}")
        
        system.translator._synthesize_and_play(test_text)
        
        print("TTS已触发，检查：")
        print("1. 是否看到 '[音频] 已播放到本地音频设备' 消息")
        print("2. 耳机或扬声器是否有声音输出")
        print("3. 如果使用BlackHole，OBS是否收到音频")
        
        # 等待播放完成
        time.sleep(5)
        
    except Exception as e:
        print(f"❌ TTS测试失败: {e}")
        return False
    finally:
        system.translator.shutdown()
    
    return True

def main():
    """主测试函数"""
    print("=== 快速响应和TTS输出修复测试 ===\n")
    print("修复内容:")
    print("🔧 VAD静音超时: 1.0s → 0.6s (更快响应)")
    print("🔧 TTS音频输出: 恢复本地播放功能")
    print("🔧 音频路由: BlackHole + 本地播放双输出")
    print("\n" + "="*50 + "\n")
    
    results = []
    
    # 1. VAD快速响应测试
    print("测试1: VAD快速响应")
    results.append(("VAD快速响应", test_quick_vad_response()))
    
    # 2. TTS音频输出测试
    print("测试2: TTS音频输出")
    results.append(("TTS音频输出", test_tts_audio_output()))
    
    # 结果汇总
    print("\n" + "="*50)
    print("修复测试结果")
    print("="*50)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:<15} {status}")
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    print("="*50)
    print(f"总计: {passed}/{total} 测试通过")
    
    if passed == total:
        print("\n🎉 修复验证成功！")
        print("\n预期效果:")
        print("• 用户说话后0.6秒静音即触发处理")
        print("• TTS结果同时输出到BlackHole和本地音频")
        print("• 耳机/扬声器能听到翻译结果") 
        print("• OBS也能录制到翻译音频")
    else:
        print("\n⚠️  部分修复未生效，请检查具体问题")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n测试被中断")
    except Exception as e:
        print(f"测试异常: {e}")
        import traceback
        traceback.print_exc()