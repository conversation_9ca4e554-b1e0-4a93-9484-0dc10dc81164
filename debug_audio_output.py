#!/usr/bin/env python3
"""
调试音频输出问题
"""
import sounddevice as sd
import numpy as np
import time

def check_audio_devices():
    """检查音频设备配置"""
    print("=== 当前音频设备配置 ===")
    
    devices = sd.query_devices()
    print(f"系统默认设备: 输入={sd.default.device[0]}, 输出={sd.default.device[1]}")
    
    print("\n所有可用设备:")
    for i, device in enumerate(devices):
        device_type = []
        if device['max_input_channels'] > 0:
            device_type.append('输入')
        if device['max_output_channels'] > 0:
            device_type.append('输出')
        
        marker = " ← 默认输出" if i == sd.default.device[1] else ""
        print(f"{i}: {device['name']} ({'/'.join(device_type)}){marker}")

def test_speaker_output():
    """测试扬声器输出"""
    print("\n=== 测试扬声器输出 ===")
    
    # 生成测试音频信号 (440Hz 正弦波，1秒)
    sample_rate = 48000
    duration = 1.0
    frequency = 440  # A4音符
    
    t = np.linspace(0, duration, int(sample_rate * duration), False)
    audio_signal = 0.3 * np.sin(2 * np.pi * frequency * t)
    
    # 转为立体声
    stereo_audio = np.column_stack((audio_signal, audio_signal))
    
    print("播放测试音频到默认扬声器...")
    try:
        sd.play(stereo_audio, sample_rate)
        sd.wait()  # 等待播放完成
        print("✓ 默认扬声器测试完成")
    except Exception as e:
        print(f"❌ 默认扬声器测试失败: {e}")
    
    # 测试具体设备
    devices = sd.query_devices()
    for i, device in enumerate(devices):
        if device['max_output_channels'] > 0 and ('扬声器' in device['name'] or 'Speaker' in device['name'] or 'AirPods' in device['name']):
            print(f"\n测试设备 {i}: {device['name']}")
            try:
                sd.play(stereo_audio, sample_rate, device=i)
                sd.wait()
                print(f"✓ 设备 {i} 测试完成")
                time.sleep(0.5)
            except Exception as e:
                print(f"❌ 设备 {i} 测试失败: {e}")

def check_volume_levels():
    """检查音量级别"""
    print("\n=== 检查系统音量 ===")
    
    try:
        # 尝试使用osascript检查音量 (macOS)
        import subprocess
        result = subprocess.run(['osascript', '-e', 'output volume of (get volume settings)'], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            volume = result.stdout.strip()
            print(f"系统音量: {volume}%")
            
            if int(volume) == 0:
                print("⚠️  系统音量为0，请调高音量")
            elif int(volume) < 30:
                print("⚠️  系统音量较低，建议调高")
        else:
            print("无法检查系统音量")
    except Exception as e:
        print(f"检查音量时出错: {e}")

if __name__ == "__main__":
    check_audio_devices()
    check_volume_levels()
    
    input("\n按Enter开始测试音频输出...")
    test_speaker_output()
    
    print("\n如果听不到测试音频，可能的问题:")
    print("1. 系统音量设置过低")
    print("2. 默认输出设备不正确") 
    print("3. 耳机/扬声器没有连接")
    print("4. 音频被其他应用占用")