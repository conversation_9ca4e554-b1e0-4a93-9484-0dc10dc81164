#!/usr/bin/env python3
"""
音频路由测试工具 - 检查所有音频设备并提供选择
"""
import sounddevice as sd
import numpy as np
from queue import Queue
import threading
import time

class AudioRouterTest:
    def __init__(self):
        self.audio_queue = Queue(maxsize=100)
        self.is_running = False
        self.sample_rate = 48000
        self.channels = 1  # 使用单声道用于麦克风
        self.blocksize = 1024
        
    def list_audio_devices(self):
        """列出所有可用的音频设备"""
        devices = sd.query_devices()
        print("=== 可用音频设备 ===\n")
        
        input_devices = []
        output_devices = []
        
        for i, device in enumerate(devices):
            device_info = f"{i:2d}: {device['name']}"
            
            if device['max_input_channels'] > 0:
                input_devices.append({
                    'id': i,
                    'name': device['name'],
                    'channels': device['max_input_channels'],
                    'default_samplerate': device['default_samplerate']
                })
                print(f"🎤 输入设备 {device_info} (通道: {device['max_input_channels']})")
                
            if device['max_output_channels'] > 0:
                output_devices.append({
                    'id': i,
                    'name': device['name'],
                    'channels': device['max_output_channels'],
                    'default_samplerate': device['default_samplerate']
                })
                print(f"🔊 输出设备 {device_info} (通道: {device['max_output_channels']})")
        
        return input_devices, output_devices
    
    def test_device(self, device_id, device_name):
        """测试指定设备的音频输入"""
        print(f"\n=== 测试设备: {device_name} (ID: {device_id}) ===")
        print("开始录制...按Ctrl+C停止")
        
        def audio_callback(indata, frames, time, status):
            if status:
                print(f"音频状态: {status}")
            
            # 计算音量
            volume = np.sqrt(np.mean(indata**2))
            
            # 显示音量条
            bar_length = int(volume * 50)
            bar = '█' * bar_length + '░' * (50 - bar_length)
            print(f'\r设备 {device_id}: [{bar}] {volume:.4f}', end='', flush=True)
        
        try:
            with sd.InputStream(
                device=device_id,
                channels=self.channels,
                samplerate=self.sample_rate,
                blocksize=self.blocksize,
                dtype='float32',
                callback=audio_callback
            ):
                while True:
                    time.sleep(0.1)
                    
        except KeyboardInterrupt:
            print(f"\n设备 {device_id} 测试结束")
        except Exception as e:
            print(f"\n设备 {device_id} 测试失败: {e}")
    
    def create_microphone_to_blackhole_bridge(self, mic_device_id, blackhole_device_id):
        """创建从麦克风到BlackHole的音频桥接"""
        print(f"\n=== 创建音频桥接 ===")
        print(f"麦克风设备 ID: {mic_device_id}")
        print(f"BlackHole设备 ID: {blackhole_device_id}")
        print("按Ctrl+C停止桥接...")
        
        def bridge_callback(indata, outdata, frames, time, status):
            if status:
                print(f"桥接状态: {status}")
            
            # 将麦克风输入直接复制到BlackHole输出
            outdata[:] = indata
            
            # 显示音量
            volume = np.sqrt(np.mean(indata**2))
            if volume > 0.001:  # 只在有声音时显示
                bar_length = int(volume * 50)
                bar = '█' * bar_length + '░' * (50 - bar_length)
                print(f'\r桥接音量: [{bar}] {volume:.4f}', end='', flush=True)
        
        try:
            with sd.Stream(
                device=(mic_device_id, blackhole_device_id),
                channels=1,
                samplerate=self.sample_rate,
                blocksize=self.blocksize,
                dtype='float32',
                callback=bridge_callback
            ):
                while True:
                    time.sleep(0.1)
                    
        except KeyboardInterrupt:
            print("\n音频桥接已停止")
        except Exception as e:
            print(f"\n音频桥接失败: {e}")

def main():
    router = AudioRouterTest()
    
    # 列出所有设备
    input_devices, output_devices = router.list_audio_devices()
    
    print("\n" + "="*50)
    
    while True:
        print("\n请选择操作:")
        print("1. 测试输入设备")
        print("2. 创建麦克风到BlackHole桥接")
        print("3. 重新扫描设备")
        print("4. 退出")
        
        choice = input("\n请输入选择 (1-4): ").strip()
        
        if choice == '1':
            if not input_devices:
                print("没有找到输入设备")
                continue
                
            print("\n可用输入设备:")
            for device in input_devices:
                print(f"{device['id']}: {device['name']}")
                
            try:
                device_id = int(input("请输入要测试的设备ID: "))
                device_name = next((d['name'] for d in input_devices if d['id'] == device_id), "未知设备")
                router.test_device(device_id, device_name)
            except ValueError:
                print("请输入有效的数字")
            except KeyboardInterrupt:
                print("\n测试中断")
                
        elif choice == '2':
            # 查找麦克风设备
            print("\n选择麦克风设备:")
            mic_devices = [d for d in input_devices if 'AirPods' in d['name'] or 'Built-in' in d['name'] or '内置' in d['name']]
            
            for i, device in enumerate(mic_devices):
                print(f"{i}: {device['name']} (ID: {device['id']})")
                
            if not mic_devices:
                print("未找到麦克风设备")
                continue
                
            try:
                choice_mic = int(input("请选择麦克风: "))
                mic_device_id = mic_devices[choice_mic]['id']
            except (ValueError, IndexError):
                print("无效选择")
                continue
            
            # 查找BlackHole设备
            blackhole_devices = [d for d in output_devices if 'BlackHole' in d['name']]
            
            if not blackhole_devices:
                print("未找到BlackHole设备，请先安装BlackHole")
                continue
                
            print("\n选择BlackHole设备:")
            for i, device in enumerate(blackhole_devices):
                print(f"{i}: {device['name']} (ID: {device['id']})")
                
            try:
                choice_bh = int(input("请选择BlackHole设备: "))
                blackhole_device_id = blackhole_devices[choice_bh]['id']
            except (ValueError, IndexError):
                print("无效选择")
                continue
                
            router.create_microphone_to_blackhole_bridge(mic_device_id, blackhole_device_id)
            
        elif choice == '3':
            input_devices, output_devices = router.list_audio_devices()
            
        elif choice == '4':
            break
            
        else:
            print("无效选择，请重试")

if __name__ == "__main__":
    main()