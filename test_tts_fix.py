#!/usr/bin/env python3
"""
测试TTS修复
"""

import time
import numpy as np
from obs_ai_translator_mic import MicrophoneTranslatorSystem

def test_tts_output():
    """测试TTS输出"""
    print("=== 测试TTS输出修复 ===\n")
    
    system = MicrophoneTranslatorSystem(
        enable_tts=True,
        source_lang='zh',
        target_lang='en'
    )
    
    print(f"TTS启用: {system.translator.enable_tts}")
    
    # 测试1: 直接调用TTS
    print("\n1. 直接测试TTS播放...")
    try:
        test_texts = [
            "Hello, testing TTS output.",
            "This should play through your speakers.",
            "Can you hear this?"
        ]
        
        for text in test_texts:
            print(f"\n播放: {text}")
            system.translator._synthesize_and_play(text)
            time.sleep(3)  # 等待播放完成
            
    except Exception as e:
        print(f"TTS错误: {e}")
        return False
    
    # 测试2: 完整流程
    print("\n2. 测试完整流程（语音→识别→翻译→TTS）...")
    
    # 生成测试音频
    sample_rate = 48000
    duration = 1.5
    t = np.linspace(0, duration, int(sample_rate * duration), False)
    
    # 确保音量高于阈值
    audio = 0.012 * np.sin(2 * np.pi * 300 * t).astype(np.float32)
    audio = audio * np.hanning(len(audio))  # 添加包络
    
    volume = np.sqrt(np.mean(audio**2))
    print(f"测试音频音量: {volume:.5f}")
    print(f"VAD阈值: {system.translator.speech_threshold:.5f}")
    
    # 发送音频
    chunk_size = 4800
    for i in range(0, len(audio), chunk_size):
        chunk = audio[i:i+chunk_size]
        system.translator.process_audio(chunk)
        time.sleep(0.02)
    
    # 发送静音触发处理
    silence = np.zeros(chunk_size, dtype=np.float32)
    for i in range(10):
        system.translator.process_audio(silence)
        time.sleep(0.1)
        if not system.translator.is_speaking:
            break
    
    print("等待完整处理...")
    time.sleep(10)  # 等待识别→翻译→TTS
    
    system.translator.shutdown()
    
    print("\n=== 测试完成 ===")
    print("请确认：")
    print("1. 是否听到了3段测试语音？")
    print("2. 是否有翻译结果被TTS播报？")
    print("3. 如果使用耳机，是否有声音输出？")
    
    return True

if __name__ == "__main__":
    test_tts_output()