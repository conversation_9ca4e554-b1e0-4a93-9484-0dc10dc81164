#!/usr/bin/env python3
"""
实时监控VAD状态，找出为什么静音检测不工作
"""

import time
import numpy as np
from obs_ai_translator_mic import MicrophoneTranslatorSystem
import threading

def monitor_vad_status():
    """实时监控VAD状态"""
    print("=== 实时VAD状态监控 ===\n")
    
    system = MicrophoneTranslatorSystem(
        enable_tts=False,  # 禁用TTS简化测试
        source_lang='zh',
        target_lang='en'
    )
    
    translator = system.translator
    
    print(f"当前VAD设置:")
    print(f"  语音阈值: {translator.speech_threshold:.5f}")
    print(f"  静音超时: {translator.silence_timeout}秒")
    print(f"  最小语音: {translator.min_speech_duration}秒")
    print(f"  最大语音: {translator.max_speech_duration}秒")
    print("\n开始监控，请对着麦克风说话或保持安静...\n")
    
    # 监控线程
    def monitor():
        last_status = None
        silence_start = None
        
        while True:
            current_time = time.time()
            
            # 获取当前状态
            is_speaking = translator.is_speaking
            speech_start = translator.speech_start_time
            last_speech = translator.last_speech_time
            
            # 计算时间
            if is_speaking and speech_start > 0:
                total_duration = current_time - speech_start
                silence_duration = current_time - last_speech if last_speech > 0 else 0
                
                status = f"[说话中] 总长:{total_duration:.1f}s 静音:{silence_duration:.1f}s"
                
                # 检测异常
                if silence_duration > translator.silence_timeout:
                    status += f" ⚠️ 应该已经结束! (超时{silence_duration-translator.silence_timeout:.1f}s)"
            else:
                status = "[静音中]"
            
            # 只在状态变化时打印
            if status != last_status:
                print(f"\r{time.strftime('%H:%M:%S')} {status}        ", flush=True)
                last_status = status
            
            time.sleep(0.1)
    
    monitor_thread = threading.Thread(target=monitor, daemon=True)
    monitor_thread.start()
    
    # 生成测试音频模拟用户输入
    try:
        print("模拟测试:")
        print("1. 发送2秒模拟语音...")
        
        # 模拟语音
        sample_rate = 48000
        chunk_size = 4800
        
        # 用稍高音量确保被检测到
        t = np.linspace(0, 0.1, chunk_size, False)
        speech = 0.01 * np.sin(2 * np.pi * 300 * t).astype(np.float32)
        
        for i in range(20):  # 2秒语音
            translator.process_audio(speech)
            time.sleep(0.1)
        
        print("2. 发送静音，应该在0.8秒后结束...")
        
        # 发送静音
        silence = np.zeros(chunk_size, dtype=np.float32)
        
        for i in range(50):  # 5秒静音
            translator.process_audio(silence)
            time.sleep(0.1)
            
            # 每秒检查状态
            if i % 10 == 0:
                if not translator.is_speaking:
                    print(f"✅ VAD在{(i+1)*0.1:.1f}秒后正确结束")
                    break
        else:
            print(f"❌ VAD未在5秒内结束! 仍在说话状态: {translator.is_speaking}")
        
        # 再测试实际麦克风输入
        print("\n3. 现在测试真实麦克风输入...")
        print("   请说话然后停止，观察是否能检测到结束...")
        
        time.sleep(20)  # 20秒真实测试
        
    except Exception as e:
        print(f"监控错误: {e}")
    finally:
        print("\n\n=== 诊断结果 ===")
        
        if translator.is_speaking:
            current = time.time()
            duration = current - translator.speech_start_time if translator.speech_start_time else 0
            silence = current - translator.last_speech_time if translator.last_speech_time else 0
            
            print(f"❌ 问题确认: VAD仍在说话状态!")
            print(f"   已持续: {duration:.1f}秒")
            print(f"   静音时间: {silence:.1f}秒")
            
            if silence < translator.silence_timeout:
                print(f"   原因: 持续检测到'语音'(可能是噪声)")
                print(f"   建议: 提高阈值，当前{translator.speech_threshold:.5f}太低")
            else:
                print(f"   原因: 静音检测逻辑有BUG")
                print(f"   建议: 检查代码逻辑")
        else:
            print("✅ VAD状态正常")
        
        system.translator.shutdown()

def test_threshold_values():
    """测试不同阈值的效果"""
    print("\n=== 测试不同阈值 ===\n")
    
    thresholds = [0.001, 0.003, 0.005, 0.008, 0.01]
    
    for threshold in thresholds:
        print(f"\n测试阈值: {threshold:.3f}")
        
        # 临时修改阈值
        system = MicrophoneTranslatorSystem(
            enable_tts=False,
            source_lang='zh',
            target_lang='en'
        )
        
        system.translator.speech_threshold = threshold
        
        # 测试背景噪声
        silence = np.zeros(4800, dtype=np.float32)
        # 添加微小噪声模拟真实环境
        noise = np.random.normal(0, 0.002, 4800).astype(np.float32)
        test_audio = silence + noise
        
        # 计算音量
        volume = np.sqrt(np.mean(test_audio**2))
        
        print(f"  背景音量: {volume:.5f}")
        print(f"  检测为语音: {'是' if volume > threshold else '否'}")
        
        if volume > threshold:
            print(f"  ❌ 阈值太低，背景噪声被当作语音")
        else:
            print(f"  ✅ 阈值合适，能区分背景噪声")
        
        system.translator.shutdown()
        time.sleep(0.5)

if __name__ == "__main__":
    try:
        # 1. 监控实时状态
        monitor_vad_status()
        
        # 2. 测试不同阈值
        test_threshold_values()
        
    except KeyboardInterrupt:
        print("\n监控中断")
    except Exception as e:
        print(f"监控异常: {e}")
        import traceback
        traceback.print_exc()