#!/usr/bin/env python3
"""
实时同声传译核心模块 - 完全修复版
解决：
1. PaMacCore AUHAL Error -10863
2. 翻译延迟问题
3. 工作线程频繁重启
"""
import asyncio
import numpy as np
from collections import deque
import webrtcvad
import whisper
from openai import OpenAI
import edge_tts
import io
import wave
import time
import os
from threading import Thread, Lock, RLock, Event
from queue import Queue, Empty
import soundfile as sf
import logging
from audio_filter import filter_audio_text
from pydub import AudioSegment
import sounddevice as sd

class RealtimeTranslatorFixed:
    """修复版实时翻译器"""
    
    def __init__(self, source_lang='zh', target_lang='en', sample_rate=48000, 
                 audio_router=None, enable_tts=True, audio_capture=None):
        self.source_lang = source_lang
        self.target_lang = target_lang
        self.sample_rate = sample_rate
        self.audio_router = audio_router
        self.enable_tts = enable_tts
        self.audio_capture = audio_capture
        
        # 状态管理
        self.is_running = True
        self.state_lock = RLock()
        
        # VAD配置 - 更灵敏的设置
        self.vad = webrtcvad.Vad(1)  # 最灵敏模式
        self.frame_duration = 20  # 20ms帧，更快响应
        self.frame_size = int(self.sample_rate * self.frame_duration / 1000)
        
        # 音频缓冲 - 减小缓冲提高响应速度
        self.audio_buffer = deque(maxlen=int(self.sample_rate * 0.5))  # 0.5秒缓冲
        self.speech_segments = []
        self.is_speaking = False
        self.silence_count = 0
        self.speech_count = 0
        
        # 静音检测阈值 - 更快的结束检测
        self.max_silence_frames = 15  # 300ms静音即结束
        self.min_speech_frames = 5    # 100ms语音即开始
        
        # Whisper模型
        print("[系统] 加载Whisper模型...")
        self.whisper_model = whisper.load_model("base")  # 使用base模型，更快
        
        # OpenAI客户端
        self._setup_openai_client()
        
        # 工作队列 - 使用单一队列避免复杂性
        self.work_queue = Queue(maxsize=10)
        
        # 启动单一工作线程
        self.worker_thread = Thread(target=self._worker_loop, daemon=False)
        self.worker_thread.start()
        
        print("[系统] 翻译器初始化完成")
    
    def _setup_openai_client(self):
        """设置OpenAI客户端"""
        api_key = os.getenv('OPENAI_API_KEY')
        if not api_key:
            raise ValueError("请设置环境变量 OPENAI_API_KEY")
        
        # 简化的客户端设置
        self.openai_client = OpenAI(api_key=api_key, timeout=30.0)
        print("[系统] OpenAI客户端已配置")
    
    def process_audio(self, audio_data):
        """处理输入音频 - 优化的VAD"""
        if not self.is_running or len(audio_data) == 0:
            return
        
        # 确保单声道
        if len(audio_data.shape) > 1:
            audio_data = np.mean(audio_data, axis=1)
        
        # 计算音量
        volume = np.sqrt(np.mean(audio_data**2))
        
        # 添加到缓冲
        self.audio_buffer.extend(audio_data)
        
        # 简单的音量阈值检测
        is_speech = volume > 0.008  # 调整阈值
        
        with self.state_lock:
            if is_speech and not self.is_speaking:
                # 开始说话
                self.is_speaking = True
                self.speech_segments = list(self.audio_buffer)
                self.silence_count = 0
                print(f"[VAD] 检测到说话开始 (音量: {volume:.3f})")
                
            elif self.is_speaking:
                # 正在说话，添加音频
                self.speech_segments.extend(audio_data)
                
                if not is_speech:
                    # 静音帧
                    self.silence_count += 1
                    if self.silence_count > self.max_silence_frames:
                        # 说话结束
                        self._process_speech_segment()
                        self.is_speaking = False
                        self.speech_segments = []
                        self.silence_count = 0
                        print("[VAD] 检测到说话结束")
                else:
                    self.silence_count = 0
    
    def _process_speech_segment(self):
        """处理语音片段"""
        if len(self.speech_segments) < self.sample_rate * 0.3:  # 至少0.3秒
            return
        
        # 转换为numpy数组
        audio_segment = np.array(self.speech_segments, dtype=np.float32)
        
        # 添加到工作队列
        try:
            self.work_queue.put_nowait({
                'type': 'speech',
                'audio': audio_segment
            })
            print(f"[VAD] 语音片段已加入队列: {len(audio_segment)} samples")
        except:
            print("[VAD] 队列满，跳过")
    
    def _worker_loop(self):
        """单一工作线程处理所有任务"""
        while self.is_running:
            try:
                # 获取任务
                task = self.work_queue.get(timeout=1.0)
                
                if task['type'] == 'speech':
                    # 处理语音
                    self._process_speech(task['audio'])
                    
            except Empty:
                continue
            except Exception as e:
                print(f"[工作线程] 错误: {e}")
                continue
    
    def _process_speech(self, audio_segment):
        """处理语音：识别->翻译->TTS"""
        try:
            # 1. 语音识别
            print(f"[ASR] 开始识别...")
            text = self._recognize_speech(audio_segment)
            if not text:
                return
            
            # 2. 翻译
            print(f"[翻译] 开始翻译: {text}")
            translated = self._translate_text(text)
            if not translated:
                return
            
            # 3. TTS和播放
            if self.enable_tts and not self.audio_capture:
                print(f"[TTS] 合成: {translated[:30]}...")
                self._synthesize_and_play(translated)
                
        except Exception as e:
            print(f"[处理] 错误: {e}")
    
    def _recognize_speech(self, audio_segment):
        """语音识别"""
        try:
            # 转换为float32
            audio_float = audio_segment.astype(np.float32)
            
            # 重采样到16kHz
            if self.sample_rate != 16000:
                import librosa
                audio_16k = librosa.resample(
                    audio_float, 
                    orig_sr=self.sample_rate, 
                    target_sr=16000,
                    res_type='soxr_hq'  # 高质量重采样
                )
            else:
                audio_16k = audio_float
            
            # 使用Whisper识别
            result = self.whisper_model.transcribe(
                audio_16k,
                language=self.source_lang,
                task="transcribe",
                fp16=False
            )
            
            text = result['text'].strip()
            if text:
                print(f"[ASR] 识别结果: {text}")
                return text
                
        except Exception as e:
            print(f"[ASR] 识别失败: {e}")
        return None
    
    def _translate_text(self, text):
        """翻译文本"""
        try:
            response = self.openai_client.chat.completions.create(
                model="gpt-3.5-turbo",
                messages=[
                    {"role": "system", "content": f"Translate {self.source_lang} to {self.target_lang}. Only return the translation."},
                    {"role": "user", "content": text}
                ],
                temperature=0.3,
                max_tokens=200
            )
            
            translated = response.choices[0].message.content.strip()
            print(f"[翻译] 结果: {translated}")
            return translated
            
        except Exception as e:
            print(f"[翻译] 失败: {e}")
        return None
    
    def _synthesize_and_play(self, text):
        """TTS合成和播放 - 完全避免AUHAL错误"""
        if self.audio_capture:  # 麦克风模式完全禁用播放
            print("[TTS] 麦克风模式，跳过播放")
            return
            
        try:
            # 异步TTS合成
            async def synthesize():
                voice = "en-US-JennyNeural" if self.target_lang == 'en' else "zh-CN-XiaoxiaoNeural"
                communicate = edge_tts.Communicate(text, voice)
                audio_data = b""
                
                async for chunk in communicate.stream():
                    if chunk["type"] == "audio":
                        audio_data += chunk["data"]
                return audio_data
            
            # 运行异步合成
            audio_data = asyncio.run(synthesize())
            if not audio_data:
                return
            
            # 转换音频格式
            audio_segment = AudioSegment.from_mp3(io.BytesIO(audio_data))
            audio_segment = audio_segment.set_frame_rate(48000).set_channels(1)
            samples = audio_segment.get_array_of_samples()
            audio_np = np.array(samples).astype(np.float32) / 32768.0
            
            # 输出到BlackHole
            if self.audio_router:
                try:
                    self.audio_router.send_to_output(audio_np)
                    print(f"[音频] 已输出到BlackHole")
                except Exception as e:
                    print(f"[音频] BlackHole输出错误: {e}")
            
            # 不播放到本地扬声器，完全避免AUHAL错误
            print("[音频] 跳过本地播放以避免AUHAL错误")
            
        except Exception as e:
            print(f"[TTS] 错误: {e}")
    
    def shutdown(self):
        """关闭翻译器"""
        print("[系统] 开始关闭...")
        self.is_running = False
        
        # 等待工作线程结束
        if self.worker_thread.is_alive():
            self.worker_thread.join(timeout=2)
        
        print("[系统] 已关闭")

# 替换原有类
RealtimeTranslator = RealtimeTranslatorFixed