#!/usr/bin/env python3
"""
安全模式翻译器 - 禁用可能导致卡死的功能
"""
import sys
import os
import time
from datetime import datetime

def patch_translator_safe_mode():
    """为翻译器应用安全模式补丁"""
    from realtime_translator import RealtimeTranslator
    
    # 保存原始方法
    original_synthesize_and_play = RealtimeTranslator._synthesize_and_play
    original_process_speech = RealtimeTranslator._process_speech
    
    def safe_synthesize_and_play(self, text):
        """安全模式：跳过TTS，只显示翻译结果"""
        print(f"\n🔊 [TTS跳过] 翻译结果: {text}")
        print(f"⏰ 时间: {datetime.now().strftime('%H:%M:%S')}")
        print("-" * 50)
        # 不执行实际的TTS，避免可能的卡死
        return
    
    def safe_process_speech(self, audio_segment):
        """安全模式的语音处理：只做识别和翻译，跳过TTS"""
        try:
            print(f"[安全模式] 开始处理语音片段...")
            
            # 1. 语音识别
            print(f"[ASR] 开始识别...")
            text = self._recognize_speech(audio_segment)
            if not text:
                print(f"[ASR] 识别结果为空，跳过")
                return
            
            print(f"[ASR] ✅ 识别结果: {text}")
            
            # 2. 翻译
            print(f"[翻译] 开始翻译...")
            translated = self._translate_text(text)
            if not translated:
                print(f"[翻译] 翻译结果为空")
                return
            
            print(f"[翻译] ✅ 翻译结果: {translated}")
            
            # 3. 跳过TTS，直接显示结果
            print(f"\n" + "="*60)
            print(f"🎤 原文: {text}")
            print(f"🌍 译文: {translated}")
            print(f"⏰ 时间: {datetime.now().strftime('%H:%M:%S')}")
            print(f"="*60 + "\n")
            
            # 可选：输出到BlackHole（如果需要OBS捕获）
            if hasattr(self, 'audio_router') and self.audio_router:
                print(f"[音频] 跳过音频输出（安全模式）")
                
        except Exception as e:
            print(f"[安全模式] 处理错误: {e}")
            import traceback
            traceback.print_exc()
    
    # 应用安全模式补丁
    RealtimeTranslator._synthesize_and_play = safe_synthesize_and_play
    RealtimeTranslator._process_speech = safe_process_speech
    
    print("✅ 安全模式补丁已应用")
    print("   - TTS功能已禁用")
    print("   - 音频播放已跳过")
    print("   - 只保留识别和翻译功能")

def main():
    print("=== 安全模式AI翻译器 ===")
    print("此版本禁用了可能导致卡死的功能:")
    print("❌ TTS语音合成")
    print("❌ 音频播放")
    print("❌ 复杂的异步操作")
    print("✅ 语音识别")
    print("✅ 文本翻译")
    print("✅ 结果显示")
    print()
    
    # 检查环境变量
    if not os.getenv('OPENAI_API_KEY'):
        print("❌ 请先设置 OPENAI_API_KEY 环境变量")
        print("export OPENAI_API_KEY='your-api-key'")
        sys.exit(1)
    
    try:
        # 应用安全模式补丁
        patch_translator_safe_mode()
        
        # 导入并启动系统
        from obs_ai_translator_main import AITranslatorSystem
        
        # 检查是否为测试模式
        test_mode = '--test' in sys.argv or '-t' in sys.argv
        
        # 获取OBS密码
        obs_password = ""
        if not test_mode:
            obs_password = os.getenv('OBS_WEBSOCKET_PASSWORD', '')
            if not obs_password:
                try:
                    obs_password = input("请输入OBS WebSocket密码(可留空): ") or ""
                except (EOFError, KeyboardInterrupt):
                    obs_password = ""
                    print("\n使用空密码连接OBS")
        
        # 创建系统实例（禁用TTS）
        system = AITranslatorSystem(obs_password)
        
        print("\n💡 使用提示:")
        print("- 按 'r' + Enter: 重置系统")
        print("- 按 'q' + Enter: 退出系统")
        print("- 翻译结果将直接显示在控制台")
        print("- 如果此版本不卡死，说明问题出在TTS或音频播放")
        print()
        
        # 启动系统
        system.start(test_mode=test_mode)
        
    except KeyboardInterrupt:
        print("\n用户中断，正在退出...")
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
