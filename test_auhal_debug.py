#!/usr/bin/env python3
"""
深度调试PaMacCore AUHAL Error -10863
测试不同的音频输出方案
"""

import sys
import time
import numpy as np
import sounddevice as sd
from obs_ai_translator_mic import MicrophoneTranslatorSystem, MicrophoneAudioCapture

def list_all_audio_devices():
    """列出所有音频设备"""
    print("=== 所有音频设备 ===")
    devices = sd.query_devices()
    for i, device in enumerate(devices):
        print(f"ID {i}: {device['name']}")
        print(f"  输入通道: {device['max_input_channels']}")
        print(f"  输出通道: {device['max_output_channels']}")
        print(f"  采样率: {device['default_samplerate']}")
        print()

def test_direct_audio_playback():
    """直接测试sounddevice播放"""
    print("=== 直接测试sounddevice播放 ===")
    
    # 生成测试音频
    duration = 1.0  # seconds
    fs = 48000
    t = np.linspace(0, duration, int(fs * duration), False)
    audio = 0.1 * np.sin(2 * np.pi * 440 * t)  # 440Hz sine wave
    
    print("1. 测试默认输出设备...")
    try:
        sd.play(audio, fs, blocking=True)
        print("✅ 默认设备播放成功")
    except Exception as e:
        print(f"❌ 默认设备播放失败: {e}")
        if "-10863" in str(e):
            print("🔍 发现PaMacCore Error -10863")
    
    print("\n2. 测试所有输出设备...")
    devices = sd.query_devices()
    for i, device in enumerate(devices):
        if device['max_output_channels'] > 0:
            print(f"\n测试设备 {i}: {device['name']}")
            try:
                sd.play(audio, fs, device=i, blocking=True)
                print(f"✅ 设备 {i} 播放成功")
                return i  # 返回第一个成功的设备
            except Exception as e:
                print(f"❌ 设备 {i} 播放失败: {e}")
                if "-10863" in str(e):
                    print("🔍 发现PaMacCore Error -10863")
    
    return None

def test_device_conflicts():
    """测试设备冲突"""
    print("\n=== 测试设备冲突 ===")
    
    # 创建麦克风捕获
    print("1. 创建麦克风捕获...")
    mic_capture = MicrophoneAudioCapture()
    
    print(f"麦克风设备ID: {mic_capture.device_id}")
    
    # 获取设备信息
    devices = sd.query_devices()
    if mic_capture.device_id is not None:
        mic_device = devices[mic_capture.device_id]
        print(f"麦克风设备: {mic_device['name']}")
        print(f"输入通道: {mic_device['max_input_channels']}")
        print(f"输出通道: {mic_device['max_output_channels']}")
    
    # 测试在麦克风激活时播放音频
    print("\n2. 启动麦克风捕获...")
    def audio_callback(indata, volume):
        if volume > 0.001:
            print(f"检测到音频: {volume:.4f}")
    
    # 在后台线程启动麦克风
    import threading
    capture_thread = threading.Thread(
        target=mic_capture.start_capture,
        args=(audio_callback,)
    )
    capture_thread.daemon = True
    capture_thread.start()
    
    time.sleep(1)  # 让麦克风稳定
    
    print("\n3. 在麦克风激活时测试音频播放...")
    
    # 生成测试音频
    duration = 0.5
    fs = 48000
    t = np.linspace(0, duration, int(fs * duration), False)
    audio = 0.05 * np.sin(2 * np.pi * 880 * t)  # 880Hz低音量
    
    # 测试不同设备
    devices = sd.query_devices()
    for i, device in enumerate(devices):
        if device['max_output_channels'] > 0:
            # 跳过麦克风设备
            if i == mic_capture.device_id:
                print(f"⏭️  跳过麦克风设备 {i}: {device['name']}")
                continue
                
            print(f"\n测试设备 {i}: {device['name']}")
            try:
                sd.play(audio, fs, device=i, blocking=False)
                time.sleep(0.6)  # 等待播放完成
                sd.stop()
                print(f"✅ 设备 {i} 播放成功（麦克风激活时）")
            except Exception as e:
                print(f"❌ 设备 {i} 播放失败: {e}")
                if "-10863" in str(e):
                    print("🔍 发现PaMacCore Error -10863（冲突）")
    
    # 停止麦克风
    mic_capture.is_running = False
    time.sleep(0.5)

def test_microphone_translator_system():
    """测试完整的麦克风翻译系统"""
    print("\n=== 测试麦克风翻译系统 ===")
    
    print("1. 创建麦克风翻译系统...")
    system = MicrophoneTranslatorSystem(
        enable_tts=True,
        source_lang='zh',
        target_lang='en'
    )
    
    print("2. 检查系统配置...")
    print(f"   TTS启用: {system.translator.enable_tts}")
    print(f"   音频捕获: {system.translator.audio_capture is not None}")
    print(f"   音频路由: {system.translator.audio_router}")
    
    print("\n3. 模拟翻译请求（可能触发TTS播放）...")
    
    # 模拟音频数据
    test_audio = np.random.normal(0, 0.02, 16000).astype(np.float32)
    
    try:
        system.translator.process_audio(test_audio)
        time.sleep(3)  # 等待处理完成
        print("✅ 翻译处理完成")
    except Exception as e:
        print(f"❌ 翻译处理失败: {e}")
        if "-10863" in str(e):
            print("🔍 在翻译系统中发现PaMacCore Error -10863")
    
    print("\n4. 清理系统...")
    system.translator.shutdown()

def main():
    """主测试函数"""
    print("=== PaMacCore AUHAL Error -10863 深度调试 ===\n")
    
    # 1. 列出所有设备
    list_all_audio_devices()
    
    # 2. 测试直接播放
    working_device = test_direct_audio_playback()
    
    # 3. 测试设备冲突
    test_device_conflicts()
    
    # 4. 测试完整系统
    test_microphone_translator_system()
    
    print("\n=== 调试完成 ===")
    if working_device is not None:
        print(f"✅ 发现可工作的音频设备: {working_device}")
    else:
        print("❌ 未发现任何可工作的音频设备")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n测试被中断")
        sys.exit(1)
    except Exception as e:
        print(f"测试失败: {e}")
        sys.exit(1)