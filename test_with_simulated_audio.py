#!/usr/bin/env python3
"""
使用模拟音频测试完整翻译管道
"""
import asyncio
import numpy as np
from realtime_translator import RealtimeTranslator

def generate_test_audio():
    """生成模拟的语音音频"""
    # 生成包含语音的音频信号 (模拟说话)
    sample_rate = 48000
    duration = 3.0  # 3秒
    
    # 生成基频和谐波 (模拟人声)
    t = np.linspace(0, duration, int(sample_rate * duration), False)
    
    # 基频 (约150Hz，男性说话)
    fundamental = 0.3 * np.sin(2 * np.pi * 150 * t)
    
    # 添加谐波
    harmonic2 = 0.2 * np.sin(2 * np.pi * 300 * t)
    harmonic3 = 0.1 * np.sin(2 * np.pi * 450 * t)
    
    # 添加一些噪声
    noise = 0.05 * np.random.normal(0, 1, len(t))
    
    # 组合信号
    speech_signal = fundamental + harmonic2 + harmonic3 + noise
    
    # 添加包络 (音量变化，模拟自然说话)
    envelope = np.abs(np.sin(2 * np.pi * 2 * t)) * 0.8 + 0.2
    speech_signal *= envelope
    
    return speech_signal.astype(np.float32)

async def test_full_pipeline():
    """测试完整的翻译管道"""
    print("=== 模拟音频翻译管道测试 ===\n")
    
    # 创建翻译器
    print("1. 初始化翻译器...")
    translator = RealtimeTranslator(source_lang='zh', target_lang='en')
    print("✓ 翻译器初始化完成")
    
    # 生成模拟音频
    print("\n2. 生成模拟语音音频...")
    audio_data = generate_test_audio()
    print(f"✓ 生成音频: {len(audio_data)} samples, {len(audio_data)/48000:.2f}秒")
    
    # 模拟实时音频输入
    print("\n3. 模拟实时音频输入...")
    chunk_size = 1024
    
    for i in range(0, len(audio_data), chunk_size):
        chunk = audio_data[i:i+chunk_size]
        if len(chunk) < chunk_size:
            # 零填充最后一块
            chunk = np.pad(chunk, (0, chunk_size - len(chunk)), 'constant')
        
        translator.process_audio(chunk)
        
        # 模拟实时延迟
        await asyncio.sleep(0.021)  # 21ms (1024/48000)
    
    print("\n4. 等待处理完成...")
    await asyncio.sleep(5)
    
    print("\n=== 测试完成 ===")
    print("注意：这个测试使用模拟的语音信号，不包含实际的中文语音内容")
    print("如果看到ASR处理日志，说明VAD和音频处理管道工作正常")

if __name__ == "__main__":
    asyncio.run(test_full_pipeline())