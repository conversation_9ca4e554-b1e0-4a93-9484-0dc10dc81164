#!/bin/bash

# AI同声传译系统启动脚本

echo "=== AI同声传译系统启动 ==="

# 检查Python环境
if ! command -v python3 &> /dev/null; then
    echo "❌ 未找到Python3"
    exit 1
fi

# 检查环境变量
if [ -z "$OPENAI_API_KEY" ]; then
    echo "❌ 未设置 OPENAI_API_KEY 环境变量"
    echo "请运行: export OPENAI_API_KEY='your-api-key'"
    exit 1
fi

echo "✓ OpenAI API Key 已设置"

# 激活虚拟环境（如果存在）
if [ -d "venv" ]; then
    echo "✓ 激活虚拟环境"
    source venv/bin/activate
fi

# 检查依赖
echo "📦 检查依赖包..."
pip install -q -r requirements.txt

# 启动程序
echo "🚀 启动AI同声传译系统..."
python3 obs_ai_translator_main.py "$@"