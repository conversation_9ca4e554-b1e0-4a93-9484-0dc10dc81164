#!/usr/bin/env python3
"""
测试静音计时器修复
"""

import time
import numpy as np
from obs_ai_translator_mic import MicrophoneTranslatorSystem

def test_silence_timer_fix():
    """测试修复后的静音计时器"""
    print("=== 测试静音计时器修复 ===\n")
    
    system = MicrophoneTranslatorSystem(
        enable_tts=False,
        source_lang='zh',
        target_lang='en'
    )
    
    translator = system.translator
    
    print(f"VAD配置:")
    print(f"  语音阈值: {translator.speech_threshold:.5f}")
    print(f"  静音超时: {translator.silence_timeout}秒")
    
    # 测试场景：语音 -> 边缘噪声 -> 应该能正确结束
    print("\n模拟场景：语音结束后边缘噪声不应重置计时")
    print("-" * 60)
    
    sample_rate = 48000
    chunk_size = 4800
    
    # 1. 发送清晰语音（1.5秒）
    print("1. 发送1.5秒清晰语音...")
    t = np.linspace(0, 0.1, chunk_size, False)
    clear_speech = 0.025 * np.sin(2 * np.pi * 300 * t).astype(np.float32)
    
    for i in range(15):
        translator.process_audio(clear_speech)
        time.sleep(0.1)
    
    speech_end_time = time.time()
    print(f"语音结束，当前状态: 说话中={translator.is_speaking}")
    
    # 2. 发送边缘噪声（在0.01附近波动，但低于新阈值0.015）
    print("2. 发送边缘噪声（应该不会重置计时）...")
    edge_noise_levels = [0.008, 0.012, 0.009, 0.013, 0.007, 0.011, 0.008, 0.010]
    
    silence_detected = False
    
    for i in range(20):  # 2秒
        noise_level = edge_noise_levels[i % len(edge_noise_levels)]
        noise = np.random.normal(0, noise_level, chunk_size).astype(np.float32)
        
        translator.process_audio(noise)
        time.sleep(0.1)
        
        # 检查是否结束
        if not translator.is_speaking:
            end_time = time.time()
            actual_silence_duration = end_time - speech_end_time
            print(f"✅ VAD正确结束！")
            print(f"   实际用时: {actual_silence_duration:.2f}秒")
            print(f"   期望: ~{translator.silence_timeout}秒")
            silence_detected = True
            break
        
        # 显示当前状态
        if i % 5 == 0:
            current_time = time.time()
            elapsed = current_time - speech_end_time
            print(f"   {elapsed:.1f}秒: 仍在说话状态，噪声音量:{noise_level:.3f}")
    
    if not silence_detected:
        print(f"❌ VAD未在2秒内结束，仍在说话状态")
        system.translator.shutdown()
        return False
    
    system.translator.shutdown()
    return True

def test_real_silence_scenario():
    """测试真实静音场景"""
    print("\n=== 测试真实静音场景 ===\n")
    
    system = MicrophoneTranslatorSystem(
        enable_tts=False,
        source_lang='zh',
        target_lang='en'
    )
    
    translator = system.translator
    translator.debug_vad = False  # 减少输出
    
    sample_rate = 48000
    chunk_size = 4800
    
    # 1. 语音
    print("1. 发送1秒语音...")
    t = np.linspace(0, 0.1, chunk_size, False)
    speech = 0.02 * np.sin(2 * np.pi * 300 * t).astype(np.float32)
    
    for i in range(10):
        translator.process_audio(speech)
        time.sleep(0.1)
    
    speech_end_time = time.time()
    
    # 2. 真实静音
    print("2. 发送真实静音...")
    silence = np.zeros(chunk_size, dtype=np.float32)
    
    for i in range(15):  # 1.5秒
        translator.process_audio(silence)
        time.sleep(0.1)
        
        if not translator.is_speaking:
            end_time = time.time()
            silence_duration = end_time - speech_end_time
            print(f"✅ 真实静音正确结束: {silence_duration:.2f}秒")
            system.translator.shutdown()
            return True
    
    print(f"❌ 真实静音未能结束")
    system.translator.shutdown()
    return False

def main():
    """主测试"""
    print("=== 静音计时器修复验证 ===\n")
    print("关键修复:")
    print("🔧 修复逻辑: 'if self.is_speaking and not is_speech' → 'elif self.is_speaking'")
    print("🔧 提高阈值: 0.01 → 0.015 (远离边缘噪声)")
    print("🔧 现在边缘噪声不会重置静音计时")
    print("\n" + "="*60 + "\n")
    
    results = []
    
    # 测试1: 边缘噪声场景
    results.append(("边缘噪声测试", test_silence_timer_fix()))
    
    time.sleep(1)
    
    # 测试2: 真实静音场景  
    results.append(("真实静音测试", test_real_silence_scenario()))
    
    # 结果
    print("\n" + "="*60)
    print("修复验证结果")
    print("="*60)
    
    for name, result in results:
        status = "✅ 通过" if result else "❌ 失败" 
        print(f"{name:<15} {status}")
    
    all_passed = all(result for _, result in results)
    
    if all_passed:
        print(f"\n🎉 静音计时器修复成功！")
        print("现在VAD应该能:")
        print("• 正确累积静音时间到0.8秒")
        print("• 不被边缘噪声重置计时")
        print("• 在真正静音时正确结束")
    else:
        print(f"\n⚠️ 部分测试失败，可能需要进一步调整阈值")

if __name__ == "__main__":
    main()