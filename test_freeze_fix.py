#!/usr/bin/env python3
"""
测试修复后的系统是否还会出现"一次互动后卡死"的问题
"""
import time
import numpy as np
import threading
from realtime_translator import RealtimeTranslator
import os

def test_multiple_interactions():
    """测试多次连续互动"""
    print("=== 测试多次连续互动 ===")
    
    # 设置测试环境变量（如果没有的话）
    if not os.getenv('OPENAI_API_KEY'):
        print("❌ 请设置 OPENAI_API_KEY 环境变量")
        return False
    
    try:
        # 创建翻译器实例
        translator = RealtimeTranslator(
            source_lang='zh',
            target_lang='en',
            sample_rate=48000,
            enable_tts=True
        )
        
        print("✅ 翻译器初始化成功")
        
        # 模拟多次语音输入
        test_cases = [
            "你好，这是第一次测试",
            "这是第二次测试，看看会不会卡死",
            "第三次测试，系统应该还能正常工作",
            "最后一次测试，验证修复效果"
        ]
        
        for i, test_text in enumerate(test_cases, 1):
            print(f"\n--- 第 {i} 次互动测试 ---")
            print(f"模拟输入: {test_text}")
            
            # 生成模拟音频数据（1秒的正弦波）
            duration = 1.0
            sample_rate = 48000
            frequency = 440  # A4音符
            t = np.linspace(0, duration, int(sample_rate * duration), False)
            audio_data = 0.1 * np.sin(2 * np.pi * frequency * t)
            
            # 模拟语音检测过程
            start_time = time.time()
            
            # 分块发送音频数据，模拟实时输入
            chunk_size = 1024
            for j in range(0, len(audio_data), chunk_size):
                chunk = audio_data[j:j+chunk_size]
                translator.process_audio(chunk)
                time.sleep(0.02)  # 模拟实时间隔
            
            # 发送静音数据触发处理
            silence_duration = 1.0  # 1秒静音
            silence_samples = int(sample_rate * silence_duration)
            silence_data = np.zeros(silence_samples)
            
            for j in range(0, len(silence_data), chunk_size):
                chunk = silence_data[j:j+chunk_size]
                translator.process_audio(chunk)
                time.sleep(0.02)
            
            # 等待处理完成
            processing_timeout = 30  # 30秒超时
            wait_start = time.time()
            
            while translator.work_queue.qsize() > 0:
                if time.time() - wait_start > processing_timeout:
                    print(f"❌ 第 {i} 次测试超时！队列大小: {translator.work_queue.qsize()}")
                    return False
                time.sleep(0.1)
            
            elapsed = time.time() - start_time
            print(f"✅ 第 {i} 次测试完成 (耗时: {elapsed:.1f}s)")
            
            # 检查系统状态
            if not translator.is_running:
                print(f"❌ 第 {i} 次测试后系统停止运行")
                return False
            
            if not translator.worker_thread.is_alive():
                print(f"❌ 第 {i} 次测试后工作线程死亡")
                return False
            
            print(f"✅ 第 {i} 次测试后系统状态正常")
            
            # 测试间隔
            time.sleep(2)
        
        print("\n=== 所有测试完成 ===")
        print("✅ 系统在多次互动后仍能正常工作")
        
        # 清理
        translator.shutdown()
        return True
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_reset_functionality():
    """测试重置功能"""
    print("\n=== 测试重置功能 ===")
    
    try:
        translator = RealtimeTranslator(
            source_lang='zh',
            target_lang='en',
            sample_rate=48000,
            enable_tts=False  # 禁用TTS加快测试
        )
        
        # 添加一些任务到队列
        for i in range(5):
            audio_data = np.random.random(1000).astype(np.float32)
            translator.process_audio(audio_data)
        
        print(f"添加任务后队列大小: {translator.work_queue.qsize()}")
        
        # 执行重置
        translator.reset_system()
        
        # 检查重置效果
        time.sleep(1)  # 等待重置完成
        
        if translator.work_queue.qsize() == 0:
            print("✅ 队列重置成功")
        else:
            print(f"❌ 队列重置失败，剩余: {translator.work_queue.qsize()}")
            return False
        
        if translator.worker_thread.is_alive():
            print("✅ 工作线程重置后仍然健康")
        else:
            print("❌ 工作线程重置后死亡")
            return False
        
        translator.shutdown()
        return True
        
    except Exception as e:
        print(f"❌ 重置测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("开始测试修复后的系统...")
    
    # 测试1: 多次互动
    success1 = test_multiple_interactions()
    
    # 测试2: 重置功能
    success2 = test_reset_functionality()
    
    print(f"\n=== 测试结果汇总 ===")
    print(f"多次互动测试: {'✅ 通过' if success1 else '❌ 失败'}")
    print(f"重置功能测试: {'✅ 通过' if success2 else '❌ 失败'}")
    
    if success1 and success2:
        print("\n🎉 所有测试通过！修复应该有效。")
        return True
    else:
        print("\n❌ 部分测试失败，可能需要进一步调试。")
        return False

if __name__ == "__main__":
    main()
