#!/usr/bin/env python3
"""
快速音频设备切换工具
"""
import subprocess
import sys

def get_audio_devices():
    """获取音频设备列表"""
    try:
        # 使用AppleScript获取音频设备
        script = '''
        tell application "System Preferences"
            reveal pane "com.apple.preference.sound"
            delay 1
        end tell
        
        tell application "System Events"
            tell process "System Preferences"
                tell tab group 1 of window 1
                    click radio button "Output"
                    delay 0.5
                    set deviceList to {}
                    repeat with theRow in (rows of table 1 of scroll area 1)
                        set deviceName to value of text field 1 of theRow
                        set end of deviceList to deviceName
                    end repeat
                end tell
            end tell
        end tell
        
        tell application "System Preferences" to quit
        
        return deviceList
        '''
        
        result = subprocess.run(['osascript', '-e', script], capture_output=True, text=True)
        if result.returncode == 0:
            devices = result.stdout.strip().split(', ')
            return [d.strip() for d in devices if d.strip()]
        else:
            return []
    except:
        return []

def switch_to_device(device_name):
    """切换到指定音频设备"""
    script = f'''
    tell application "System Preferences"
        reveal pane "com.apple.preference.sound"
        delay 1
    end tell
    
    tell application "System Events"
        tell process "System Preferences"
            tell tab group 1 of window 1
                click radio button "Output"
                delay 0.5
                repeat with theRow in (rows of table 1 of scroll area 1)
                    if value of text field 1 of theRow is "{device_name}" then
                        click theRow
                        exit repeat
                    end if
                end repeat
            end tell
        end tell
    end tell
    
    tell application "System Preferences" to quit
    '''
    
    result = subprocess.run(['osascript', '-e', script], capture_output=True, text=True)
    return result.returncode == 0

def quick_switch():
    """快速切换音频设备"""
    print("=== 音频设备切换工具 ===\n")
    
    # 常见设备快速选项
    common_devices = [
        "Rockenlee的AirPods Pro #2",
        "MacBook Pro扬声器", 
        "AI同传输出",
        "多输出设备"
    ]
    
    print("快速选项:")
    for i, device in enumerate(common_devices):
        print(f"{i+1}. {device}")
    print("0. 退出")
    
    try:
        choice = input("\n请选择要切换的设备 (1-4, 0退出): ").strip()
        
        if choice == "0":
            print("已取消")
            return
            
        choice_idx = int(choice) - 1
        if 0 <= choice_idx < len(common_devices):
            device_name = common_devices[choice_idx]
            print(f"\n正在切换到: {device_name}")
            
            if switch_to_device(device_name):
                print(f"✅ 已成功切换到: {device_name}")
            else:
                print(f"❌ 切换失败: {device_name}")
        else:
            print("无效选择")
            
    except ValueError:
        print("请输入有效数字")
    except KeyboardInterrupt:
        print("\n已取消")

if __name__ == "__main__":
    quick_switch()