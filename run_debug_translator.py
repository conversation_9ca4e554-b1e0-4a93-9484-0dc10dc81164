#!/usr/bin/env python3
"""
调试版翻译器 - 添加详细日志和超时保护
"""
import sys
import os
import time
import threading
import signal
from datetime import datetime

# 添加详细日志记录
class DebugLogger:
    def __init__(self, log_file="debug_translator.log"):
        self.log_file = log_file
        self.lock = threading.Lock()
        
    def log(self, message, level="INFO"):
        timestamp = datetime.now().strftime("%H:%M:%S.%f")[:-3]
        thread_id = threading.current_thread().ident
        log_msg = f"[{timestamp}] [{level}] [T-{thread_id}] {message}"
        
        with self.lock:
            print(log_msg)
            try:
                with open(self.log_file, "a", encoding="utf-8") as f:
                    f.write(log_msg + "\n")
            except:
                pass

# 全局日志器
debug_logger = DebugLogger()

def log_debug(message):
    debug_logger.log(message, "DEBUG")

def log_info(message):
    debug_logger.log(message, "INFO")

def log_warning(message):
    debug_logger.log(message, "WARN")

def log_error(message):
    debug_logger.log(message, "ERROR")

# 超时装饰器
def timeout_handler(timeout_seconds):
    def decorator(func):
        def wrapper(*args, **kwargs):
            result = [None]
            exception = [None]
            
            def target():
                try:
                    result[0] = func(*args, **kwargs)
                except Exception as e:
                    exception[0] = e
            
            thread = threading.Thread(target=target)
            thread.daemon = True
            thread.start()
            thread.join(timeout_seconds)
            
            if thread.is_alive():
                log_error(f"{func.__name__} 超时 ({timeout_seconds}s)")
                raise TimeoutError(f"{func.__name__} 超时")
            
            if exception[0]:
                raise exception[0]
            
            return result[0]
        return wrapper
    return decorator

# 修补翻译器类
def patch_translator():
    """为翻译器添加调试功能"""
    from realtime_translator import RealtimeTranslator
    
    # 保存原始方法
    original_process_speech = RealtimeTranslator._process_speech
    original_recognize_speech = RealtimeTranslator._recognize_speech
    original_translate_text = RealtimeTranslator._translate_text
    original_synthesize_and_play = RealtimeTranslator._synthesize_and_play
    
    # 添加超时和日志的包装方法
    @timeout_handler(60)
    def debug_process_speech(self, audio_segment):
        log_info(f"开始处理语音片段: {len(audio_segment)} samples")
        start_time = time.time()
        try:
            result = original_process_speech(self, audio_segment)
            duration = time.time() - start_time
            log_info(f"语音处理完成，耗时: {duration:.2f}s")
            return result
        except Exception as e:
            duration = time.time() - start_time
            log_error(f"语音处理失败，耗时: {duration:.2f}s，错误: {e}")
            raise
    
    @timeout_handler(30)
    def debug_recognize_speech(self, audio_segment):
        log_info("开始语音识别...")
        start_time = time.time()
        try:
            result = original_recognize_speech(self, audio_segment)
            duration = time.time() - start_time
            log_info(f"语音识别完成，耗时: {duration:.2f}s，结果: {result}")
            return result
        except Exception as e:
            duration = time.time() - start_time
            log_error(f"语音识别失败，耗时: {duration:.2f}s，错误: {e}")
            raise
    
    @timeout_handler(45)
    def debug_translate_text(self, text):
        log_info(f"开始翻译: {text}")
        start_time = time.time()
        try:
            result = original_translate_text(self, text)
            duration = time.time() - start_time
            log_info(f"翻译完成，耗时: {duration:.2f}s，结果: {result}")
            return result
        except Exception as e:
            duration = time.time() - start_time
            log_error(f"翻译失败，耗时: {duration:.2f}s，错误: {e}")
            raise
    
    @timeout_handler(60)
    def debug_synthesize_and_play(self, text):
        log_info(f"开始TTS合成: {text[:50]}...")
        start_time = time.time()
        try:
            result = original_synthesize_and_play(self, text)
            duration = time.time() - start_time
            log_info(f"TTS完成，耗时: {duration:.2f}s")
            return result
        except Exception as e:
            duration = time.time() - start_time
            log_error(f"TTS失败，耗时: {duration:.2f}s，错误: {e}")
            raise
    
    # 应用补丁
    RealtimeTranslator._process_speech = debug_process_speech
    RealtimeTranslator._recognize_speech = debug_recognize_speech
    RealtimeTranslator._translate_text = debug_translate_text
    RealtimeTranslator._synthesize_and_play = debug_synthesize_and_play
    
    log_info("翻译器调试补丁已应用")

def setup_signal_handlers():
    """设置信号处理器"""
    def signal_handler(signum, frame):
        log_warning(f"收到信号 {signum}，准备退出...")
        sys.exit(0)
    
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

def main():
    log_info("=== 调试版AI翻译器启动 ===")
    log_info("增强功能:")
    log_info("1. ✅ 详细的操作日志记录")
    log_info("2. ✅ 每个操作的超时保护")
    log_info("3. ✅ 线程状态监控")
    log_info("4. ✅ 性能计时统计")
    
    # 设置信号处理
    setup_signal_handlers()
    
    # 检查环境变量
    if not os.getenv('OPENAI_API_KEY'):
        log_error("请先设置 OPENAI_API_KEY 环境变量")
        sys.exit(1)
    
    try:
        # 应用调试补丁
        patch_translator()
        
        # 导入并启动系统
        from obs_ai_translator_main import AITranslatorSystem
        
        # 检查是否为测试模式
        test_mode = '--test' in sys.argv or '-t' in sys.argv
        
        # 获取OBS密码
        obs_password = ""
        if not test_mode:
            obs_password = os.getenv('OBS_WEBSOCKET_PASSWORD', '')
            if not obs_password:
                try:
                    obs_password = input("请输入OBS WebSocket密码(可留空): ") or ""
                except (EOFError, KeyboardInterrupt):
                    obs_password = ""
                    log_info("使用空密码连接OBS")
        
        log_info(f"创建翻译系统实例 (测试模式: {test_mode})")
        system = AITranslatorSystem(obs_password)
        
        log_info("启动心跳监控...")
        def heartbeat():
            while True:
                time.sleep(10)
                log_debug("系统心跳 - 运行正常")
        
        heartbeat_thread = threading.Thread(target=heartbeat, daemon=True)
        heartbeat_thread.start()
        
        log_info("系统启动中...")
        system.start(test_mode=test_mode)
        
    except KeyboardInterrupt:
        log_info("用户中断，正在退出...")
    except Exception as e:
        log_error(f"启动失败: {e}")
        import traceback
        log_error(traceback.format_exc())
        sys.exit(1)

if __name__ == "__main__":
    main()
