#!/usr/bin/env python3
"""
测试VAD静音检测修复
验证0.8秒静音后能正确结束
"""

import time
import numpy as np
from obs_ai_translator_mic import MicrophoneTranslatorSystem

def test_silence_detection():
    """测试静音检测是否正常工作"""
    print("=== 测试VAD静音检测修复 ===\n")
    
    system = MicrophoneTranslatorSystem(
        enable_tts=False,
        source_lang='zh',
        target_lang='en'
    )
    
    translator = system.translator
    
    print(f"VAD配置:")
    print(f"  语音阈值: {translator.speech_threshold:.5f}")
    print(f"  静音超时: {translator.silence_timeout}秒")
    print(f"  调试模式: 开启\n")
    
    try:
        # 测试1: 正常语音后静音
        print("测试1: 2秒语音 → 静音 → 应在0.8秒后结束")
        print("-" * 50)
        
        sample_rate = 48000
        chunk_size = 4800
        
        # 生成明显高于阈值的语音
        t = np.linspace(0, 0.1, chunk_size, False)
        speech = 0.015 * np.sin(2 * np.pi * 300 * t).astype(np.float32)
        speech_volume = np.sqrt(np.mean(speech**2))
        print(f"语音音量: {speech_volume:.5f} (阈值: {translator.speech_threshold:.5f})")
        
        # 发送2秒语音
        start_time = time.time()
        for i in range(20):  # 2秒
            translator.process_audio(speech)
            time.sleep(0.1)
        
        speech_end_time = time.time()
        print(f"语音发送完成 (耗时: {speech_end_time - start_time:.1f}秒)")
        
        # 发送静音
        silence = np.zeros(chunk_size, dtype=np.float32)
        silence_volume = np.sqrt(np.mean(silence**2))
        print(f"静音音量: {silence_volume:.5f}")
        
        # 检测结束时间
        detected_end = False
        for i in range(20):  # 最多2秒
            translator.process_audio(silence)
            time.sleep(0.1)
            
            if not translator.is_speaking:
                end_time = time.time()
                silence_duration = end_time - speech_end_time
                print(f"✅ VAD检测结束! 静音{silence_duration:.2f}秒后结束")
                detected_end = True
                break
        
        if not detected_end:
            print(f"❌ VAD未在2秒内结束!")
            print(f"   仍在说话状态: {translator.is_speaking}")
            return False
        
        time.sleep(1)  # 间隔
        
        # 测试2: 背景噪声测试
        print("\n测试2: 纯背景噪声不应触发语音检测")
        print("-" * 50)
        
        # 生成低于阈值的噪声
        noise_level = translator.speech_threshold * 0.5
        noise = np.random.normal(0, noise_level, chunk_size).astype(np.float32)
        noise_volume = np.sqrt(np.mean(noise**2))
        print(f"背景噪声音量: {noise_volume:.5f} (阈值: {translator.speech_threshold:.5f})")
        
        # 发送5秒噪声
        triggered = False
        for i in range(50):
            translator.process_audio(noise)
            time.sleep(0.1)
            
            if translator.is_speaking:
                print(f"❌ 背景噪声错误触发了语音检测!")
                triggered = True
                break
        
        if not triggered:
            print(f"✅ 背景噪声未触发语音检测")
        
        return not triggered and detected_end
        
    except Exception as e:
        print(f"测试失败: {e}")
        return False
    finally:
        translator.debug_vad = False  # 关闭调试
        system.translator.shutdown()

def test_different_scenarios():
    """测试不同场景"""
    print("\n=== 测试不同VAD场景 ===\n")
    
    scenarios = [
        ("短语音(0.3秒)", 0.3, True),  # 应该被忽略
        ("正常语音(1秒)", 1.0, False),  # 应该被处理
        ("长语音(3秒)", 3.0, False),    # 应该被处理
    ]
    
    system = MicrophoneTranslatorSystem(
        enable_tts=False,
        source_lang='zh',
        target_lang='en'
    )
    
    translator = system.translator
    translator.debug_vad = False  # 关闭详细调试
    
    results = []
    
    for name, duration, should_ignore in scenarios:
        print(f"\n测试: {name}")
        print("-" * 30)
        
        # 重置状态
        translator.is_speaking = False
        queue_before = translator.work_queue.qsize()
        
        # 发送语音
        chunk_size = 4800
        num_chunks = int(duration * 10)
        t = np.linspace(0, 0.1, chunk_size, False)
        speech = 0.012 * np.sin(2 * np.pi * 300 * t).astype(np.float32)
        
        for i in range(num_chunks):
            translator.process_audio(speech)
            time.sleep(0.1)
        
        # 发送静音触发结束
        silence = np.zeros(chunk_size, dtype=np.float32)
        for i in range(12):  # 1.2秒
            translator.process_audio(silence)
            time.sleep(0.1)
            if not translator.is_speaking:
                break
        
        # 检查结果
        queue_after = translator.work_queue.qsize()
        processed = queue_after > queue_before
        
        if should_ignore:
            if not processed:
                print(f"✅ 正确: 短语音被忽略")
                results.append(True)
            else:
                print(f"❌ 错误: 短语音不应被处理")
                results.append(False)
        else:
            if processed:
                print(f"✅ 正确: 语音被处理")
                results.append(True)
            else:
                print(f"❌ 错误: 语音应该被处理")
                results.append(False)
    
    system.translator.shutdown()
    return all(results)

def main():
    """主测试"""
    print("=== VAD静音检测修复验证 ===\n")
    print("修复内容:")
    print("🔧 阈值调整: 0.003 → 0.008 (避免背景噪声干扰)")
    print("🔧 调试信息: 实时显示音量和静音计时")
    print("🔧 明确逻辑: 区分语音和背景噪声\n")
    
    results = []
    
    # 测试1: 静音检测
    print("=" * 60)
    results.append(("静音检测", test_silence_detection()))
    
    # 测试2: 不同场景
    print("\n" + "=" * 60)
    results.append(("场景测试", test_different_scenarios()))
    
    # 结果汇总
    print("\n" + "=" * 60)
    print("测试结果汇总")
    print("=" * 60)
    
    for name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{name:<12} {status}")
    
    if all(r for _, r in results):
        print("\n🎉 VAD静音检测修复成功!")
        print("现在系统应该:")
        print("• 正确检测语音开始和结束")
        print("• 0.8秒静音后自动结束录制")
        print("• 不被背景噪声持续触发")
    else:
        print("\n⚠️ 仍有问题需要调试")

if __name__ == "__main__":
    main()