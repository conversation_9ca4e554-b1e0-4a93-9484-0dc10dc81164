#!/usr/bin/env python3
"""
OBS AI同声传译 - 麦克风直接输入版本
"""
import asyncio
import sounddevice as sd
import numpy as np
import obsws_python as obs
from queue import Queue
import threading
import time
import os
from realtime_translator import RealtimeTranslator
from config import check_environment

class MicrophoneAudioCapture:
    """麦克风音频捕获器"""
    
    def __init__(self, device_id=None):
        self.sample_rate = 48000
        self.channels = 1  # 麦克风使用单声道
        self.blocksize = 1024
        self.audio_queue = Queue(maxsize=100)
        self.is_running = False
        self.device_id = device_id or self._find_best_microphone()
        
    def _find_best_microphone(self):
        """查找最佳麦克风设备"""
        devices = sd.query_devices()
        
        # 优先级：MacBook Pro麦克风 > AirPods > 其他
        priority_keywords = [
            ['MacBook Pro麦克风', 'Built-in Microphone'],
            ['AirPods'],
            ['麦克风', 'Microphone', 'mic']
        ]
        
        for priority_group in priority_keywords:
            for i, device in enumerate(devices):
                if device['max_input_channels'] > 0:
                    device_name = device['name']
                    for keyword in priority_group:
                        if keyword in device_name:
                            print(f"选择麦克风: {device_name} (设备ID: {i})")
                            return i
        
        # 如果没找到特定设备，使用默认输入设备
        print("使用系统默认输入设备")
        return None
        
    def start_capture(self, callback=None):
        """开始音频捕获"""
        print(f"开始从麦克风捕获音频...")
        if self.device_id is not None:
            device_info = sd.query_devices(self.device_id)
            print(f"设备: {device_info['name']}")
        else:
            print("设备: 系统默认")
        print(f"采样率: {self.sample_rate} Hz")
        print(f"通道数: {self.channels}")
        print(f"块大小: {self.blocksize}")
        
        def audio_callback(indata, frames, time, status):
            if status:
                print(f"音频状态: {status}")
            
            # 计算音频电平
            volume = np.sqrt(np.mean(indata**2))
            
            # 将音频数据加入队列
            if not self.audio_queue.full():
                self.audio_queue.put({
                    'data': indata.copy(),
                    'volume': volume,
                    'timestamp': time.inputBufferAdcTime
                })
            
            # 调用外部回调
            if callback:
                callback(indata, volume)
        
        self.is_running = True
        
        with sd.InputStream(
            device=self.device_id,
            channels=self.channels,
            samplerate=self.sample_rate,
            blocksize=self.blocksize,
            dtype='float32',
            latency='low',
            callback=audio_callback
        ):
            print("\n按Ctrl+C停止...")
            
            try:
                while self.is_running:
                    time.sleep(0.1)
            except KeyboardInterrupt:
                print("\n停止音频捕获")
                self.is_running = False

class OBSController:
    """OBS控制器"""
    
    def __init__(self, host='localhost', port=4455, password=''):
        self.client = None
        self.host = host
        self.port = port
        self.password = password
        
    def connect(self):
        """连接到OBS"""
        try:
            self.client = obs.ReqClient(
                host=self.host,
                port=self.port,
                password=self.password
            )
            version = self.client.get_version()
            print(f"已连接到OBS {version.obs_version}")
            return True
        except Exception as e:
            print(f"OBS连接失败: {e}")
            return False
    
    def update_text_source(self, source_name, text):
        """更新文字源（字幕）"""
        try:
            self.client.set_input_settings(
                inputName=source_name,
                inputSettings={'text': text}
            )
        except Exception as e:
            print(f"更新文字失败: {e}")

class MicrophoneTranslatorSystem:
    """麦克风AI同声传译系统"""
    
    def __init__(self, obs_password=None, source_lang='zh', target_lang='en', mic_device_id=None, enable_tts=True):
        self.audio_capture = MicrophoneAudioCapture(device_id=mic_device_id)
        
        if obs_password is None:
            obs_password = ''
            
        self.obs_controller = OBSController(password=obs_password)
        self.translator = RealtimeTranslator(
            source_lang=source_lang, 
            target_lang=target_lang,
            sample_rate=48000,  # 匹配麦克风采样率
            audio_router=None,  # 麦克风版本不需要音频路由器
            enable_tts=enable_tts,  # TTS功能开关
            audio_capture=self.audio_capture  # 传递音频捕获器引用以避免设备冲突
        )
        
    def start(self, test_mode=False):
        """启动系统"""
        print("=== AI同声传译系统启动（麦克风输入） ===\n")
        
        # 连接OBS
        if not test_mode:
            if not self.obs_controller.connect():
                print("❌ 无法连接OBS")
                print("💡 提示:")
                print("1. 确保OBS Studio已启动")
                print("2. 启用WebSocket服务器: 工具 → WebSocket服务器设置")
                print("3. 检查密码设置")
                print("4. 或使用测试模式: python3 obs_ai_translator_mic.py --test")
                return
        else:
            print("🧪 测试模式：跳过OBS连接")
        
        # 音频处理回调
        def audio_callback(audio_data, volume):
            # 显示音量条
            bar_length = int(volume * 50)
            bar = '█' * bar_length + '░' * (50 - bar_length)
            print(f'\r麦克风音量: [{bar}] {volume:.3f}', end='', flush=True)
            
            # 实时AI处理 - 更灵敏的麦克风阈值
            if volume > 0.005:  # 降低阈值，更容易捕获语音
                if audio_data.shape[0] > 0:
                    # 麦克风已经是单声道，直接处理
                    self.translator.process_audio(audio_data.flatten())
        
        # 开始音频捕获
        print("💡 提示: 按 'r' + Enter 重置翻译系统")
        print("💡 对着麦克风说话开始翻译\n")
        
        # 键盘监听线程
        def keyboard_monitor():
            try:
                while True:
                    user_input = input().strip().lower()
                    if user_input == 'r':
                        self.translator.reset_system()
                    elif user_input == 'q':
                        print("退出系统...")
                        # Graceful shutdown - CRITICAL FIX
                        self.translator.shutdown()
                        self.audio_capture.is_running = False
                        break
            except (EOFError, KeyboardInterrupt):
                print("接收到中断信号，正在优雅关闭...")
                self.translator.shutdown()
                self.audio_capture.is_running = False
        
        threading.Thread(target=keyboard_monitor, daemon=False).start()
        
        try:
            # 开始音频捕获
            self.audio_capture.start_capture(callback=audio_callback)
        except KeyboardInterrupt:
            print("接收到中断信号，正在优雅关闭...")
            self.translator.shutdown()
        finally:
            # Ensure cleanup
            if hasattr(self, 'translator'):
                self.translator.shutdown()

def main():
    """主函数"""
    import sys
    
    try:
        # 检查环境变量
        check_environment()
        print("✓ 环境变量检查通过")
        
        # 检查是否为测试模式
        test_mode = '--test' in sys.argv or '-t' in sys.argv
        
        # 检查是否禁用TTS
        enable_tts = '--no-tts' not in sys.argv
        if not enable_tts:
            print("🔇 TTS语音合成已禁用")
        
        # 检查麦克风设备选择
        mic_device_id = None
        for arg in sys.argv:
            if arg.startswith('--mic='):
                try:
                    mic_device_id = int(arg.split('=')[1])
                    print(f"使用指定麦克风设备ID: {mic_device_id}")
                except ValueError:
                    print("无效的麦克风设备ID")
        
        # 从命令行获取OBS密码
        obs_password = ""
        if len(sys.argv) > 1 and not sys.argv[1].startswith('--'):
            obs_password = sys.argv[1]
        else:
            obs_password = os.getenv('OBS_WEBSOCKET_PASSWORD', '')
            if not obs_password and not test_mode:
                try:
                    obs_password = input("请输入OBS WebSocket密码(可留空): ") or ""
                except (EOFError, KeyboardInterrupt):
                    obs_password = ""
                    print("\n使用空密码连接OBS")
        
        # 创建并启动系统
        system = MicrophoneTranslatorSystem(
            obs_password=obs_password,
            mic_device_id=mic_device_id,
            enable_tts=enable_tts
        )
        system.start(test_mode=test_mode)
        
    except ValueError as e:
        print(f"❌ 配置错误: {e}")
        print("\n请设置以下环境变量:")
        print("export OPENAI_API_KEY='your-openai-api-key'")
        sys.exit(1)
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()