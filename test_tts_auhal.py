#!/usr/bin/env python3
"""
测试TTS语音合成时的PaMacCore Error -10863
重现实际使用场景
"""

import sys
import time
import numpy as np
import sounddevice as sd
from obs_ai_translator_mic import MicrophoneTranslatorSystem
import asyncio
import edge_tts
import io
import wave
from pydub import AudioSegment

def force_tts_synthesis(text="Hello, this is a test"):
    """强制进行TTS合成并播放"""
    print(f"=== 强制TTS合成测试: {text} ===")
    
    async def synthesize_and_play():
        print("1. 进行TTS合成...")
        voice = "en-US-JennyNeural"
        communicate = edge_tts.Communicate(text, voice)
        audio_data = b""
        
        async for chunk in communicate.stream():
            if chunk["type"] == "audio":
                audio_data += chunk["data"]
        
        if audio_data:
            print("2. 转换音频格式...")
            # Edge-TTS outputs MP3, convert to wav
            audio_segment = AudioSegment.from_mp3(io.BytesIO(audio_data))
            audio_segment = audio_segment.set_frame_rate(48000).set_channels(1)
            
            # Convert to numpy array
            samples = audio_segment.get_array_of_samples()
            audio_np = np.array(samples).astype(np.float32) / 32768.0
            
            print(f"3. 播放音频 ({len(audio_np)} 样本)...")
            
            # 测试所有输出设备
            devices = sd.query_devices()
            for i, device in enumerate(devices):
                if device['max_output_channels'] > 0:
                    print(f"\n测试设备 {i}: {device['name']}")
                    try:
                        sd.play(audio_np, 48000, device=i, blocking=False)
                        time.sleep(len(audio_np) / 48000 + 0.5)  # 等待播放完成
                        sd.stop()
                        print(f"✅ 设备 {i} TTS播放成功")
                    except Exception as e:
                        print(f"❌ 设备 {i} TTS播放失败: {e}")
                        if "-10863" in str(e):
                            print("🔍 发现PaMacCore Error -10863")
        else:
            print("❌ TTS合成失败，无音频数据")
    
    # 运行异步函数
    asyncio.run(synthesize_and_play())

def test_concurrent_microphone_and_tts():
    """测试麦克风采集和TTS播放同时进行"""
    print("\n=== 测试麦克风+TTS并发 ===")
    
    from obs_ai_translator_mic import MicrophoneAudioCapture
    import threading
    
    print("1. 启动麦克风采集...")
    mic_capture = MicrophoneAudioCapture()
    
    # 音频捕获回调
    def audio_callback(indata, volume):
        if volume > 0.005:
            print(f"麦克风: {volume:.4f}")
    
    # 启动麦克风线程
    capture_thread = threading.Thread(
        target=mic_capture.start_capture,
        args=(audio_callback,)
    )
    capture_thread.daemon = True
    capture_thread.start()
    
    time.sleep(2)  # 让麦克风稳定
    
    print("\n2. 在麦克风激活时进行TTS合成...")
    
    async def concurrent_tts():
        texts = [
            "This is the first test sentence.",
            "这是第二个测试句子。",
            "Testing concurrent audio processing."
        ]
        
        for i, text in enumerate(texts, 1):
            print(f"\n--- TTS测试 {i}: {text} ---")
            
            voice = "en-US-JennyNeural" if any(c < '\u4e00' or c > '\u9fff' for c in text) else "zh-CN-XiaoxiaoNeural"
            communicate = edge_tts.Communicate(text, voice)
            audio_data = b""
            
            async for chunk in communicate.stream():
                if chunk["type"] == "audio":
                    audio_data += chunk["data"]
            
            if audio_data:
                # 转换音频
                audio_segment = AudioSegment.from_mp3(io.BytesIO(audio_data))
                audio_segment = audio_segment.set_frame_rate(48000).set_channels(1)
                samples = audio_segment.get_array_of_samples()
                audio_np = np.array(samples).astype(np.float32) / 32768.0
                
                print(f"播放TTS音频 ({len(audio_np)} 样本)")
                
                # 尝试播放到不同设备
                devices = sd.query_devices()
                for device_id, device in enumerate(devices):
                    if device['max_output_channels'] > 0:
                        # 跳过麦克风设备
                        if device_id == mic_capture.device_id:
                            continue
                            
                        print(f"  → 设备 {device_id}: {device['name']}")
                        try:
                            sd.play(audio_np, 48000, device=device_id, blocking=False)
                            time.sleep(len(audio_np) / 48000 + 0.2)
                            sd.stop()
                            print(f"    ✅ 成功")
                            break  # 第一个成功的就够了
                        except Exception as e:
                            print(f"    ❌ 失败: {e}")
                            if "-10863" in str(e):
                                print("    🔍 PaMacCore Error -10863")
            
            time.sleep(1)  # 间隔
    
    try:
        asyncio.run(concurrent_tts())
    finally:
        print("\n3. 停止麦克风...")
        mic_capture.is_running = False
        time.sleep(1)

def test_full_translator_workflow():
    """测试完整翻译工作流"""
    print("\n=== 测试完整翻译工作流 ===")
    
    print("1. 创建翻译系统...")
    system = MicrophoneTranslatorSystem(
        enable_tts=True,
        source_lang='zh',
        target_lang='en'
    )
    
    try:
        print("\n2. 模拟真实翻译场景...")
        
        # 模拟中文语音输入
        test_scenarios = [
            {"audio_volume": 0.02, "expected_text": "你好"},
            {"audio_volume": 0.03, "expected_text": "今天天气很好"},
            {"audio_volume": 0.025, "expected_text": "我想测试翻译功能"}
        ]
        
        for i, scenario in enumerate(test_scenarios, 1):
            print(f"\n--- 场景 {i} ---")
            
            # 生成模拟音频（更像真实语音）
            duration = 2.0
            sample_rate = 16000
            t = np.linspace(0, duration, int(sample_rate * duration), False)
            
            # 模拟语音特征：多个频率成分 + 噪声
            audio = (
                0.3 * scenario["audio_volume"] * np.sin(2 * np.pi * 200 * t) +
                0.2 * scenario["audio_volume"] * np.sin(2 * np.pi * 400 * t) +
                0.1 * scenario["audio_volume"] * np.sin(2 * np.pi * 800 * t) +
                0.1 * scenario["audio_volume"] * np.random.normal(0, 0.001, len(t))
            ).astype(np.float32)
            
            print(f"处理模拟语音 (音量: {scenario['audio_volume']:.3f})")
            
            try:
                system.translator.process_audio(audio)
                print("✅ 音频处理完成")
                
                # 等待可能的TTS播放
                time.sleep(3)
                
            except Exception as e:
                print(f"❌ 音频处理失败: {e}")
                if "-10863" in str(e):
                    print("🔍 在翻译过程中发现PaMacCore Error -10863")
    
    finally:
        print("\n3. 清理系统...")
        system.translator.shutdown()

def main():
    """主测试函数"""
    print("=== TTS PaMacCore AUHAL Error -10863 测试 ===\n")
    
    try:
        # 1. 强制TTS测试
        force_tts_synthesis("Hello world, testing TTS synthesis")
        
        # 2. 并发测试
        test_concurrent_microphone_and_tts()
        
        # 3. 完整工作流测试
        test_full_translator_workflow()
        
        print("\n=== 所有测试完成 ===")
        
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        if "-10863" in str(e):
            print("🔍 捕获到PaMacCore Error -10863")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n测试被中断")
        sys.exit(1)
    except Exception as e:
        print(f"测试失败: {e}")
        sys.exit(1)