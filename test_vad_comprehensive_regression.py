#!/usr/bin/env python3
"""
Comprehensive VAD Regression Test Suite
=====================================

This test suite provides comprehensive regression testing for the VAD (Voice Activity Detection) 
system after recent code changes. Covers:

1. Basic Functionality Tests
2. Edge Cases and Error Scenarios  
3. Integration Tests
4. Performance and Stability Tests

Based on the code review findings, special attention is paid to:
- Syntax error fix in conditional logic
- Silence timer accuracy and reliability
- Thread safety issues
- Exception handling robustness

Author: QA Testing Framework
Date: 2025-08-27
"""

import sys
import time
import numpy as np
import threading
import psutil
import os
import tempfile
import json
import unittest
from unittest.mock import Mock, patch, MagicMock
from collections import deque
from concurrent.futures import ThreadPoolExecutor
from datetime import datetime, timedelta
import gc
import tracemalloc

# Import the modules under test
from realtime_translator import RealtimeTranslatorFixed
from vad_fix import FixedVAD
from obs_ai_translator_mic import MicrophoneTranslatorSystem


class AudioGenerator:
    """Utility class for generating test audio samples"""
    
    @staticmethod
    def generate_silence(duration=1.0, sample_rate=48000):
        """Generate silence audio"""
        samples = int(sample_rate * duration)
        return np.zeros(samples, dtype=np.float32)
    
    @staticmethod  
    def generate_speech(duration=2.0, sample_rate=48000, volume=0.02, frequency_base=150):
        """Generate realistic speech-like audio with harmonics"""
        t = np.linspace(0, duration, int(sample_rate * duration), False)
        
        # Generate speech with fundamental frequency and harmonics
        audio = (
            0.4 * volume * np.sin(2 * np.pi * frequency_base * t) +
            0.3 * volume * np.sin(2 * np.pi * frequency_base * 2 * t) +
            0.2 * volume * np.sin(2 * np.pi * frequency_base * 3 * t) +
            0.1 * volume * np.sin(2 * np.pi * frequency_base * 4 * t)
        )
        
        # Add speech envelope (attack-decay-sustain-release)
        envelope = np.ones_like(t)
        attack_samples = int(0.1 * len(t))  # 10% attack
        release_samples = int(0.1 * len(t))  # 10% release
        
        # Attack
        envelope[:attack_samples] = np.linspace(0, 1, attack_samples)
        # Release  
        envelope[-release_samples:] = np.linspace(1, 0, release_samples)
        
        audio = audio * envelope
        
        # Add realistic noise
        noise = np.random.normal(0, 0.0005, len(audio))
        audio = audio + noise
        
        return audio.astype(np.float32)
    
    @staticmethod
    def generate_edge_noise(duration=1.0, sample_rate=48000, volume=0.008):
        """Generate edge-case noise (near threshold)"""
        t = np.linspace(0, duration, int(sample_rate * duration), False)
        # Pink noise with specific volume to test threshold sensitivity
        noise = np.random.normal(0, volume/3, len(t))
        # Add some low-frequency components
        noise += volume * 0.5 * np.sin(2 * np.pi * 50 * t)
        return noise.astype(np.float32)
    
    @staticmethod
    def generate_rapid_transitions(sample_rate=48000):
        """Generate rapid speech/silence transitions"""
        chunk_size = int(sample_rate * 0.1)  # 0.1 second chunks
        audio_parts = []
        
        # Pattern: speech, silence, speech, silence, speech (rapid transitions)
        patterns = [
            ('speech', 0.2, 0.015),  # 0.2s speech
            ('silence', 0.1, 0),     # 0.1s silence  
            ('speech', 0.3, 0.018),  # 0.3s speech
            ('silence', 0.15, 0),    # 0.15s silence
            ('speech', 0.25, 0.016), # 0.25s speech
        ]
        
        for pattern_type, duration, volume in patterns:
            if pattern_type == 'speech':
                audio_parts.append(AudioGenerator.generate_speech(duration, sample_rate, volume))
            else:
                audio_parts.append(AudioGenerator.generate_silence(duration, sample_rate))
        
        return np.concatenate(audio_parts)


class TestResults:
    """Centralized test results tracking"""
    
    def __init__(self):
        self.results = {
            'basic_functionality': {},
            'edge_cases': {},
            'integration': {},
            'performance': {},
            'stability': {}
        }
        self.start_time = datetime.now()
        self.performance_metrics = {
            'memory_usage': [],
            'response_times': [],
            'thread_counts': [],
            'error_counts': 0
        }
    
    def record_test(self, category, test_name, passed, details=None, metrics=None):
        """Record a test result"""
        self.results[category][test_name] = {
            'passed': passed,
            'details': details or {},
            'metrics': metrics or {},
            'timestamp': datetime.now().isoformat()
        }
    
    def record_performance_metric(self, metric_type, value):
        """Record performance metrics"""
        if metric_type in self.performance_metrics:
            if isinstance(self.performance_metrics[metric_type], list):
                self.performance_metrics[metric_type].append(value)
            else:
                self.performance_metrics[metric_type] = value
    
    def get_summary(self):
        """Get test summary"""
        total_tests = 0
        passed_tests = 0
        
        for category in self.results:
            for test_name, result in self.results[category].items():
                total_tests += 1
                if result['passed']:
                    passed_tests += 1
        
        return {
            'total_tests': total_tests,
            'passed_tests': passed_tests,
            'failed_tests': total_tests - passed_tests,
            'pass_rate': (passed_tests / total_tests * 100) if total_tests > 0 else 0,
            'duration': (datetime.now() - self.start_time).total_seconds(),
            'performance_metrics': self.performance_metrics
        }


class BasicFunctionalityTests:
    """Test suite for basic VAD functionality"""
    
    def __init__(self, results: TestResults):
        self.results = results
    
    def test_vad_initialization(self):
        """Test VAD initialization and configuration"""
        print("🧪 Testing VAD initialization...")
        
        try:
            # Test RealtimeTranslatorFixed initialization
            translator = RealtimeTranslatorFixed(
                source_lang='zh',
                target_lang='en', 
                sample_rate=48000,
                enable_tts=False
            )
            
            # Check critical attributes
            checks = {
                'has_audio_buffer': hasattr(translator, 'audio_buffer'),
                'has_speech_threshold': hasattr(translator, 'speech_threshold'),
                'has_silence_timeout': hasattr(translator, 'silence_timeout'),
                'has_worker_thread': hasattr(translator, 'worker_thread'),
                'worker_thread_alive': translator.worker_thread.is_alive() if hasattr(translator, 'worker_thread') else False,
                'speech_threshold_valid': 0.001 <= translator.speech_threshold <= 0.1,
                'silence_timeout_valid': 0.1 <= translator.silence_timeout <= 10.0,
            }
            
            all_passed = all(checks.values())
            
            self.results.record_test(
                'basic_functionality', 
                'vad_initialization',
                all_passed,
                details=checks
            )
            
            translator.shutdown()
            
            if all_passed:
                print("✅ VAD initialization test passed")
            else:
                print("❌ VAD initialization test failed")
                print(f"   Failed checks: {[k for k, v in checks.items() if not v]}")
            
            return all_passed
            
        except Exception as e:
            self.results.record_test(
                'basic_functionality',
                'vad_initialization', 
                False,
                details={'error': str(e), 'traceback': str(type(e).__name__)}
            )
            print(f"❌ VAD initialization test failed: {e}")
            return False
    
    def test_speech_detection_start(self):
        """Test speech detection start mechanism"""
        print("🧪 Testing speech detection start...")
        
        try:
            translator = RealtimeTranslatorFixed(
                source_lang='zh',
                target_lang='en',
                sample_rate=48000, 
                enable_tts=False
            )
            
            # Generate speech audio above threshold
            speech_audio = AudioGenerator.generate_speech(1.0, 48000, 0.025)
            
            # Process audio in chunks
            chunk_size = 4800  # 0.1s
            speech_detected = False
            
            for i in range(0, len(speech_audio), chunk_size):
                chunk = speech_audio[i:i+chunk_size]
                translator.process_audio(chunk)
                
                # Check if speech was detected
                if translator.is_speaking:
                    speech_detected = True
                    break
                    
                time.sleep(0.01)
            
            # Verify speech detection metrics
            metrics = {
                'speech_detected': speech_detected,
                'is_speaking_state': translator.is_speaking,
                'audio_buffer_size': len(translator.audio_buffer),
                'speech_segments_size': len(translator.speech_segments) if hasattr(translator, 'speech_segments') else 0
            }
            
            passed = speech_detected and translator.is_speaking
            
            self.results.record_test(
                'basic_functionality',
                'speech_detection_start',
                passed,
                details=metrics
            )
            
            translator.shutdown()
            
            if passed:
                print("✅ Speech detection start test passed")
            else:
                print("❌ Speech detection start test failed")
                print(f"   Metrics: {metrics}")
            
            return passed
            
        except Exception as e:
            self.results.record_test(
                'basic_functionality',
                'speech_detection_start',
                False, 
                details={'error': str(e)}
            )
            print(f"❌ Speech detection start test failed: {e}")
            return False
    
    def test_silence_timer_functionality(self):
        """Test silence timer accuracy and reliability"""
        print("🧪 Testing silence timer functionality...")
        
        try:
            translator = RealtimeTranslatorFixed(
                source_lang='zh',
                target_lang='en',
                sample_rate=48000,
                enable_tts=False
            )
            
            # First, trigger speech detection
            speech_audio = AudioGenerator.generate_speech(0.8, 48000, 0.025)
            chunk_size = 4800
            
            for i in range(0, len(speech_audio), chunk_size):
                chunk = speech_audio[i:i+chunk_size]
                translator.process_audio(chunk)
                time.sleep(0.01)
            
            # Ensure speech is detected
            if not translator.is_speaking:
                print("⚠️ Speech was not detected, cannot test silence timer")
                translator.shutdown()
                return False
            
            # Now send silence to test timer
            silence = AudioGenerator.generate_silence(0.1, 48000)
            silence_start = time.time()
            timeout_threshold = translator.silence_timeout
            
            speech_ended = False
            actual_timeout = None
            
            # Send silence until speech ends or timeout
            for i in range(int(timeout_threshold * 20)):  # Check every 0.05s for 2x timeout duration
                translator.process_audio(silence)
                
                if not translator.is_speaking and not speech_ended:
                    speech_ended = True
                    actual_timeout = time.time() - silence_start
                    break
                    
                time.sleep(0.05)
            
            # Calculate timing accuracy
            timing_accuracy = None
            if speech_ended and actual_timeout:
                timing_error = abs(actual_timeout - timeout_threshold)
                timing_accuracy = max(0, 1 - (timing_error / timeout_threshold))
            
            metrics = {
                'speech_ended': speech_ended,
                'expected_timeout': timeout_threshold,
                'actual_timeout': actual_timeout,
                'timing_accuracy': timing_accuracy,
                'timing_within_tolerance': timing_accuracy and timing_accuracy >= 0.8
            }
            
            passed = (speech_ended and 
                     timing_accuracy is not None and 
                     timing_accuracy >= 0.7)  # Allow 30% tolerance
            
            self.results.record_test(
                'basic_functionality',
                'silence_timer_functionality',
                passed,
                details=metrics
            )
            
            translator.shutdown()
            
            if passed:
                print("✅ Silence timer test passed")
                print(f"   Timeout accuracy: {timing_accuracy:.2%}")
            else:
                print("❌ Silence timer test failed")
                print(f"   Metrics: {metrics}")
            
            return passed
            
        except Exception as e:
            self.results.record_test(
                'basic_functionality',
                'silence_timer_functionality',
                False,
                details={'error': str(e)}
            )
            print(f"❌ Silence timer test failed: {e}")
            return False
    
    def test_audio_processing_pipeline(self):
        """Test audio processing pipeline integrity"""
        print("🧪 Testing audio processing pipeline...")
        
        try:
            # Mock the dependencies to isolate pipeline testing
            with patch('realtime_translator.whisper') as mock_whisper, \
                 patch('realtime_translator.OpenAI') as mock_openai:
                
                # Setup mocks
                mock_whisper.load_model.return_value.transcribe.return_value = {'text': 'test speech'}
                mock_client = Mock()
                mock_response = Mock()
                mock_response.choices[0].message.content = 'test translation'
                mock_client.chat.completions.create.return_value = mock_response
                mock_openai.return_value = mock_client
                
                translator = RealtimeTranslatorFixed(
                    source_lang='zh',
                    target_lang='en',
                    sample_rate=48000,
                    enable_tts=False
                )
                
                # Test pipeline with speech
                speech_audio = AudioGenerator.generate_speech(1.5, 48000, 0.025)
                chunk_size = 4800
                
                # Process speech
                for i in range(0, len(speech_audio), chunk_size):
                    chunk = speech_audio[i:i+chunk_size]
                    translator.process_audio(chunk)
                    time.sleep(0.01)
                
                # Add silence to trigger processing
                silence = AudioGenerator.generate_silence(0.1, 48000)
                for i in range(15):  # Send silence to trigger end
                    translator.process_audio(silence)
                    time.sleep(0.1)
                    if not translator.is_speaking:
                        break
                
                # Wait for processing
                time.sleep(2.0)
                
                # Check pipeline execution
                pipeline_metrics = {
                    'queue_processed': translator.work_queue.qsize() == 0,
                    'whisper_called': mock_whisper.load_model.called,
                    'openai_called': mock_openai.called,
                    'worker_thread_alive': translator.worker_thread.is_alive()
                }
                
                passed = all(pipeline_metrics.values())
                
                self.results.record_test(
                    'basic_functionality',
                    'audio_processing_pipeline',
                    passed,
                    details=pipeline_metrics
                )
                
                translator.shutdown()
                
                if passed:
                    print("✅ Audio processing pipeline test passed")
                else:
                    print("❌ Audio processing pipeline test failed")
                    print(f"   Metrics: {pipeline_metrics}")
                
                return passed
                
        except Exception as e:
            self.results.record_test(
                'basic_functionality',
                'audio_processing_pipeline',
                False,
                details={'error': str(e)}
            )
            print(f"❌ Audio processing pipeline test failed: {e}")
            return False
    
    def run_all_tests(self):
        """Run all basic functionality tests"""
        print("\n" + "="*60)
        print("BASIC FUNCTIONALITY TESTS")
        print("="*60)
        
        tests = [
            self.test_vad_initialization,
            self.test_speech_detection_start, 
            self.test_silence_timer_functionality,
            self.test_audio_processing_pipeline
        ]
        
        results = []
        for test in tests:
            results.append(test())
            print()  # Add spacing between tests
        
        passed = sum(results)
        total = len(results)
        print(f"Basic Functionality Tests: {passed}/{total} passed")
        
        return passed, total


class EdgeCasesAndErrorTests:
    """Test suite for edge cases and error scenarios"""
    
    def __init__(self, results: TestResults):
        self.results = results
    
    def test_edge_noise_handling(self):
        """Test handling of edge noise near threshold"""
        print("🧪 Testing edge noise handling...")
        
        try:
            translator = RealtimeTranslatorFixed(
                source_lang='zh',
                target_lang='en',
                sample_rate=48000,
                enable_tts=False
            )
            
            # Test with noise slightly below threshold
            below_threshold_noise = AudioGenerator.generate_edge_noise(2.0, 48000, translator.speech_threshold * 0.8)
            
            # Test with noise slightly above threshold  
            above_threshold_noise = AudioGenerator.generate_edge_noise(2.0, 48000, translator.speech_threshold * 1.2)
            
            chunk_size = 4800
            
            # Test below threshold - should not trigger speech detection
            for i in range(0, len(below_threshold_noise), chunk_size):
                chunk = below_threshold_noise[i:i+chunk_size]
                translator.process_audio(chunk)
                time.sleep(0.01)
            
            below_threshold_triggered = translator.is_speaking
            
            # Reset state
            translator.is_speaking = False
            translator.speech_segments = []
            
            # Test above threshold - should trigger speech detection
            for i in range(0, len(above_threshold_noise), chunk_size):
                chunk = above_threshold_noise[i:i+chunk_size]
                translator.process_audio(chunk)
                time.sleep(0.01)
            
            above_threshold_triggered = translator.is_speaking
            
            # Test metrics
            metrics = {
                'below_threshold_no_trigger': not below_threshold_triggered,
                'above_threshold_trigger': above_threshold_triggered,
                'threshold_value': translator.speech_threshold,
                'noise_stability': True  # Assume stable unless we detect issues
            }
            
            passed = (not below_threshold_triggered and above_threshold_triggered)
            
            self.results.record_test(
                'edge_cases',
                'edge_noise_handling',
                passed,
                details=metrics
            )
            
            translator.shutdown()
            
            if passed:
                print("✅ Edge noise handling test passed")
            else:
                print("❌ Edge noise handling test failed")
                print(f"   Below threshold triggered: {below_threshold_triggered}")
                print(f"   Above threshold triggered: {above_threshold_triggered}")
            
            return passed
            
        except Exception as e:
            self.results.record_test(
                'edge_cases',
                'edge_noise_handling',
                False,
                details={'error': str(e)}
            )
            print(f"❌ Edge noise handling test failed: {e}")
            return False
    
    def test_long_speech_segments(self):
        """Test handling of long speech segments"""
        print("🧪 Testing long speech segment handling...")
        
        try:
            translator = RealtimeTranslatorFixed(
                source_lang='zh',
                target_lang='en',
                sample_rate=48000,
                enable_tts=False
            )
            
            # Generate long speech (exceeds max_speech_duration)
            long_duration = translator.max_speech_duration + 2.0  # 2 seconds beyond max
            long_speech = AudioGenerator.generate_speech(long_duration, 48000, 0.025)
            
            chunk_size = 4800
            start_time = time.time()
            speech_ended = False
            actual_duration = None
            
            # Process long speech
            for i in range(0, len(long_speech), chunk_size):
                chunk = long_speech[i:i+chunk_size]
                translator.process_audio(chunk)
                
                # Check if speech was auto-ended due to length
                if not translator.is_speaking and not speech_ended:
                    speech_ended = True
                    actual_duration = time.time() - start_time
                    break
                    
                time.sleep(0.01)
            
            # If not auto-ended, add silence to end manually
            if not speech_ended:
                silence = AudioGenerator.generate_silence(0.1, 48000)
                for i in range(15):
                    translator.process_audio(silence)
                    time.sleep(0.1)
                    if not translator.is_speaking:
                        speech_ended = True
                        actual_duration = time.time() - start_time
                        break
            
            metrics = {
                'speech_ended': speech_ended,
                'max_duration_enforced': actual_duration and actual_duration <= (translator.max_speech_duration + 2.0),
                'expected_max_duration': translator.max_speech_duration,
                'actual_duration': actual_duration,
                'queue_has_work': translator.work_queue.qsize() > 0
            }
            
            passed = (speech_ended and 
                     metrics['max_duration_enforced'])
            
            self.results.record_test(
                'edge_cases',
                'long_speech_segments',
                passed,
                details=metrics
            )
            
            translator.shutdown()
            
            if passed:
                print("✅ Long speech segment test passed")
                print(f"   Duration: {actual_duration:.1f}s (max: {translator.max_speech_duration}s)")
            else:
                print("❌ Long speech segment test failed")
                print(f"   Metrics: {metrics}")
            
            return passed
            
        except Exception as e:
            self.results.record_test(
                'edge_cases',
                'long_speech_segments',
                False,
                details={'error': str(e)}
            )
            print(f"❌ Long speech segment test failed: {e}")
            return False
    
    def test_rapid_speech_silence_transitions(self):
        """Test rapid speech/silence transitions"""
        print("🧪 Testing rapid speech/silence transitions...")
        
        try:
            translator = RealtimeTranslatorFixed(
                source_lang='zh',
                target_lang='en',
                sample_rate=48000,
                enable_tts=False
            )
            
            # Generate rapid transitions
            rapid_audio = AudioGenerator.generate_rapid_transitions(48000)
            
            chunk_size = 4800
            transitions_detected = 0
            last_speaking_state = False
            
            # Process rapid transitions
            for i in range(0, len(rapid_audio), chunk_size):
                chunk = rapid_audio[i:i+chunk_size]
                translator.process_audio(chunk)
                
                # Count state transitions
                if translator.is_speaking != last_speaking_state:
                    transitions_detected += 1
                    last_speaking_state = translator.is_speaking
                
                time.sleep(0.01)
            
            # Add silence to finish any pending speech
            silence = AudioGenerator.generate_silence(0.5, 48000)
            for i in range(0, len(silence), chunk_size):
                translator.process_audio(silence[i:i+chunk_size])
                time.sleep(0.05)
                if not translator.is_speaking:
                    break
            
            # Wait for processing
            time.sleep(1.0)
            
            metrics = {
                'transitions_detected': transitions_detected,
                'final_speaking_state': translator.is_speaking,
                'queue_items': translator.work_queue.qsize(),
                'stability_maintained': transitions_detected >= 2  # Should detect at least some transitions
            }
            
            passed = (transitions_detected >= 2 and 
                     not translator.is_speaking and  # Should end in non-speaking state
                     translator.work_queue.qsize() >= 1)  # Should have processed some speech
            
            self.results.record_test(
                'edge_cases',
                'rapid_speech_silence_transitions',
                passed,
                details=metrics
            )
            
            translator.shutdown()
            
            if passed:
                print("✅ Rapid transitions test passed")
                print(f"   Transitions detected: {transitions_detected}")
            else:
                print("❌ Rapid transitions test failed")
                print(f"   Metrics: {metrics}")
            
            return passed
            
        except Exception as e:
            self.results.record_test(
                'edge_cases',
                'rapid_speech_silence_transitions',
                False,
                details={'error': str(e)}
            )
            print(f"❌ Rapid transitions test failed: {e}")
            return False
    
    def test_invalid_audio_input_handling(self):
        """Test handling of invalid audio input"""
        print("🧪 Testing invalid audio input handling...")
        
        try:
            translator = RealtimeTranslatorFixed(
                source_lang='zh',
                target_lang='en',
                sample_rate=48000,
                enable_tts=False
            )
            
            test_cases = []
            
            # Test case 1: Empty array
            try:
                translator.process_audio(np.array([], dtype=np.float32))
                test_cases.append(('empty_array', True))
            except Exception as e:
                test_cases.append(('empty_array', False, str(e)))
            
            # Test case 2: NaN values
            try:
                nan_audio = np.full(4800, np.nan, dtype=np.float32)
                translator.process_audio(nan_audio)
                test_cases.append(('nan_values', True))
            except Exception as e:
                test_cases.append(('nan_values', False, str(e)))
            
            # Test case 3: Infinite values
            try:
                inf_audio = np.full(4800, np.inf, dtype=np.float32)
                translator.process_audio(inf_audio)
                test_cases.append(('inf_values', True))
            except Exception as e:
                test_cases.append(('inf_values', False, str(e)))
            
            # Test case 4: Wrong dtype
            try:
                int_audio = np.ones(4800, dtype=np.int32) * 1000
                translator.process_audio(int_audio)
                test_cases.append(('wrong_dtype', True))
            except Exception as e:
                test_cases.append(('wrong_dtype', False, str(e)))
            
            # Test case 5: Multi-dimensional array
            try:
                multi_audio = np.random.rand(2, 4800).astype(np.float32) * 0.02
                translator.process_audio(multi_audio)
                test_cases.append(('multi_dimensional', True))
            except Exception as e:
                test_cases.append(('multi_dimensional', False, str(e)))
            
            # Analyze results
            handled_gracefully = sum(1 for case in test_cases if len(case) == 2 and case[1])
            total_cases = len(test_cases)
            
            # System should handle at least 3 out of 5 cases gracefully
            passed = handled_gracefully >= 3
            
            metrics = {
                'cases_handled_gracefully': handled_gracefully,
                'total_cases': total_cases,
                'grace_rate': handled_gracefully / total_cases,
                'test_results': {case[0]: case[1] for case in test_cases}
            }
            
            self.results.record_test(
                'edge_cases',
                'invalid_audio_input_handling',
                passed,
                details=metrics
            )
            
            translator.shutdown()
            
            if passed:
                print("✅ Invalid audio input handling test passed")
                print(f"   Handled gracefully: {handled_gracefully}/{total_cases}")
            else:
                print("❌ Invalid audio input handling test failed")
                print(f"   Only {handled_gracefully}/{total_cases} cases handled gracefully")
                for case in test_cases:
                    if len(case) > 2:  # Has error
                        print(f"   {case[0]}: {case[2]}")
            
            return passed
            
        except Exception as e:
            self.results.record_test(
                'edge_cases',
                'invalid_audio_input_handling',
                False,
                details={'error': str(e)}
            )
            print(f"❌ Invalid audio input handling test failed: {e}")
            return False
    
    def test_memory_usage_under_stress(self):
        """Test memory usage under stress conditions"""
        print("🧪 Testing memory usage under stress...")
        
        try:
            # Start memory tracking
            tracemalloc.start()
            
            translator = RealtimeTranslatorFixed(
                source_lang='zh',
                target_lang='en',
                sample_rate=48000,
                enable_tts=False
            )
            
            initial_memory = tracemalloc.get_traced_memory()[0]
            max_memory = initial_memory
            
            # Stress test: Process many audio chunks
            chunk_size = 4800
            stress_duration = 30  # 30 iterations
            
            for i in range(stress_duration):
                # Alternate between speech and silence
                if i % 2 == 0:
                    audio = AudioGenerator.generate_speech(0.5, 48000, 0.025)
                else:
                    audio = AudioGenerator.generate_silence(0.3, 48000)
                
                # Process in chunks
                for j in range(0, len(audio), chunk_size):
                    chunk = audio[j:j+chunk_size]
                    translator.process_audio(chunk)
                
                # Check memory usage
                current_memory = tracemalloc.get_traced_memory()[0]
                max_memory = max(max_memory, current_memory)
                
                # Brief pause
                time.sleep(0.05)
            
            final_memory = tracemalloc.get_traced_memory()[0]
            
            # Calculate memory metrics
            memory_growth = final_memory - initial_memory
            memory_growth_mb = memory_growth / (1024 * 1024)
            max_memory_mb = max_memory / (1024 * 1024)
            
            # Memory should not grow excessively (limit: 50MB growth)
            memory_acceptable = memory_growth_mb < 50
            
            metrics = {
                'initial_memory_mb': initial_memory / (1024 * 1024),
                'final_memory_mb': final_memory / (1024 * 1024),
                'max_memory_mb': max_memory_mb,
                'memory_growth_mb': memory_growth_mb,
                'memory_growth_acceptable': memory_acceptable,
                'stress_iterations': stress_duration
            }
            
            passed = memory_acceptable
            
            self.results.record_test(
                'edge_cases',
                'memory_usage_under_stress',
                passed,
                details=metrics
            )
            
            # Record performance metrics
            self.results.record_performance_metric('memory_usage', max_memory_mb)
            
            translator.shutdown()
            tracemalloc.stop()
            
            if passed:
                print("✅ Memory usage under stress test passed")
                print(f"   Memory growth: {memory_growth_mb:.1f}MB (max: {max_memory_mb:.1f}MB)")
            else:
                print("❌ Memory usage under stress test failed")
                print(f"   Excessive memory growth: {memory_growth_mb:.1f}MB")
            
            return passed
            
        except Exception as e:
            tracemalloc.stop()
            self.results.record_test(
                'edge_cases',
                'memory_usage_under_stress',
                False,
                details={'error': str(e)}
            )
            print(f"❌ Memory usage under stress test failed: {e}")
            return False
    
    def run_all_tests(self):
        """Run all edge cases and error scenario tests"""
        print("\n" + "="*60)
        print("EDGE CASES AND ERROR SCENARIO TESTS")
        print("="*60)
        
        tests = [
            self.test_edge_noise_handling,
            self.test_long_speech_segments,
            self.test_rapid_speech_silence_transitions,
            self.test_invalid_audio_input_handling,
            self.test_memory_usage_under_stress
        ]
        
        results = []
        for test in tests:
            results.append(test())
            print()  # Add spacing between tests
        
        passed = sum(results)
        total = len(results)
        print(f"Edge Cases and Error Tests: {passed}/{total} passed")
        
        return passed, total


class IntegrationTests:
    """Test suite for integration testing"""
    
    def __init__(self, results: TestResults):
        self.results = results
    
    def test_full_pipeline_integration(self):
        """Test full pipeline: Speech → Recognition → Translation → TTS"""
        print("🧪 Testing full pipeline integration...")
        
        try:
            # Use MicrophoneTranslatorSystem for full integration
            system = MicrophoneTranslatorSystem(
                enable_tts=True,
                source_lang='zh',
                target_lang='en'
            )
            
            # Generate test speech
            test_speech = AudioGenerator.generate_speech(2.0, 48000, 0.025)
            
            # Track pipeline execution
            pipeline_stages = {
                'speech_detected': False,
                'recognition_attempted': False,
                'translation_attempted': False,
                'tts_attempted': False
            }
            
            # Process speech
            chunk_size = 4800
            start_time = time.time()
            
            for i in range(0, len(test_speech), chunk_size):
                chunk = test_speech[i:i+chunk_size]
                system.translator.process_audio(chunk)
                
                if system.translator.is_speaking:
                    pipeline_stages['speech_detected'] = True
                
                time.sleep(0.01)
            
            # Add silence to trigger processing
            silence = AudioGenerator.generate_silence(0.1, 48000)
            for i in range(15):
                system.translator.process_audio(silence)
                time.sleep(0.1)
                if not system.translator.is_speaking:
                    break
            
            # Wait for full pipeline processing
            processing_timeout = 15  # 15 seconds timeout
            wait_start = time.time()
            
            while (time.time() - wait_start) < processing_timeout:
                queue_size = system.translator.work_queue.qsize()
                if queue_size == 0:  # Queue processed
                    break
                time.sleep(0.5)
            
            # Check if components were invoked (indirectly through queue processing)
            final_queue_size = system.translator.work_queue.qsize()
            worker_alive = system.translator.worker_thread.is_alive()
            
            metrics = {
                'pipeline_execution_time': time.time() - start_time,
                'speech_detected': pipeline_stages['speech_detected'],
                'queue_processed': final_queue_size == 0,
                'worker_thread_alive': worker_alive,
                'final_queue_size': final_queue_size
            }
            
            passed = (pipeline_stages['speech_detected'] and 
                     worker_alive and
                     final_queue_size <= 1)  # Queue should be processed or nearly processed
            
            self.results.record_test(
                'integration',
                'full_pipeline_integration',
                passed,
                details=metrics
            )
            
            # Record performance metric
            self.results.record_performance_metric('response_times', metrics['pipeline_execution_time'])
            
            system.translator.shutdown()
            
            if passed:
                print("✅ Full pipeline integration test passed")
                print(f"   Execution time: {metrics['pipeline_execution_time']:.1f}s")
            else:
                print("❌ Full pipeline integration test failed")
                print(f"   Metrics: {metrics}")
            
            return passed
            
        except Exception as e:
            self.results.record_test(
                'integration',
                'full_pipeline_integration',
                False,
                details={'error': str(e)}
            )
            print(f"❌ Full pipeline integration test failed: {e}")
            return False
    
    def test_multithreading_behavior(self):
        """Test multi-threading behavior and synchronization"""
        print("🧪 Testing multi-threading behavior...")
        
        try:
            translator = RealtimeTranslatorFixed(
                source_lang='zh',
                target_lang='en',
                sample_rate=48000,
                enable_tts=False
            )
            
            # Test concurrent audio processing from multiple threads
            def audio_processor(thread_id, results_dict):
                try:
                    # Generate unique audio for this thread
                    audio = AudioGenerator.generate_speech(1.0, 48000, 0.02 + thread_id * 0.002)
                    chunk_size = 4800
                    
                    for i in range(0, len(audio), chunk_size):
                        chunk = audio[i:i+chunk_size]
                        translator.process_audio(chunk)
                        time.sleep(0.01)
                    
                    # Add silence
                    silence = AudioGenerator.generate_silence(0.2, 48000)
                    for i in range(0, len(silence), chunk_size):
                        translator.process_audio(silence[i:i+chunk_size])
                        time.sleep(0.05)
                    
                    results_dict[f'thread_{thread_id}'] = 'completed'
                    
                except Exception as e:
                    results_dict[f'thread_{thread_id}'] = f'error: {e}'
            
            # Run multiple threads
            num_threads = 3
            thread_results = {}
            threads = []
            
            for i in range(num_threads):
                thread = threading.Thread(
                    target=audio_processor, 
                    args=(i, thread_results)
                )
                threads.append(thread)
                thread.start()
            
            # Wait for all threads
            for thread in threads:
                thread.join(timeout=10)
            
            # Wait for processing
            time.sleep(3)
            
            # Check results
            completed_threads = sum(1 for result in thread_results.values() if result == 'completed')
            error_threads = sum(1 for result in thread_results.values() if result.startswith('error'))
            
            metrics = {
                'threads_launched': num_threads,
                'threads_completed': completed_threads,
                'threads_with_errors': error_threads,
                'worker_thread_alive': translator.worker_thread.is_alive(),
                'final_queue_size': translator.work_queue.qsize(),
                'thread_results': thread_results
            }
            
            # Success if most threads completed and worker is still alive
            passed = (completed_threads >= num_threads - 1 and  # Allow 1 failure
                     translator.worker_thread.is_alive() and
                     error_threads <= 1)
            
            self.results.record_test(
                'integration',
                'multithreading_behavior',
                passed,
                details=metrics
            )
            
            translator.shutdown()
            
            if passed:
                print("✅ Multi-threading behavior test passed")
                print(f"   Completed threads: {completed_threads}/{num_threads}")
            else:
                print("❌ Multi-threading behavior test failed")
                print(f"   Completed: {completed_threads}, Errors: {error_threads}")
                if error_threads > 0:
                    for thread_id, result in thread_results.items():
                        if result.startswith('error'):
                            print(f"   {thread_id}: {result}")
            
            return passed
            
        except Exception as e:
            self.results.record_test(
                'integration',
                'multithreading_behavior',
                False,
                details={'error': str(e)}
            )
            print(f"❌ Multi-threading behavior test failed: {e}")
            return False
    
    def test_resource_cleanup_and_shutdown(self):
        """Test resource cleanup and shutdown procedures"""
        print("🧪 Testing resource cleanup and shutdown...")
        
        try:
            # Create and use translator
            translator = RealtimeTranslatorFixed(
                source_lang='zh',
                target_lang='en',
                sample_rate=48000,
                enable_tts=False
            )
            
            # Process some audio to create resources
            speech = AudioGenerator.generate_speech(1.0, 48000, 0.025)
            chunk_size = 4800
            
            for i in range(0, len(speech), chunk_size):
                chunk = speech[i:i+chunk_size]
                translator.process_audio(chunk)
                time.sleep(0.01)
            
            # Check resources before shutdown
            pre_shutdown_metrics = {
                'worker_thread_alive': translator.worker_thread.is_alive(),
                'queue_size': translator.work_queue.qsize(),
                'audio_buffer_size': len(translator.audio_buffer),
                'is_running': translator.is_running
            }
            
            # Shutdown
            shutdown_start = time.time()
            translator.shutdown()
            shutdown_duration = time.time() - shutdown_start
            
            # Check resources after shutdown
            post_shutdown_metrics = {
                'worker_thread_alive': translator.worker_thread.is_alive() if hasattr(translator.worker_thread, 'is_alive') else False,
                'is_running': translator.is_running,
                'shutdown_duration': shutdown_duration,
                'shutdown_timeout_acceptable': shutdown_duration < 5.0  # Should shutdown within 5 seconds
            }
            
            # Test creating new translator after shutdown (resource reuse)
            try:
                new_translator = RealtimeTranslatorFixed(
                    source_lang='zh',
                    target_lang='en',
                    sample_rate=48000,
                    enable_tts=False
                )
                resource_reuse_ok = True
                new_translator.shutdown()
            except Exception as e:
                resource_reuse_ok = False
                reuse_error = str(e)
            
            metrics = {
                'pre_shutdown': pre_shutdown_metrics,
                'post_shutdown': post_shutdown_metrics,
                'resource_reuse_ok': resource_reuse_ok,
                'clean_shutdown': (not post_shutdown_metrics['worker_thread_alive'] and 
                                 not post_shutdown_metrics['is_running'])
            }
            
            passed = (post_shutdown_metrics['shutdown_timeout_acceptable'] and
                     metrics['clean_shutdown'] and
                     resource_reuse_ok)
            
            self.results.record_test(
                'integration',
                'resource_cleanup_and_shutdown',
                passed,
                details=metrics
            )
            
            if passed:
                print("✅ Resource cleanup and shutdown test passed")
                print(f"   Shutdown time: {shutdown_duration:.2f}s")
            else:
                print("❌ Resource cleanup and shutdown test failed")
                print(f"   Clean shutdown: {metrics['clean_shutdown']}")
                print(f"   Resource reuse: {resource_reuse_ok}")
            
            return passed
            
        except Exception as e:
            self.results.record_test(
                'integration',
                'resource_cleanup_and_shutdown',
                False,
                details={'error': str(e)}
            )
            print(f"❌ Resource cleanup and shutdown test failed: {e}")
            return False
    
    def run_all_tests(self):
        """Run all integration tests"""
        print("\n" + "="*60)
        print("INTEGRATION TESTS")
        print("="*60)
        
        tests = [
            self.test_full_pipeline_integration,
            self.test_multithreading_behavior,
            self.test_resource_cleanup_and_shutdown
        ]
        
        results = []
        for test in tests:
            results.append(test())
            print()  # Add spacing between tests
        
        passed = sum(results)
        total = len(results)
        print(f"Integration Tests: {passed}/{total} passed")
        
        return passed, total


class PerformanceAndStabilityTests:
    """Test suite for performance and stability testing"""
    
    def __init__(self, results: TestResults):
        self.results = results
    
    def test_continuous_operation(self):
        """Test continuous operation over extended period"""
        print("🧪 Testing continuous operation...")
        
        try:
            translator = RealtimeTranslatorFixed(
                source_lang='zh',
                target_lang='en',
                sample_rate=48000,
                enable_tts=False
            )
            
            # Continuous operation test parameters
            test_duration = 60  # 60 seconds of continuous operation
            chunk_interval = 0.1  # Process audio every 100ms
            
            start_time = time.time()
            operations_completed = 0
            errors_encountered = 0
            speech_cycles = 0
            
            while (time.time() - start_time) < test_duration:
                try:
                    # Alternate between speech and silence
                    if operations_completed % 20 < 10:  # 10 chunks speech, 10 chunks silence
                        audio = AudioGenerator.generate_speech(chunk_interval, 48000, 0.02)
                        if not translator.is_speaking and operations_completed % 20 == 0:
                            speech_cycles += 1
                    else:
                        audio = AudioGenerator.generate_silence(chunk_interval, 48000)
                    
                    # Process audio
                    translator.process_audio(audio)
                    operations_completed += 1
                    
                    time.sleep(chunk_interval)
                    
                except Exception as e:
                    errors_encountered += 1
                    print(f"   Error during operation {operations_completed}: {e}")
            
            # Final metrics
            actual_duration = time.time() - start_time
            operations_per_second = operations_completed / actual_duration
            error_rate = errors_encountered / operations_completed if operations_completed > 0 else 1.0
            
            metrics = {
                'test_duration': actual_duration,
                'operations_completed': operations_completed,
                'operations_per_second': operations_per_second,
                'errors_encountered': errors_encountered,
                'error_rate': error_rate,
                'speech_cycles': speech_cycles,
                'worker_thread_alive': translator.worker_thread.is_alive(),
                'final_queue_size': translator.work_queue.qsize()
            }
            
            # Success criteria: low error rate, consistent operation, worker alive
            passed = (error_rate < 0.05 and  # Less than 5% error rate
                     operations_completed > test_duration * 5 and  # At least 5 ops/second
                     translator.worker_thread.is_alive())
            
            self.results.record_test(
                'performance',
                'continuous_operation',
                passed,
                details=metrics
            )
            
            # Record performance metrics
            self.results.record_performance_metric('response_times', actual_duration)
            
            translator.shutdown()
            
            if passed:
                print("✅ Continuous operation test passed")
                print(f"   Duration: {actual_duration:.1f}s, Operations: {operations_completed}")
                print(f"   Error rate: {error_rate:.2%}, Ops/sec: {operations_per_second:.1f}")
            else:
                print("❌ Continuous operation test failed")
                print(f"   Error rate: {error_rate:.2%}, Ops/sec: {operations_per_second:.1f}")
            
            return passed
            
        except Exception as e:
            self.results.record_test(
                'performance',
                'continuous_operation',
                False,
                details={'error': str(e)}
            )
            print(f"❌ Continuous operation test failed: {e}")
            return False
    
    def test_memory_leak_detection(self):
        """Test for memory leaks during extended operation"""
        print("🧪 Testing memory leak detection...")
        
        try:
            tracemalloc.start()
            
            translator = RealtimeTranslatorFixed(
                source_lang='zh',
                target_lang='en',
                sample_rate=48000,
                enable_tts=False
            )
            
            # Baseline memory
            baseline_memory = tracemalloc.get_traced_memory()[0]
            memory_samples = [baseline_memory]
            
            # Run multiple processing cycles
            cycles = 30
            chunk_size = 4800
            
            for cycle in range(cycles):
                # Process speech
                speech = AudioGenerator.generate_speech(0.8, 48000, 0.02)
                for i in range(0, len(speech), chunk_size):
                    chunk = speech[i:i+chunk_size]
                    translator.process_audio(chunk)
                    time.sleep(0.01)
                
                # Process silence
                silence = AudioGenerator.generate_silence(0.5, 48000)
                for i in range(0, len(silence), chunk_size):
                    translator.process_audio(silence[i:i+chunk_size])
                    time.sleep(0.01)
                
                # Sample memory every 5 cycles
                if cycle % 5 == 0:
                    current_memory = tracemalloc.get_traced_memory()[0]
                    memory_samples.append(current_memory)
                
                # Brief pause
                time.sleep(0.1)
            
            # Final memory measurement
            final_memory = tracemalloc.get_traced_memory()[0]
            memory_samples.append(final_memory)
            
            # Analyze memory trend
            memory_growth = final_memory - baseline_memory
            memory_growth_mb = memory_growth / (1024 * 1024)
            
            # Calculate linear regression to detect consistent growth (leak)
            import statistics
            if len(memory_samples) > 2:
                # Simple trend analysis
                first_half_avg = statistics.mean(memory_samples[:len(memory_samples)//2])
                second_half_avg = statistics.mean(memory_samples[len(memory_samples)//2:])
                trend_growth = second_half_avg - first_half_avg
                trend_growth_mb = trend_growth / (1024 * 1024)
            else:
                trend_growth_mb = memory_growth_mb
            
            # Memory leak thresholds
            acceptable_growth_mb = 20  # 20MB total growth acceptable
            acceptable_trend_mb = 10   # 10MB trend growth acceptable
            
            metrics = {
                'baseline_memory_mb': baseline_memory / (1024 * 1024),
                'final_memory_mb': final_memory / (1024 * 1024),
                'total_growth_mb': memory_growth_mb,
                'trend_growth_mb': trend_growth_mb,
                'processing_cycles': cycles,
                'memory_samples_count': len(memory_samples),
                'no_significant_leak': (memory_growth_mb < acceptable_growth_mb and 
                                      trend_growth_mb < acceptable_trend_mb)
            }
            
            passed = metrics['no_significant_leak']
            
            self.results.record_test(
                'performance',
                'memory_leak_detection',
                passed,
                details=metrics
            )
            
            # Record performance metrics
            self.results.record_performance_metric('memory_usage', final_memory / (1024 * 1024))
            
            translator.shutdown()
            tracemalloc.stop()
            
            if passed:
                print("✅ Memory leak detection test passed")
                print(f"   Memory growth: {memory_growth_mb:.1f}MB (trend: {trend_growth_mb:.1f}MB)")
            else:
                print("❌ Memory leak detection test failed")
                print(f"   Excessive memory growth: {memory_growth_mb:.1f}MB (trend: {trend_growth_mb:.1f}MB)")
            
            return passed
            
        except Exception as e:
            tracemalloc.stop()
            self.results.record_test(
                'performance',
                'memory_leak_detection',
                False,
                details={'error': str(e)}
            )
            print(f"❌ Memory leak detection test failed: {e}")
            return False
    
    def test_thread_safety_validation(self):
        """Test thread safety under concurrent access"""
        print("🧪 Testing thread safety validation...")
        
        try:
            translator = RealtimeTranslatorFixed(
                source_lang='zh',
                target_lang='en',
                sample_rate=48000,
                enable_tts=False
            )
            
            # Concurrent access test
            num_threads = 5
            operations_per_thread = 20
            thread_results = {}
            successful_operations = 0
            failed_operations = 0
            
            def concurrent_processor(thread_id, operations_count):
                thread_success = 0
                thread_failures = 0
                
                for op in range(operations_count):
                    try:
                        # Mix of operations
                        if op % 3 == 0:
                            # Process speech
                            audio = AudioGenerator.generate_speech(0.3, 48000, 0.015)
                        elif op % 3 == 1:
                            # Process silence
                            audio = AudioGenerator.generate_silence(0.2, 48000)
                        else:
                            # Check state (read-only)
                            is_speaking = translator.is_speaking
                            queue_size = translator.work_queue.qsize()
                            thread_success += 1
                            continue
                        
                        # Process audio
                        chunk_size = 2400  # Smaller chunks for more concurrency
                        for i in range(0, len(audio), chunk_size):
                            chunk = audio[i:i+chunk_size]
                            translator.process_audio(chunk)
                        
                        thread_success += 1
                        time.sleep(0.01)  # Brief pause
                        
                    except Exception as e:
                        thread_failures += 1
                        print(f"   Thread {thread_id} error in op {op}: {e}")
                
                thread_results[thread_id] = {
                    'success': thread_success,
                    'failures': thread_failures
                }
            
            # Launch concurrent threads
            threads = []
            start_time = time.time()
            
            for i in range(num_threads):
                thread = threading.Thread(
                    target=concurrent_processor,
                    args=(i, operations_per_thread)
                )
                threads.append(thread)
                thread.start()
            
            # Wait for completion
            for thread in threads:
                thread.join(timeout=15)
            
            execution_time = time.time() - start_time
            
            # Aggregate results
            for thread_id, results in thread_results.items():
                successful_operations += results['success']
                failed_operations += results['failures']
            
            total_operations = successful_operations + failed_operations
            success_rate = successful_operations / total_operations if total_operations > 0 else 0
            
            # Check system state after concurrent access
            final_state_healthy = (translator.worker_thread.is_alive() and 
                                 translator.is_running)
            
            metrics = {
                'threads_used': num_threads,
                'operations_per_thread': operations_per_thread,
                'total_operations': total_operations,
                'successful_operations': successful_operations,
                'failed_operations': failed_operations,
                'success_rate': success_rate,
                'execution_time': execution_time,
                'final_state_healthy': final_state_healthy,
                'thread_results': thread_results
            }
            
            # Success criteria: high success rate and healthy final state
            passed = (success_rate >= 0.95 and  # At least 95% success rate
                     final_state_healthy)
            
            self.results.record_test(
                'stability',
                'thread_safety_validation',
                passed,
                details=metrics
            )
            
            # Record performance metrics  
            self.results.record_performance_metric('thread_counts', num_threads)
            
            translator.shutdown()
            
            if passed:
                print("✅ Thread safety validation test passed")
                print(f"   Success rate: {success_rate:.1%} ({successful_operations}/{total_operations})")
            else:
                print("❌ Thread safety validation test failed")
                print(f"   Success rate: {success_rate:.1%}, Healthy state: {final_state_healthy}")
            
            return passed
            
        except Exception as e:
            self.results.record_test(
                'stability',
                'thread_safety_validation',
                False,
                details={'error': str(e)}
            )
            print(f"❌ Thread safety validation test failed: {e}")
            return False
    
    def run_all_tests(self):
        """Run all performance and stability tests"""
        print("\n" + "="*60)
        print("PERFORMANCE AND STABILITY TESTS")
        print("="*60)
        
        tests = [
            self.test_continuous_operation,
            self.test_memory_leak_detection,
            self.test_thread_safety_validation
        ]
        
        results = []
        for test in tests:
            results.append(test())
            print()  # Add spacing between tests
        
        passed = sum(results)
        total = len(results)
        print(f"Performance and Stability Tests: {passed}/{total} passed")
        
        return passed, total


class QAReportGenerator:
    """Generate comprehensive QA report"""
    
    def __init__(self, results: TestResults):
        self.results = results
    
    def generate_report(self):
        """Generate comprehensive QA report"""
        summary = self.results.get_summary()
        
        report = {
            'test_execution_summary': {
                'timestamp': datetime.now().isoformat(),
                'total_duration_seconds': summary['duration'],
                'total_tests': summary['total_tests'],
                'passed_tests': summary['passed_tests'],
                'failed_tests': summary['failed_tests'],
                'overall_pass_rate': summary['pass_rate']
            },
            'test_results_by_category': {},
            'performance_metrics': summary['performance_metrics'],
            'issues_discovered': [],
            'recommendations': []
        }
        
        # Process results by category
        for category, tests in self.results.results.items():
            if not tests:  # Skip empty categories
                continue
                
            category_passed = sum(1 for test in tests.values() if test['passed'])
            category_total = len(tests)
            
            report['test_results_by_category'][category] = {
                'total_tests': category_total,
                'passed_tests': category_passed,
                'failed_tests': category_total - category_passed,
                'pass_rate': (category_passed / category_total * 100) if category_total > 0 else 0,
                'test_details': {}
            }
            
            # Add individual test details and collect issues
            for test_name, test_result in tests.items():
                report['test_results_by_category'][category]['test_details'][test_name] = {
                    'passed': test_result['passed'],
                    'timestamp': test_result['timestamp'],
                    'details': test_result['details']
                }
                
                # Collect issues from failed tests
                if not test_result['passed']:
                    issue = {
                        'category': category,
                        'test_name': test_name,
                        'issue_type': 'test_failure',
                        'description': test_result['details'].get('error', 'Test failed'),
                        'severity': self._assess_severity(category, test_name),
                        'timestamp': test_result['timestamp']
                    }
                    report['issues_discovered'].append(issue)
        
        # Generate recommendations
        report['recommendations'] = self._generate_recommendations(report)
        
        # Production readiness assessment
        report['production_readiness'] = self._assess_production_readiness(report)
        
        return report
    
    def _assess_severity(self, category, test_name):
        """Assess issue severity"""
        critical_tests = [
            'vad_initialization',
            'silence_timer_functionality', 
            'thread_safety_validation',
            'memory_leak_detection'
        ]
        
        high_priority_categories = ['basic_functionality', 'stability']
        
        if test_name in critical_tests:
            return 'critical'
        elif category in high_priority_categories:
            return 'high'
        elif category == 'performance':
            return 'medium'
        else:
            return 'low'
    
    def _generate_recommendations(self, report):
        """Generate recommendations based on test results"""
        recommendations = []
        
        # Check overall pass rate
        overall_pass_rate = report['test_execution_summary']['overall_pass_rate']
        if overall_pass_rate < 80:
            recommendations.append({
                'category': 'general',
                'priority': 'critical',
                'title': 'Low Overall Test Pass Rate',
                'description': f'Overall pass rate is {overall_pass_rate:.1f}%, which is below acceptable threshold of 80%',
                'action': 'Address failing tests before production deployment'
            })
        
        # Check specific category failures
        categories = report['test_results_by_category']
        
        if 'basic_functionality' in categories and categories['basic_functionality']['pass_rate'] < 100:
            recommendations.append({
                'category': 'basic_functionality',
                'priority': 'critical',
                'title': 'Basic Functionality Issues',
                'description': 'Core VAD functionality tests are failing',
                'action': 'Fix basic functionality issues immediately - system is not ready for production'
            })
        
        if 'stability' in categories and categories['stability']['pass_rate'] < 90:
            recommendations.append({
                'category': 'stability',
                'priority': 'high',
                'title': 'Stability Concerns',
                'description': 'Thread safety or stability tests are failing',
                'action': 'Investigate and fix stability issues before production deployment'
            })
        
        # Check performance metrics
        perf_metrics = report['performance_metrics']
        
        if perf_metrics['memory_usage'] and max(perf_metrics['memory_usage']) > 100:  # 100MB
            recommendations.append({
                'category': 'performance',
                'priority': 'medium',
                'title': 'High Memory Usage',
                'description': f'Peak memory usage is {max(perf_metrics["memory_usage"]):.1f}MB',
                'action': 'Monitor memory usage in production and consider optimization'
            })
        
        if perf_metrics['response_times'] and max(perf_metrics['response_times']) > 30:  # 30 seconds
            recommendations.append({
                'category': 'performance',
                'priority': 'medium',
                'title': 'Long Response Times',
                'description': f'Peak response time is {max(perf_metrics["response_times"]):.1f}s',
                'action': 'Optimize response times for better user experience'
            })
        
        # Check for no recommendations case
        if not recommendations:
            recommendations.append({
                'category': 'general',
                'priority': 'info',
                'title': 'All Tests Passed',
                'description': 'All tests passed successfully',
                'action': 'System appears ready for production deployment with continued monitoring'
            })
        
        return recommendations
    
    def _assess_production_readiness(self, report):
        """Assess production readiness"""
        overall_pass_rate = report['test_execution_summary']['overall_pass_rate']
        critical_issues = [rec for rec in report['recommendations'] if rec['priority'] == 'critical']
        high_issues = [rec for rec in report['recommendations'] if rec['priority'] == 'high']
        
        # Determine readiness level
        if overall_pass_rate >= 95 and len(critical_issues) == 0:
            readiness_level = 'ready'
            status_message = 'System is ready for production deployment'
        elif overall_pass_rate >= 80 and len(critical_issues) == 0:
            readiness_level = 'ready_with_monitoring'
            status_message = 'System is ready for production with enhanced monitoring'
        elif overall_pass_rate >= 70 and len(critical_issues) <= 1:
            readiness_level = 'conditional'
            status_message = 'System may be ready for limited production deployment after addressing critical issues'
        else:
            readiness_level = 'not_ready'
            status_message = 'System is NOT ready for production deployment'
        
        return {
            'readiness_level': readiness_level,
            'status_message': status_message,
            'critical_issues_count': len(critical_issues),
            'high_priority_issues_count': len(high_issues),
            'blocking_issues': critical_issues + high_issues
        }
    
    def print_report(self, report):
        """Print formatted QA report to console"""
        print("\n" + "="*80)
        print("VAD SYSTEM COMPREHENSIVE QA REGRESSION TEST REPORT")
        print("="*80)
        print(f"Test Execution Date: {report['test_execution_summary']['timestamp']}")
        print(f"Total Duration: {report['test_execution_summary']['total_duration_seconds']:.1f} seconds")
        print()
        
        # Test Summary
        summary = report['test_execution_summary']
        print("📊 TEST EXECUTION SUMMARY")
        print("-" * 40)
        print(f"Total Tests:    {summary['total_tests']}")
        print(f"Passed:         {summary['passed_tests']} ✅")
        print(f"Failed:         {summary['failed_tests']} ❌")
        print(f"Pass Rate:      {summary['overall_pass_rate']:.1f}%")
        print()
        
        # Results by Category
        print("📋 RESULTS BY CATEGORY")
        print("-" * 40)
        for category, results in report['test_results_by_category'].items():
            status = "✅" if results['pass_rate'] == 100 else "⚠️" if results['pass_rate'] >= 80 else "❌"
            print(f"{category.replace('_', ' ').title():<25} {results['passed_tests']}/{results['total_tests']} ({results['pass_rate']:.0f}%) {status}")
        print()
        
        # Performance Metrics
        print("⚡ PERFORMANCE METRICS")
        print("-" * 40)
        perf = report['performance_metrics']
        if perf['memory_usage']:
            print(f"Peak Memory Usage:     {max(perf['memory_usage']):.1f} MB")
        if perf['response_times']:
            print(f"Max Response Time:     {max(perf['response_times']):.1f} seconds")
        if perf['thread_counts']:
            print(f"Max Concurrent Threads: {max(perf['thread_counts'])}")
        print(f"Total Error Count:     {perf['error_counts']}")
        print()
        
        # Issues Discovered
        if report['issues_discovered']:
            print("🚨 ISSUES DISCOVERED")
            print("-" * 40)
            for issue in report['issues_discovered']:
                severity_icon = {"critical": "🔴", "high": "🟠", "medium": "🟡", "low": "🟢"}.get(issue['severity'], "⚪")
                print(f"{severity_icon} {issue['severity'].upper()}: {issue['test_name']}")
                print(f"   Category: {issue['category']}")
                print(f"   Description: {issue['description']}")
                print()
        
        # Recommendations
        print("💡 RECOMMENDATIONS")
        print("-" * 40)
        for rec in report['recommendations']:
            priority_icon = {"critical": "🔴", "high": "🟠", "medium": "🟡", "low": "🟢", "info": "ℹ️"}.get(rec['priority'], "⚪")
            print(f"{priority_icon} {rec['priority'].upper()}: {rec['title']}")
            print(f"   {rec['description']}")
            print(f"   Action: {rec['action']}")
            print()
        
        # Production Readiness
        readiness = report['production_readiness']
        print("🎯 PRODUCTION READINESS ASSESSMENT")
        print("-" * 40)
        readiness_icon = {
            'ready': '✅',
            'ready_with_monitoring': '🟡', 
            'conditional': '⚠️',
            'not_ready': '❌'
        }.get(readiness['readiness_level'], '❓')
        
        print(f"Status: {readiness_icon} {readiness['readiness_level'].replace('_', ' ').upper()}")
        print(f"Assessment: {readiness['status_message']}")
        print(f"Critical Issues: {readiness['critical_issues_count']}")
        print(f"High Priority Issues: {readiness['high_priority_issues_count']}")
        
        if readiness['blocking_issues']:
            print("\nBlocking Issues:")
            for issue in readiness['blocking_issues']:
                print(f"  - {issue['title']} ({issue['priority']})")
        
        print("\n" + "="*80)
        print("END OF QA REPORT")
        print("="*80)
    
    def save_report(self, report, filename=None):
        """Save report to JSON file"""
        if filename is None:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f'/Users/<USER>/WorkSpace/obs-ai-translator/vad_regression_test_report_{timestamp}.json'
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        print(f"\n📁 Test report saved to: {filename}")
        return filename


def main():
    """Main test execution function"""
    
    print("🔬 VAD COMPREHENSIVE REGRESSION TEST SUITE")
    print("==========================================")
    print()
    print("Starting comprehensive regression testing of the VAD system...")
    print("This test suite covers:")
    print("  1. Basic Functionality Tests")
    print("  2. Edge Cases and Error Scenarios")
    print("  3. Integration Tests")
    print("  4. Performance and Stability Tests")
    print()
    print("Based on recent code changes, special attention to:")
    print("  ✓ Syntax error fix in conditional logic")
    print("  ✓ Silence timer accuracy and reliability")
    print("  ✓ Thread safety issues")
    print("  ✓ Exception handling robustness")
    print()
    
    # Initialize results tracking
    results = TestResults()
    
    try:
        # Run all test suites
        print("🚀 Beginning test execution...")
        
        # 1. Basic Functionality Tests
        basic_tests = BasicFunctionalityTests(results)
        basic_passed, basic_total = basic_tests.run_all_tests()
        
        # 2. Edge Cases and Error Scenarios
        edge_tests = EdgeCasesAndErrorTests(results)
        edge_passed, edge_total = edge_tests.run_all_tests()
        
        # 3. Integration Tests  
        integration_tests = IntegrationTests(results)
        integration_passed, integration_total = integration_tests.run_all_tests()
        
        # 4. Performance and Stability Tests
        performance_tests = PerformanceAndStabilityTests(results)
        performance_passed, performance_total = performance_tests.run_all_tests()
        
        # Generate and display report
        report_generator = QAReportGenerator(results)
        report = report_generator.generate_report()
        
        # Print report to console
        report_generator.print_report(report)
        
        # Save report to file
        report_filename = report_generator.save_report(report)
        
        # Final summary
        total_passed = basic_passed + edge_passed + integration_passed + performance_passed
        total_tests = basic_total + edge_total + integration_total + performance_total
        
        print(f"\n🎯 FINAL SUMMARY")
        print(f"Total Tests Executed: {total_tests}")
        print(f"Total Tests Passed: {total_passed}")
        print(f"Overall Pass Rate: {(total_passed/total_tests*100):.1f}%")
        
        # Return success/failure for CI/CD
        return total_passed == total_tests
        
    except Exception as e:
        print(f"\n❌ Test suite execution failed: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n⛔ Test execution interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Fatal error during test execution: {e}")
        sys.exit(1)