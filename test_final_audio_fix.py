#!/usr/bin/env python3
"""
最终音频修复验证测试
测试用户报告的"一句话之后就卡住，PaMacCore错误"问题
"""
import subprocess
import time
import signal
import sys
import threading
from contextlib import contextmanager

@contextmanager
def timeout(duration):
    """超时上下文管理器"""
    def timeout_handler(signum, frame):
        raise TimeoutError(f"操作超时 {duration} 秒")
    
    signal.signal(signal.SIGALRM, timeout_handler)
    signal.alarm(duration)
    try:
        yield
    finally:
        signal.alarm(0)

def test_system_startup():
    """测试系统启动是否正常"""
    print("🧪 测试 1: 系统启动验证")
    
    try:
        with timeout(10):
            # 启动系统并快速关闭
            process = subprocess.Popen(
                ['python3', 'obs_ai_translator_mic.py', '--test'],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            
            # 等待系统启动
            startup_output = ""
            start_time = time.time()
            
            while time.time() - start_time < 5:
                line = process.stdout.readline()
                if line:
                    startup_output += line
                    print(f"启动日志: {line.strip()}")
                    
                    # 检查是否有PaMacCore错误
                    if "PaMacCore" in line and "Error" in line:
                        process.terminate()
                        return False, f"检测到PaMacCore错误: {line.strip()}"
                    
                    # 检查系统是否完全启动
                    if "按Ctrl+C停止..." in line:
                        print("✅ 系统成功启动，未出现音频错误")
                        break
            else:
                process.terminate()
                return False, "系统启动超时"
            
            # 优雅关闭系统
            process.terminate()
            process.wait(timeout=3)
            
            return True, "系统启动正常，无音频错误"
            
    except TimeoutError:
        return False, "测试超时"
    except Exception as e:
        return False, f"测试异常: {e}"

def test_multiple_startups():
    """测试多次启动是否稳定"""
    print("\n🧪 测试 2: 多次启动稳定性")
    
    for i in range(3):
        print(f"第 {i+1} 次启动测试...")
        success, message = test_system_startup()
        if not success:
            return False, f"第{i+1}次启动失败: {message}"
        time.sleep(1)
    
    return True, "多次启动测试通过"

def test_system_recovery():
    """测试系统恢复能力"""
    print("\n🧪 测试 3: 系统恢复能力")
    
    try:
        # 启动系统
        process = subprocess.Popen(
            ['python3', 'obs_ai_translator_mic.py', '--test'],
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            text=True
        )
        
        # 等待启动完成
        time.sleep(3)
        
        # 模拟用户重置操作 (发送 'r')
        try:
            process.stdin.write('r\n')
            process.stdin.flush()
        except:
            pass  # 可能stdin不可写
        
        # 继续运行一会儿
        time.sleep(2)
        
        # 检查进程是否仍然正常
        if process.poll() is None:
            print("✅ 系统在重置后仍然正常运行")
            process.terminate()
            process.wait(timeout=3)
            return True, "系统恢复功能正常"
        else:
            return False, "系统在重置后意外退出"
            
    except Exception as e:
        return False, f"恢复测试异常: {e}"

def main():
    """主测试函数"""
    print("=== 最终音频修复验证测试 ===")
    print("针对用户报告: '一句话之后就卡住，PaMacCore (AUHAL) Error'")
    print()
    
    tests = [
        ("系统启动验证", test_system_startup),
        ("多次启动稳定性", test_multiple_startups), 
        ("系统恢复能力", test_system_recovery)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 运行测试: {test_name}")
        try:
            success, message = test_func()
            if success:
                print(f"✅ {test_name}: 通过 - {message}")
                passed += 1
            else:
                print(f"❌ {test_name}: 失败 - {message}")
        except Exception as e:
            print(f"❌ {test_name}: 异常 - {e}")
    
    print(f"\n📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！音频设备冲突问题已修复")
        print("\n✅ 修复摘要:")
        print("- 麦克风模式下禁用音频播放，避免设备冲突")
        print("- 智能设备选择，避开正在使用的麦克风")
        print("- 增强错误处理和恢复机制")
        print("- TTS禁用选项作为备选方案")
        return 0
    else:
        print("❌ 部分测试失败，仍存在问题需要解决")
        return 1

if __name__ == "__main__":
    sys.exit(main())