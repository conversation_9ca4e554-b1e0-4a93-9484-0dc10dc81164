#!/usr/bin/env python3
"""
修复工作线程重启和AUHAL错误问题
深度诊断和修复方案
"""

import sys
import time
import threading
import sounddevice as sd
import numpy as np
from contextlib import contextmanager
import queue
import logging

# 配置详细日志
logging.basicConfig(
    level=logging.DEBUG,
    format='[%(asctime)s] %(levelname)s [%(threadName)s] %(message)s'
)
logger = logging.getLogger(__name__)

class EnhancedAudioDeviceManager:
    """增强的音频设备管理器 - 解决AUHAL错误"""
    
    def __init__(self):
        self.device_lock = threading.RLock()
        self.active_streams = {}
        self.device_health = {}
        self.error_counts = {}
        self._init_device_info()
        
    def _init_device_info(self):
        """初始化设备信息"""
        try:
            self.devices = sd.query_devices()
            self.default_input = sd.default.device[0]
            self.default_output = sd.default.device[1]
            
            # 初始化设备健康状态
            for i, device in enumerate(self.devices):
                self.device_health[i] = True
                self.error_counts[i] = 0
                
            logger.info(f"初始化 {len(self.devices)} 个音频设备")
        except Exception as e:
            logger.error(f"初始化设备失败: {e}")
            self.devices = []
    
    def mark_device_error(self, device_id, error):
        """记录设备错误"""
        with self.device_lock:
            self.error_counts[device_id] = self.error_counts.get(device_id, 0) + 1
            
            # 如果错误次数过多，标记设备不健康
            if self.error_counts[device_id] >= 3:
                self.device_health[device_id] = False
                logger.warning(f"设备 {device_id} 标记为不健康（错误次数: {self.error_counts[device_id]}）")
                
            # 检查AUHAL错误
            if "-10863" in str(error):
                logger.error(f"检测到AUHAL错误 -10863 on 设备 {device_id}")
                self.device_health[device_id] = False
                return True
        return False
    
    def get_healthy_output_device(self, exclude_ids=None):
        """获取健康的输出设备"""
        if exclude_ids is None:
            exclude_ids = []
            
        with self.device_lock:
            # 优先级：健康的蓝牙 > 健康的扬声器 > 其他健康设备
            priority_groups = [
                ['airpods', 'bluetooth', 'wireless'],
                ['speaker', '扬声器', 'external'],
                ['display', 'monitor'],
                ['headphone', 'headset']
            ]
            
            for priority_keywords in priority_groups:
                for i, device in enumerate(self.devices):
                    # 检查基本条件
                    if (device['max_output_channels'] == 0 or
                        i in exclude_ids or
                        not self.device_health.get(i, True) or
                        i in self.active_streams):
                        continue
                    
                    # 检查设备名称
                    device_name = device['name'].lower()
                    if any(kw in device_name for kw in priority_keywords):
                        logger.info(f"选择健康输出设备: {i} - {device['name']}")
                        return i
            
            # 选择任意健康设备
            for i, device in enumerate(self.devices):
                if (device['max_output_channels'] > 0 and
                    i not in exclude_ids and
                    self.device_health.get(i, True) and
                    i not in self.active_streams):
                    logger.info(f"选择备用健康设备: {i} - {device['name']}")
                    return i
                    
            logger.warning("无可用的健康输出设备")
            return None
    
    @contextmanager
    def safe_device_access(self, device_id, operation_name):
        """安全的设备访问上下文管理器"""
        acquired = False
        try:
            with self.device_lock:
                if device_id in self.active_streams:
                    raise RuntimeError(f"设备 {device_id} 正被 {self.active_streams[device_id]} 使用")
                self.active_streams[device_id] = operation_name
                acquired = True
                logger.debug(f"获取设备 {device_id} for {operation_name}")
            
            yield device_id
            
        except Exception as e:
            if acquired:
                self.mark_device_error(device_id, e)
            raise
        finally:
            if acquired:
                with self.device_lock:
                    if device_id in self.active_streams:
                        del self.active_streams[device_id]
                        logger.debug(f"释放设备 {device_id}")

class RobustWorkerThread(threading.Thread):
    """健壮的工作线程 - 防止不健康重启"""
    
    def __init__(self, name, target_func, work_queue, max_failures=3):
        super().__init__(name=name, daemon=False)
        self.target_func = target_func
        self.work_queue = work_queue
        self.max_failures = max_failures
        self.failure_count = 0
        self.is_healthy = True
        self.should_stop = threading.Event()
        self.last_error = None
        self.last_heartbeat = time.time()
        
    def run(self):
        """运行工作线程"""
        logger.info(f"工作线程 {self.name} 启动")
        
        while not self.should_stop.is_set():
            try:
                # 更新心跳
                self.last_heartbeat = time.time()
                
                # 获取任务（超时避免死锁）
                try:
                    item = self.work_queue.get(timeout=1.0)
                except queue.Empty:
                    continue
                
                # 处理任务
                logger.debug(f"{self.name} 处理任务")
                self.target_func(item)
                
                # 成功处理，重置失败计数
                if self.failure_count > 0:
                    self.failure_count = 0
                    logger.info(f"{self.name} 恢复健康")
                
            except Exception as e:
                self.failure_count += 1
                self.last_error = e
                logger.error(f"{self.name} 处理失败 ({self.failure_count}/{self.max_failures}): {e}")
                
                # 检查是否需要标记为不健康
                if self.failure_count >= self.max_failures:
                    self.is_healthy = False
                    logger.critical(f"{self.name} 标记为不健康")
                    
                    # 等待一段时间后尝试恢复
                    time.sleep(5)
                    self.failure_count = 0
                    self.is_healthy = True
                    logger.info(f"{self.name} 尝试自动恢复")
        
        logger.info(f"工作线程 {self.name} 停止")
    
    def stop(self, timeout=5):
        """停止工作线程"""
        logger.info(f"请求停止 {self.name}")
        self.should_stop.set()
        self.join(timeout)
        if self.is_alive():
            logger.warning(f"{self.name} 未在 {timeout} 秒内停止")
    
    def check_health(self):
        """检查线程健康状态"""
        # 检查心跳
        if time.time() - self.last_heartbeat > 10:
            logger.warning(f"{self.name} 心跳超时")
            return False
        
        # 检查健康标志
        if not self.is_healthy:
            logger.warning(f"{self.name} 不健康: {self.last_error}")
            return False
            
        return True

class AudioPlaybackManager:
    """音频播放管理器 - 防止AUHAL错误"""
    
    def __init__(self, device_manager):
        self.device_manager = device_manager
        self.playback_lock = threading.Lock()
        self.last_playback_time = 0
        self.min_playback_interval = 0.1  # 最小播放间隔
        
    def play_audio_safe(self, audio_data, sample_rate=48000, exclude_devices=None):
        """安全播放音频"""
        with self.playback_lock:
            # 检查播放间隔
            current_time = time.time()
            time_since_last = current_time - self.last_playback_time
            if time_since_last < self.min_playback_interval:
                wait_time = self.min_playback_interval - time_since_last
                logger.debug(f"等待 {wait_time:.3f}s 避免设备冲突")
                time.sleep(wait_time)
            
            # 获取健康设备
            device_id = self.device_manager.get_healthy_output_device(exclude_devices)
            if device_id is None:
                logger.error("无可用音频设备")
                return False
            
            try:
                # 使用安全访问
                with self.device_manager.safe_device_access(device_id, "音频播放"):
                    # 播放音频
                    sd.stop()  # 停止任何现有播放
                    sd.play(audio_data, sample_rate, device=device_id, blocking=False)
                    
                    # 等待播放完成
                    duration = len(audio_data) / sample_rate
                    time.sleep(duration + 0.1)
                    
                    sd.stop()  # 确保停止
                    
                    self.last_playback_time = time.time()
                    logger.info(f"音频播放成功 on 设备 {device_id}")
                    return True
                    
            except Exception as e:
                logger.error(f"播放失败: {e}")
                
                # 检查AUHAL错误
                if "-10863" in str(e):
                    logger.critical("AUHAL错误检测到，执行恢复程序")
                    self.recover_from_auhal_error()
                    
                return False
    
    def recover_from_auhal_error(self):
        """从AUHAL错误恢复"""
        try:
            logger.info("执行AUHAL错误恢复...")
            
            # 1. 停止所有音频
            sd.stop()
            time.sleep(0.5)
            
            # 2. 重新初始化设备
            self.device_manager._init_device_info()
            
            # 3. 清理设备状态
            self.device_manager.active_streams.clear()
            
            logger.info("AUHAL错误恢复完成")
            
        except Exception as e:
            logger.error(f"恢复失败: {e}")

def test_robust_system():
    """测试健壮的系统"""
    logger.info("=== 测试健壮系统 ===")
    
    # 创建组件
    device_manager = EnhancedAudioDeviceManager()
    playback_manager = AudioPlaybackManager(device_manager)
    
    # 创建工作队列
    work_queue = queue.Queue()
    
    # 创建健壮的工作线程
    def process_item(item):
        """处理任务"""
        logger.info(f"处理: {item}")
        if "audio" in item:
            # 生成测试音频
            audio = np.sin(2 * np.pi * 440 * np.linspace(0, 0.5, 24000)).astype(np.float32) * 0.1
            playback_manager.play_audio_safe(audio)
        time.sleep(0.5)
    
    worker = RobustWorkerThread("TestWorker", process_item, work_queue)
    worker.start()
    
    # 测试场景
    try:
        # 1. 正常任务
        logger.info("测试正常任务...")
        work_queue.put({"type": "normal"})
        time.sleep(1)
        assert worker.check_health(), "工作线程应该健康"
        
        # 2. 音频任务
        logger.info("测试音频任务...")
        work_queue.put({"type": "audio"})
        time.sleep(2)
        assert worker.check_health(), "工作线程应该保持健康"
        
        # 3. 批量任务
        logger.info("测试批量任务...")
        for i in range(3):
            work_queue.put({"type": "audio", "id": i})
            time.sleep(0.5)
        
        time.sleep(3)
        assert worker.check_health(), "工作线程应该处理批量任务"
        
        logger.info("✅ 所有测试通过")
        
    finally:
        # 清理
        worker.stop()
        logger.info("测试完成")

def main():
    """主函数"""
    logger.info("=== 工作线程和AUHAL错误修复 ===")
    
    # 运行测试
    test_robust_system()
    
    logger.info("\n修复方案:")
    logger.info("1. 增强的设备健康监控")
    logger.info("2. 工作线程自动恢复机制")
    logger.info("3. AUHAL错误检测和恢复")
    logger.info("4. 设备访问互斥保护")
    logger.info("5. 播放间隔控制")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        logger.info("程序被中断")
    except Exception as e:
        logger.error(f"程序错误: {e}")
        sys.exit(1)