#!/usr/bin/env python3
"""
运行完整的翻译测试
"""
import subprocess
import time
import threading
import asyncio
import edge_tts
import sounddevice as sd
import numpy as np
from pydub import AudioSegment
import io
import signal
import sys

class TranslatorTest:
    def __init__(self):
        self.translator_process = None
        
    async def generate_test_audio(self):
        """生成测试音频"""
        text = "你好，这是一个测试。我们正在测试AI同声传译系统的完整功能。"
        voice = "zh-CN-XiaoxiaoNeural"
        
        communicate = edge_tts.Communicate(text, voice)
        audio_data = b""
        
        async for chunk in communicate.stream():
            if chunk["type"] == "audio":
                audio_data += chunk["data"]
        
        return audio_data, text
    
    def convert_audio_for_blackhole(self, audio_data):
        """转换音频用于BlackHole播放"""
        audio_segment = AudioSegment.from_mp3(io.BytesIO(audio_data))
        audio_segment = audio_segment.set_frame_rate(48000).set_channels(2)
        audio_np = np.array(audio_segment.get_array_of_samples(), dtype=np.float32)
        audio_np = audio_np.reshape((-1, 2))
        audio_np = audio_np / 32768.0
        return audio_np
    
    def start_translator(self):
        """启动翻译系统"""
        print("🚀 启动AI翻译系统...")
        self.translator_process = subprocess.Popen([
            'python3', 'obs_ai_translator_main.py', '--test'
        ], stdout=subprocess.PIPE, stderr=subprocess.STDOUT, text=True, bufsize=1)
        
        # 等待系统启动
        print("等待系统启动...")
        time.sleep(8)
        
    def stop_translator(self):
        """停止翻译系统"""
        if self.translator_process:
            print("停止AI翻译系统...")
            self.translator_process.terminate()
            self.translator_process.wait()
    
    async def play_test_audio(self):
        """播放测试音频"""
        print("\n=== 生成测试音频 ===")
        audio_data, original_text = await self.generate_test_audio()
        print(f"测试文本: {original_text}")
        
        audio_np = self.convert_audio_for_blackhole(audio_data)
        print(f"音频长度: {len(audio_np)/48000:.2f}秒")
        
        # 查找BlackHole设备
        devices = sd.query_devices()
        blackhole_device = None
        
        for i, device in enumerate(devices):
            if 'BlackHole' in device['name'] and 'ch' in device['name']:
                blackhole_device = i
                break
        
        if blackhole_device is None:
            print("❌ 未找到BlackHole设备")
            return
        
        print(f"\n🎵 播放音频到BlackHole设备 {blackhole_device}...")
        sd.play(audio_np, 48000, device=blackhole_device)
        sd.wait()
        print("✓ 音频播放完成")
        
        # 等待处理完成
        print("等待AI处理完成...")
        time.sleep(15)
    
    async def run_test(self):
        """运行完整测试"""
        try:
            print("=== AI同声传译完整流程测试 ===\n")
            
            # 启动翻译系统
            self.start_translator()
            
            # 播放测试音频
            await self.play_test_audio()
            
            print("\n=== 测试完成 ===")
            print("检查上方输出，应该包含:")
            print("✓ [VAD] 检测到说话开始")
            print("✓ [ASR] 识别结果")
            print("✓ [翻译] 翻译结果")
            print("✓ [TTS] 完成")
            print("✓ [音频] 播放翻译语音")
            
        except KeyboardInterrupt:
            print("\n测试被中断")
        finally:
            self.stop_translator()

def signal_handler(sig, frame):
    print("\n退出测试...")
    sys.exit(0)

if __name__ == "__main__":
    signal.signal(signal.SIGINT, signal_handler)
    
    test = TranslatorTest()
    asyncio.run(test.run_test())