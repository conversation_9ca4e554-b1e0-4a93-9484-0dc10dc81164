#!/usr/bin/env python3
"""
简单的翻译器测试
"""
import numpy as np
from realtime_translator import RealtimeTranslator
import asyncio
import edge_tts
from pydub import AudioSegment
import io
import time

async def test_translation_pipeline():
    print("=== 简单翻译管道测试 ===\n")
    
    # 创建翻译器
    print("1. 初始化翻译器...")
    translator = RealtimeTranslator(source_lang='zh', target_lang='en')
    print("✓ 翻译器初始化完成")
    
    # 生成测试音频
    print("\n2. 生成测试音频...")
    text = "你好，这是一个AI同声传译系统的完整功能测试。我们正在验证语音识别、机器翻译和语音合成的整个流程是否能够正常工作。"
    voice = "zh-CN-XiaoxiaoNeural"
    
    communicate = edge_tts.Communicate(text, voice)
    audio_data = b""
    
    async for chunk in communicate.stream():
        if chunk["type"] == "audio":
            audio_data += chunk["data"]
    
    # 转换音频格式
    audio_segment = AudioSegment.from_mp3(io.BytesIO(audio_data))
    audio_segment = audio_segment.set_frame_rate(48000).set_channels(1)  # 单声道
    audio_np = np.array(audio_segment.get_array_of_samples(), dtype=np.float32)
    audio_np = audio_np / 32768.0  # 归一化
    
    print(f"✓ 音频生成完成: {len(audio_np)} samples, {len(audio_np)/48000:.2f}秒")
    print(f"原始文本: {text}")
    
    # 模拟音频流输入
    print("\n3. 模拟音频流输入...")
    chunk_size = 1024  # 每次处理1024个样本
    
    for i in range(0, len(audio_np), chunk_size):
        chunk = audio_np[i:i+chunk_size]
        translator.process_audio(chunk)
        time.sleep(0.02)  # 模拟实时输入 (21ms间隔)
    
    # 等待处理完成
    print("\n4. 等待处理完成...")
    time.sleep(10)
    
    print("\n=== 测试完成 ===")

if __name__ == "__main__":
    asyncio.run(test_translation_pipeline())