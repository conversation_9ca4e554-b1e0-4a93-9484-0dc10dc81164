#!/usr/bin/env python3
"""
检查音频路由配置，找出BlackHole的音频来源
"""
import subprocess
import json
import re

def check_system_audio_settings():
    """检查系统音频设置"""
    print("=== 系统音频设置检查 ===")
    
    try:
        # 检查当前输入设备
        result = subprocess.run(['osascript', '-e', 'get volume settings'], 
                              capture_output=True, text=True, timeout=5)
        if result.returncode == 0:
            print(f"系统音量设置: {result.stdout.strip()}")
    except:
        pass
    
    try:
        # 检查音频设备详情
        result = subprocess.run(['system_profiler', 'SPAudioDataType', '-json'], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            data = json.loads(result.stdout)
            audio_devices = data.get('SPAudioDataType', [])
            
            print("\n=== 音频设备详情 ===")
            for device in audio_devices:
                name = device.get('_name', 'Unknown')
                if 'BlackHole' in name or 'AI同传' in name or 'VB-Cable' in name:
                    print(f"\n设备: {name}")
                    for key, value in device.items():
                        if not key.startswith('_'):
                            print(f"  {key}: {value}")
    except Exception as e:
        print(f"无法获取详细设备信息: {e}")

def check_audio_midi_setup():
    """检查音频MIDI设置"""
    print("\n=== 音频MIDI设置建议 ===")
    print("请手动检查以下项目:")
    print("1. 打开 应用程序 → 实用工具 → 音频MIDI设置")
    print("2. 查看BlackHole 16ch设备的配置")
    print("3. 检查是否有聚合设备或多输出设备包含BlackHole")
    print("4. 查看哪些应用正在使用BlackHole作为输出")

def check_running_audio_apps():
    """检查可能使用音频的运行中应用"""
    print("\n=== 检查音频应用 ===")
    
    # 常见的音频应用
    audio_apps = [
        'Music', 'Spotify', 'iTunes', 'VLC', 'QuickTime Player',
        'Chrome', 'Safari', 'Firefox', 'Zoom', 'Teams', 'Skype',
        'OBS', 'Logic Pro', 'GarageBand', 'Audacity'
    ]
    
    try:
        # 获取运行中的应用
        result = subprocess.run(['ps', 'aux'], capture_output=True, text=True, timeout=5)
        if result.returncode == 0:
            processes = result.stdout.lower()
            
            running_audio_apps = []
            for app in audio_apps:
                if app.lower() in processes:
                    running_audio_apps.append(app)
            
            if running_audio_apps:
                print("发现运行中的音频相关应用:")
                for app in running_audio_apps:
                    print(f"  - {app}")
                print("\n💡 建议: 检查这些应用的音频输出设置")
            else:
                print("未发现明显的音频应用")
    except:
        print("无法检查运行中的应用")

def get_current_audio_devices():
    """获取当前系统音频设备设置"""
    print("\n=== 当前音频设备设置 ===")
    
    try:
        # 检查输入设备
        result = subprocess.run([
            'osascript', '-e', 
            'tell application "System Preferences" to get the name of current input device'
        ], capture_output=True, text=True, timeout=5)
        
        if result.returncode == 0:
            print(f"当前输入设备: {result.stdout.strip()}")
    except:
        pass
    
    try:
        # 检查输出设备
        result = subprocess.run([
            'osascript', '-e', 
            'tell application "System Preferences" to get the name of current output device'
        ], capture_output=True, text=True, timeout=5)
        
        if result.returncode == 0:
            print(f"当前输出设备: {result.stdout.strip()}")
    except:
        pass

def provide_solutions():
    """提供解决方案"""
    print("\n" + "="*50)
    print("🔧 解决方案建议")
    print("="*50)
    
    print("\n1. 🎯 立即解决方案:")
    print("   a) 打开 系统偏好设置 → 声音")
    print("   b) 确保输出设备不是BlackHole或包含BlackHole的聚合设备")
    print("   c) 确保输入设备是您想要的麦克风")
    
    print("\n2. 🔍 深度检查:")
    print("   a) 打开 音频MIDI设置")
    print("   b) 查看是否有聚合设备或多输出设备")
    print("   c) 检查这些设备是否包含BlackHole")
    print("   d) 如果有，考虑临时禁用或重新配置")
    
    print("\n3. 🚫 应用检查:")
    print("   a) 检查OBS的音频设置")
    print("   b) 确保没有其他应用将音频输出到BlackHole")
    print("   c) 暂停所有音频播放应用进行测试")
    
    print("\n4. 🛠️ 临时解决方案:")
    print("   a) 提高语音检测阈值:")
    print("      python3 fix_vad_sensitivity.py")
    print("   b) 或者手动修改 realtime_translator.py 中的:")
    print("      self.speech_threshold = 0.015  # 从0.008提高到0.015")
    
    print("\n5. 🧪 验证修复:")
    print("   a) 重新运行诊断: python3 diagnose_audio_input.py")
    print("   b) 在安静环境中测试翻译程序")
    print("   c) 观察是否还有误触发")

def main():
    print("=== BlackHole音频路由检查工具 ===")
    print("根据诊断结果，您的BlackHole正在接收音频输入")
    print("让我们找出音频来源...")
    
    # 检查系统设置
    check_system_audio_settings()
    
    # 检查当前设备
    get_current_audio_devices()
    
    # 检查运行中的应用
    check_running_audio_apps()
    
    # 音频MIDI设置建议
    check_audio_midi_setup()
    
    # 提供解决方案
    provide_solutions()

if __name__ == "__main__":
    main()
