#!/usr/bin/env python3
"""
调试VAD问题
"""

import sys
import time
import numpy as np
from obs_ai_translator_mic import MicrophoneTranslatorSystem

def debug_audio_volume():
    """调试音频音量检测"""
    print("=== 调试音频音量检测 ===\n")
    
    # 生成不同音量的测试音频
    sample_rate = 48000
    duration = 0.5
    t = np.linspace(0, duration, int(sample_rate * duration), False)
    
    volumes = [0.001, 0.005, 0.008, 0.01, 0.02, 0.05]
    
    system = MicrophoneTranslatorSystem(
        enable_tts=False,
        source_lang='zh',
        target_lang='en'
    )
    
    try:
        print(f"VAD阈值: {system.translator.speech_threshold}")
        print("\n测试不同音量:")
        
        for vol in volumes:
            # 生成测试音频
            audio = vol * np.sin(2 * np.pi * 200 * t).astype(np.float32)
            actual_volume = np.sqrt(np.mean(audio**2))
            
            print(f"\n设定音量: {vol:.3f}, 实际RMS: {actual_volume:.3f}")
            
            # 重置状态
            system.translator.is_speaking = False
            
            # 处理音频
            chunk_size = 4800
            for i in range(0, len(audio), chunk_size):
                chunk = audio[i:i+chunk_size]
                system.translator.process_audio(chunk)
                time.sleep(0.01)
            
            # 检查是否触发了语音检测
            if system.translator.is_speaking:
                print("  ✅ 触发了语音检测")
                
                # 发送静音结束
                silence = np.zeros(4800, dtype=np.float32)
                for j in range(12):
                    system.translator.process_audio(silence)
                    time.sleep(0.1)
                    if not system.translator.is_speaking:
                        break
                        
                print(f"  队列项目: {system.translator.work_queue.qsize()}")
            else:
                print("  ❌ 未触发语音检测")
    
    finally:
        system.translator.shutdown()

def debug_real_time_processing():
    """调试实时处理"""
    print("\n=== 调试实时处理 ===\n")
    
    system = MicrophoneTranslatorSystem(
        enable_tts=False,
        source_lang='zh',
        target_lang='en'
    )
    
    try:
        # 生成明显的语音信号
        sample_rate = 48000
        duration = 2.0
        t = np.linspace(0, duration, int(sample_rate * duration), False)
        
        # 多频率混合，模拟真实语音
        audio = (
            0.02 * np.sin(2 * np.pi * 200 * t) +
            0.015 * np.sin(2 * np.pi * 400 * t) +
            0.01 * np.sin(2 * np.pi * 800 * t)
        ).astype(np.float32)
        
        # 添加包络
        envelope = np.hanning(len(audio))
        audio = audio * envelope
        
        actual_volume = np.sqrt(np.mean(audio**2))
        print(f"测试音频RMS音量: {actual_volume:.4f}")
        print(f"VAD阈值: {system.translator.speech_threshold}")
        print(f"应该{'能' if actual_volume > system.translator.speech_threshold else '不能'}触发检测")
        
        print("\n开始处理音频...")
        
        chunk_size = 4800  # 0.1秒
        start_time = time.time()
        
        for i in range(0, len(audio), chunk_size):
            chunk = audio[i:i+chunk_size]
            chunk_volume = np.sqrt(np.mean(chunk**2))
            
            # 处理前状态
            speaking_before = system.translator.is_speaking
            system.translator.process_audio(chunk)
            speaking_after = system.translator.is_speaking
            
            # 显示状态变化
            if speaking_after and not speaking_before:
                print(f"  🎤 开始检测 at {time.time()-start_time:.1f}s, 音量: {chunk_volume:.4f}")
            elif not speaking_after and speaking_before:
                print(f"  🔇 结束检测 at {time.time()-start_time:.1f}s")
            
            time.sleep(0.01)
        
        print("\n音频发送完成，发送静音...")
        
        # 发送足够长的静音
        silence = np.zeros(4800, dtype=np.float32)
        for i in range(15):  # 1.5秒静音
            system.translator.process_audio(silence)
            time.sleep(0.1)
            
            if not system.translator.is_speaking:
                print(f"  🔇 静音触发结束 at {time.time()-start_time:.1f}s")
                break
        
        print(f"\n最终队列状态: {system.translator.work_queue.qsize()} 项")
        
        # 等待处理
        print("等待工作线程处理...")
        time.sleep(2)
        
        print(f"处理后队列状态: {system.translator.work_queue.qsize()} 项")
        
    finally:
        system.translator.shutdown()

def main():
    print("=== VAD调试工具 ===\n")
    
    # 1. 音量调试
    debug_audio_volume()
    
    # 2. 实时处理调试
    debug_real_time_processing()

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n调试被中断")
    except Exception as e:
        print(f"调试异常: {e}")
        import traceback
        traceback.print_exc()