#!/usr/bin/env python3
"""
测试完整的AI翻译管道
"""
import asyncio
import edge_tts
import sounddevice as sd
import numpy as np
import time
from pydub import AudioSegment
import io

async def generate_test_audio():
    """生成测试音频"""
    text = "你好，这是一个测试。我们正在测试AI同声传译系统。"
    voice = "zh-CN-XiaoxiaoNeural"
    
    communicate = edge_tts.Communicate(text, voice)
    audio_data = b""
    
    async for chunk in communicate.stream():
        if chunk["type"] == "audio":
            audio_data += chunk["data"]
    
    return audio_data, text

def convert_audio_for_blackhole(audio_data):
    """转换音频用于BlackHole播放"""
    # 从MP3转换
    audio_segment = AudioSegment.from_mp3(io.BytesIO(audio_data))
    
    # 转换为48kHz立体声
    audio_segment = audio_segment.set_frame_rate(48000).set_channels(2)
    
    # 转换为numpy数组
    audio_np = np.array(audio_segment.get_array_of_samples(), dtype=np.float32)
    audio_np = audio_np.reshape((-1, 2))  # 立体声
    audio_np = audio_np / 32768.0  # 归一化
    
    return audio_np

async def test_pipeline():
    """测试完整管道"""
    print("=== 生成测试音频 ===")
    audio_data, original_text = await generate_test_audio()
    print(f"原始文本: {original_text}")
    
    # 转换音频
    audio_np = convert_audio_for_blackhole(audio_data)
    print(f"音频数据: {audio_np.shape}, 时长: {len(audio_np)/48000:.2f}秒")
    
    # 查找BlackHole设备
    devices = sd.query_devices()
    blackhole_device = None
    
    for i, device in enumerate(devices):
        if 'BlackHole' in device['name'] and 'ch' in device['name']:
            blackhole_device = i
            print(f"找到BlackHole设备: {device['name']} (ID: {i})")
            break
    
    if blackhole_device is None:
        print("❌ 未找到BlackHole设备")
        return
    
    print("\n=== 播放测试音频到BlackHole ===")
    print("注意: 请确保AI翻译系统正在运行!")
    print("音频将在3秒后播放...")
    
    for i in range(3, 0, -1):
        print(f"倒计时: {i}")
        time.sleep(1)
    
    # 播放到BlackHole
    print("🎵 播放音频...")
    sd.play(audio_np, 48000, device=blackhole_device)
    sd.wait()  # 等待播放完成
    
    print("✓ 音频播放完成")
    print("\n如果AI翻译系统正在运行，您应该看到:")
    print("1. VAD检测到语音")
    print("2. Whisper进行语音识别")
    print("3. GPT进行翻译")
    print("4. TTS合成英文语音")
    print("5. 播放翻译结果")

if __name__ == "__main__":
    asyncio.run(test_pipeline())