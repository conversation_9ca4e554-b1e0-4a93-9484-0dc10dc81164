#!/usr/bin/env python3
"""
测试VAD噪声触发修复
验证边缘噪声不会重置静音计时
"""

import time
import numpy as np
from obs_ai_translator_mic import MicrophoneTranslatorSystem

def test_noise_rejection():
    """测试噪声拒绝和去抖动"""
    print("=== 测试VAD噪声触发修复 ===\n")
    
    system = MicrophoneTranslatorSystem(
        enable_tts=False,
        source_lang='zh',
        target_lang='en'
    )
    
    translator = system.translator
    
    print(f"VAD配置:")
    print(f"  语音阈值: {translator.speech_threshold:.5f}")
    print(f"  静音超时: {translator.silence_timeout}秒")
    print(f"  去抖动时间: {translator.speech_debounce_time}秒")
    
    # 测试场景1: 短暂噪声脉冲不应触发
    print("\n测试1: 短暂噪声脉冲（应被忽略）")
    print("-" * 50)
    
    sample_rate = 48000
    chunk_size = 4800
    
    # 发送短暂噪声脉冲（0.1秒）
    noise_pulse = 0.015 * np.sin(2 * np.pi * 300 * np.linspace(0, 0.1, chunk_size, False)).astype(np.float32)
    translator.process_audio(noise_pulse)
    time.sleep(0.1)
    
    # 然后静音
    silence = np.zeros(chunk_size, dtype=np.float32)
    for i in range(5):
        translator.process_audio(silence)
        time.sleep(0.1)
    
    if not translator.is_speaking:
        print("✅ 短暂噪声被正确忽略")
    else:
        print("❌ 短暂噪声错误触发了语音检测")
    
    # 测试场景2: 真实语音后的边缘噪声
    print("\n测试2: 语音后边缘噪声不应重置计时")
    print("-" * 50)
    
    # 重置状态
    translator.is_speaking = False
    translator.speech_start_pending = False
    
    # 1. 发送真实语音（1秒，明显高于阈值）
    t = np.linspace(0, 0.1, chunk_size, False)
    speech = 0.02 * np.sin(2 * np.pi * 300 * t).astype(np.float32)
    
    print("发送1秒语音...")
    for i in range(10):
        translator.process_audio(speech)
        time.sleep(0.1)
    
    speech_end_time = time.time()
    
    # 2. 发送带边缘噪声的"静音"（噪声略低于或接近阈值）
    print("发送边缘噪声（应不重置计时）...")
    edge_noise_level = translator.speech_threshold * 0.8  # 略低于阈值
    
    for i in range(15):  # 1.5秒
        # 偶尔有接近阈值的噪声
        if i % 3 == 0:
            # 偶尔的噪声脉冲
            noise = np.random.normal(0, edge_noise_level * 1.2, chunk_size).astype(np.float32)
        else:
            # 大部分时间是低噪声
            noise = np.random.normal(0, edge_noise_level * 0.5, chunk_size).astype(np.float32)
        
        translator.process_audio(noise)
        time.sleep(0.1)
        
        # 检查是否正确结束
        if not translator.is_speaking:
            end_time = time.time()
            actual_silence_duration = end_time - speech_end_time
            print(f"✅ VAD正确结束，实际静音时长: {actual_silence_duration:.2f}秒")
            break
    else:
        print(f"❌ VAD未在1.5秒内结束，仍在说话状态")
    
    # 测试场景3: 连续语音检测
    print("\n测试3: 连续语音需要去抖动时间")
    print("-" * 50)
    
    # 重置
    translator.is_speaking = False
    translator.speech_start_pending = False
    
    # 发送连续语音
    continuous_speech_start = time.time()
    print(f"发送连续语音（应在{translator.speech_debounce_time}秒后开始）...")
    
    for i in range(10):  # 1秒
        translator.process_audio(speech)
        time.sleep(0.1)
        
        if translator.is_speaking and not hasattr(translator, '_speech_detected_time'):
            translator._speech_detected_time = time.time()
            detection_delay = translator._speech_detected_time - continuous_speech_start
            print(f"语音检测延迟: {detection_delay:.2f}秒")
            
            if detection_delay >= translator.speech_debounce_time * 0.8:  # 允许20%误差
                print(f"✅ 去抖动工作正常")
            else:
                print(f"❌ 检测过快，去抖动可能失效")
            break
    
    # 清理
    system.translator.shutdown()

def test_real_world_scenario():
    """测试真实世界场景"""
    print("\n=== 测试真实世界场景 ===\n")
    
    system = MicrophoneTranslatorSystem(
        enable_tts=False,
        source_lang='zh',
        target_lang='en'
    )
    
    translator = system.translator
    translator.debug_vad = False  # 减少输出
    
    print("模拟真实环境：语音 + 背景噪声 + 静音")
    
    sample_rate = 48000
    chunk_size = 4800
    
    # 场景：说话 -> 停顿（有背景噪声）-> 应该结束
    scenario_start = time.time()
    
    # 1. 清晰的语音（2秒）
    print("\n阶段1: 清晰语音（2秒）")
    t = np.linspace(0, 0.1, chunk_size, False)
    clear_speech = 0.025 * np.sin(2 * np.pi * 300 * t).astype(np.float32)
    
    for i in range(20):
        translator.process_audio(clear_speech)
        time.sleep(0.1)
    
    # 2. 逐渐减弱的语音（模拟说话结束）
    print("阶段2: 语音逐渐减弱（1秒）")
    for i in range(10):
        fading_speech = clear_speech * (1 - i/10)
        translator.process_audio(fading_speech)
        time.sleep(0.1)
    
    speech_fade_end = time.time()
    
    # 3. 背景噪声（应触发结束）
    print("阶段3: 背景噪声（等待VAD结束）")
    background_noise_level = 0.005  # 低于阈值的背景噪声
    
    detected_end = False
    for i in range(20):  # 最多2秒
        noise = np.random.normal(0, background_noise_level, chunk_size).astype(np.float32)
        translator.process_audio(noise)
        time.sleep(0.1)
        
        if not translator.is_speaking:
            end_time = time.time()
            silence_duration = end_time - speech_fade_end
            total_duration = end_time - scenario_start
            
            print(f"\n✅ 场景测试成功!")
            print(f"  总时长: {total_duration:.1f}秒")
            print(f"  静音检测时长: {silence_duration:.1f}秒")
            detected_end = True
            break
    
    if not detected_end:
        print(f"\n❌ 场景测试失败：VAD未能结束")
    
    system.translator.shutdown()
    return detected_end

def main():
    """主测试"""
    print("=== VAD噪声和静音检测修复验证 ===\n")
    print("修复内容:")
    print("🔧 阈值提高: 0.008 → 0.01 (更好地过滤噪声)")
    print("🔧 去抖动: 添加0.3秒去抖动避免短暂噪声触发")
    print("🔧 改进逻辑: 防止边缘噪声重置静音计时")
    print("\n" + "="*60 + "\n")
    
    # 测试1: 噪声拒绝
    test_noise_rejection()
    
    # 测试2: 真实场景
    print("\n" + "="*60)
    success = test_real_world_scenario()
    
    print("\n" + "="*60)
    print("测试总结")
    print("="*60)
    
    if success:
        print("🎉 VAD噪声问题修复成功!")
        print("\n现在系统应该:")
        print("• 不会被短暂噪声触发")
        print("• 不会被边缘噪声重置计时")
        print("• 能在0.8秒真正静音后结束")
        print("• 对真实语音依然敏感")
    else:
        print("⚠️ 仍需调整参数")
        print("\n建议:")
        print("• 运行 diagnose_vad_issue.py 分析环境")
        print("• 根据环境噪声水平调整阈值")

if __name__ == "__main__":
    main()