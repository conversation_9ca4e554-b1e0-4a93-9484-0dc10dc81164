#!/usr/bin/env python3
"""
诊断静音计时器问题
"""

import time
import numpy as np

class SimplifiedVAD:
    """简化的VAD逻辑用于调试"""
    
    def __init__(self):
        self.speech_threshold = 0.01
        self.silence_timeout = 0.8
        self.is_speaking = False
        self.last_speech_time = 0
        self.speech_start_time = 0
        
    def process_current_logic(self, volumes):
        """当前的有问题的逻辑"""
        print("\n=== 当前逻辑（有问题）===")
        
        for i, volume in enumerate(volumes):
            current_time = i * 0.1  # 每0.1秒一个样本
            is_speech = volume > self.speech_threshold
            
            if is_speech:
                if not self.is_speaking:
                    self.is_speaking = True
                    self.speech_start_time = current_time
                    self.last_speech_time = current_time
                    print(f"{current_time:.1f}s: 开始说话 (音量:{volume:.3f})")
                else:
                    # 问题在这里：每次检测到语音都更新last_speech_time
                    self.last_speech_time = current_time
                    print(f"{current_time:.1f}s: 继续说话 (音量:{volume:.3f})")
            
            if self.is_speaking and not is_speech:
                silence_duration = current_time - self.last_speech_time
                print(f"{current_time:.1f}s: 静音 (持续:{silence_duration:.1f}s)")
                
                if silence_duration >= self.silence_timeout:
                    print(f"{current_time:.1f}s: 结束！静音{silence_duration:.1f}s")
                    self.is_speaking = False
    
    def process_fixed_logic(self, volumes):
        """修复后的逻辑"""
        print("\n=== 修复后逻辑 ===")
        
        # 重置状态
        self.is_speaking = False
        self.last_speech_time = 0
        
        for i, volume in enumerate(volumes):
            current_time = i * 0.1
            is_speech = volume > self.speech_threshold
            
            if is_speech:
                self.last_speech_time = current_time  # 始终更新最后语音时间
                
                if not self.is_speaking:
                    self.is_speaking = True
                    self.speech_start_time = current_time
                    print(f"{current_time:.1f}s: 开始说话 (音量:{volume:.3f})")
                else:
                    print(f"{current_time:.1f}s: 继续说话 (音量:{volume:.3f})")
            
            # 注意：这里改为elif，确保静音时始终检查
            elif self.is_speaking:  # 改回elif，只在静音时检查
                silence_duration = current_time - self.last_speech_time
                print(f"{current_time:.1f}s: 静音 (持续:{silence_duration:.1f}s)")
                
                if silence_duration >= self.silence_timeout:
                    print(f"{current_time:.1f}s: 结束！静音{silence_duration:.1f}s")
                    self.is_speaking = False

def test_edge_noise_scenario():
    """测试边缘噪声场景"""
    print("=== 测试场景：边缘噪声 ===")
    print("模拟：语音结束后，噪声在阈值附近波动")
    
    # 创建测试数据：2秒语音，然后边缘噪声
    volumes = []
    
    # 0-2秒：清晰语音
    for i in range(20):
        volumes.append(0.02)
    
    # 2-5秒：边缘噪声（在阈值0.01附近波动）
    noise_pattern = [0.009, 0.011, 0.008, 0.012, 0.007, 0.011, 0.009, 0.008]
    for i in range(30):
        volumes.append(noise_pattern[i % len(noise_pattern)])
    
    # 显示数据
    print("\n音量数据（阈值=0.01）:")
    for i, vol in enumerate(volumes):
        if i % 10 == 0:
            print(f"{i*0.1:.1f}s: ", end="")
        status = "🎤" if vol > 0.01 else "🔇"
        print(f"{status}", end="")
        if (i + 1) % 10 == 0:
            print()
    
    # 测试两种逻辑
    vad = SimplifiedVAD()
    vad.process_current_logic(volumes)
    vad.process_fixed_logic(volumes)

def test_real_silence_scenario():
    """测试真实静音场景"""
    print("\n\n=== 测试场景：真实静音 ===")
    print("模拟：语音结束后，真正的静音")
    
    volumes = []
    
    # 0-2秒：语音
    for i in range(20):
        volumes.append(0.02)
    
    # 2-4秒：真正的静音
    for i in range(20):
        volumes.append(0.005)
    
    vad = SimplifiedVAD()
    vad.process_current_logic(volumes)
    vad.process_fixed_logic(volumes)

def main():
    print("=== 诊断VAD静音计时问题 ===\n")
    
    # 测试1：边缘噪声
    test_edge_noise_scenario()
    
    # 测试2：真实静音
    test_real_silence_scenario()
    
    print("\n=== 问题总结 ===")
    print("当前代码问题：")
    print("1. 使用了 'if self.is_speaking and not is_speech' 而不是 elif")
    print("2. 这导致当is_speech=True时，静音检查逻辑永远不会执行")
    print("3. 边缘噪声不断触发is_speech=True，重置last_speech_time")
    print("4. 结果：静音计时永远无法累积到0.8秒")
    
    print("\n修复方案：")
    print("1. 改回使用 elif self.is_speaking")
    print("2. 提高阈值到0.015或更高，远离噪声水平")
    print("3. 或者使用滑动窗口平均而不是单次采样判断")

if __name__ == "__main__":
    main()