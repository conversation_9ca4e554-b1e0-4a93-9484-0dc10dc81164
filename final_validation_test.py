#!/usr/bin/env python3
"""
最终验证测试 - 验证所有修复
"""

import time
import numpy as np
from obs_ai_translator_mic import MicrophoneTranslatorSystem

def test_syntax_fix():
    """测试语法错误修复"""
    print("=== 测试1: 语法错误修复 ===")
    
    try:
        system = MicrophoneTranslatorSystem(
            enable_tts=False,
            source_lang='zh', 
            target_lang='en'
        )
        print("✅ 系统启动成功，无语法错误")
        
        # 测试reset_system方法
        system.translator.reset_system()
        print("✅ reset_system方法工作正常")
        
        system.translator.shutdown()
        return True
        
    except Exception as e:
        print(f"❌ 语法错误: {e}")
        return False

def test_silence_timer_fix():
    """测试静音计时器修复"""
    print("\n=== 测试2: 静音计时器修复 ===")
    
    system = MicrophoneTranslatorSystem(
        enable_tts=False,
        source_lang='zh',
        target_lang='en'
    )
    
    try:
        translator = system.translator
        print(f"VAD阈值: {translator.speech_threshold}")
        
        # 发送语音
        sample_rate = 48000
        chunk_size = 4800
        
        # 生成超过阈值的语音
        t = np.linspace(0, 0.1, chunk_size, False) 
        speech = 0.012 * np.sin(2 * np.pi * 300 * t).astype(np.float32)
        speech_volume = np.sqrt(np.mean(speech**2))
        
        print(f"语音音量: {speech_volume:.5f}")
        
        # 发送1秒语音
        for i in range(10):
            translator.process_audio(speech)
            time.sleep(0.1)
        
        if translator.is_speaking:
            print("✅ 语音检测正常")
        else:
            print("❌ 语音检测失败")
            system.translator.shutdown()
            return False
        
        # 发送静音
        silence = np.zeros(chunk_size, dtype=np.float32)
        speech_end = time.time()
        
        # 等待静音检测
        for i in range(15):  # 1.5秒
            translator.process_audio(silence)
            time.sleep(0.1)
            
            if not translator.is_speaking:
                end_time = time.time()
                silence_duration = end_time - speech_end
                print(f"✅ 静音检测正常: {silence_duration:.2f}秒后结束")
                system.translator.shutdown()
                return True
        
        print("❌ 静音检测失败，未在1.5秒内结束")
        system.translator.shutdown()
        return False
        
    except Exception as e:
        print(f"❌ 静音计时器测试失败: {e}")
        system.translator.shutdown()
        return False

def test_tts_functionality():
    """测试TTS功能"""
    print("\n=== 测试3: TTS功能 ===")
    
    system = MicrophoneTranslatorSystem(
        enable_tts=True,
        source_lang='zh',
        target_lang='en'
    )
    
    try:
        # 直接测试TTS
        system.translator._synthesize_and_play("Test audio playback.")
        print("✅ TTS调用成功，检查是否有声音输出")
        
        time.sleep(3)
        system.translator.shutdown()
        return True
        
    except Exception as e:
        print(f"❌ TTS测试失败: {e}")
        system.translator.shutdown()
        return False

def test_exception_handling():
    """测试异常处理"""
    print("\n=== 测试4: 异常处理改进 ===")
    
    system = MicrophoneTranslatorSystem(
        enable_tts=False,
        source_lang='zh',
        target_lang='en'
    )
    
    try:
        translator = system.translator
        
        # 测试无效音频
        invalid_audio = np.array([np.inf, np.nan, 1e10]).astype(np.float32)
        translator.process_audio(invalid_audio)
        
        # 测试空音频
        empty_audio = np.array([]).astype(np.float32)
        translator.process_audio(empty_audio)
        
        print("✅ 异常处理工作正常")
        system.translator.shutdown()
        return True
        
    except Exception as e:
        print(f"❌ 异常处理测试失败: {e}")
        system.translator.shutdown()
        return False

def main():
    """主测试函数"""
    print("=== 最终验证测试 ===")
    print("验证所有修复项目:\n")
    print("🔧 语法错误修复")
    print("🔧 静音计时器修复")
    print("🔧 TTS播放修复")
    print("🔧 异常处理改进")
    print("🔧 缺失方法添加")
    print("\n" + "="*50 + "\n")
    
    tests = [
        ("语法修复", test_syntax_fix),
        ("静音计时", test_silence_timer_fix),
        ("TTS功能", test_tts_functionality),
        ("异常处理", test_exception_handling)
    ]
    
    results = []
    for name, test_func in tests:
        try:
            result = test_func()
            results.append((name, result))
        except Exception as e:
            print(f"❌ {name} 测试异常: {e}")
            results.append((name, False))
    
    # 结果汇总
    print("\n" + "="*50)
    print("最终验证结果")
    print("="*50)
    
    passed = 0
    for name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{name:<10} {status}")
        if result:
            passed += 1
    
    total = len(results)
    success_rate = (passed / total) * 100
    
    print("="*50)
    print(f"通过率: {passed}/{total} ({success_rate:.1f}%)")
    
    if success_rate >= 80:
        print("\n🎉 系统验证通过！")
        print("主要修复:")
        print("• ✅ 语法错误已修复")
        print("• ✅ 静音计时器工作正常")
        print("• ✅ TTS音频播放功能恢复")
        print("• ✅ 异常处理更加健壮")
        print("• ✅ 缺失方法已添加")
        print("\n系统现在应该可以正常工作!")
    else:
        print(f"\n⚠️ 系统验证未完全通过 ({success_rate:.1f}%)")
        print("建议进一步调试失败的测试项目")
    
    return success_rate >= 80

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n测试被中断")
    except Exception as e:
        print(f"测试异常: {e}")
        import traceback
        traceback.print_exc()