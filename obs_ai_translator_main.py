#!/usr/bin/env python3
"""
OBS + BlackHole AI同声传译主程序
"""
import asyncio
import sounddevice as sd
import numpy as np
import obsws_python as obs
from queue import Queue
import threading
import time
import os
from realtime_translator import RealtimeTranslator
from config import check_environment

class AudioRouter:
    """音频路由管理器"""
    
    def __init__(self):
        self.sample_rate = 48000
        self.channels = 2
        self.blocksize = 1024
        self.audio_queue = Queue(maxsize=100)
        self.is_running = False
        
        # 查找BlackHole设备
        self.blackhole_in, self.blackhole_out = self._find_blackhole()
        
    def _find_blackhole(self):
        """查找BlackHole设备"""
        devices = sd.query_devices()
        blackhole_in = None
        blackhole_out = None
        
        for i, device in enumerate(devices):
            # 支持BlackHole 2ch 和 BlackHole 16ch
            if 'BlackHole' in device['name'] and 'ch' in device['name']:
                if device['max_input_channels'] > 0:
                    blackhole_in = i
                    print(f"找到BlackHole输入: {device['name']} (设备ID: {i})")
                if device['max_output_channels'] > 0:
                    blackhole_out = i
                    print(f"找到BlackHole输出: {device['name']} (设备ID: {i})")
        
        return blackhole_in, blackhole_out
    
    def start_capture(self, callback=None):
        """开始音频捕获"""
        if self.blackhole_in is None:
            print("错误：未找到BlackHole输入设备")
            return
        
        def audio_callback(indata, frames, time, status):
            if status:
                print(f"音频状态: {status}")

            # 更新最后音频时间
            self.last_audio_time = time.time()

            # 计算音频电平（用于可视化）
            volume = np.sqrt(np.mean(indata**2))

            # 将音频数据加入队列
            if not self.audio_queue.full():
                self.audio_queue.put({
                    'data': indata.copy(),
                    'volume': volume,
                    'timestamp': time.inputBufferAdcTime
                })

            # 调用外部回调（如果有）
            if callback:
                callback(indata, volume)
        
        self.is_running = True
        self.last_audio_time = time.time()

        retry_count = 0
        max_retries = 5

        while self.is_running and retry_count < max_retries:
            try:
                print(f"[音频] 启动音频流 (尝试 {retry_count + 1}/{max_retries})")

                with sd.InputStream(
                    device=self.blackhole_in,
                    channels=self.channels,
                    samplerate=self.sample_rate,
                    blocksize=self.blocksize,
                    dtype='float32',
                    latency='low',
                    callback=audio_callback
                ) as stream:
                    if retry_count == 0:  # 只在第一次显示详细信息
                        print(f"开始从BlackHole捕获音频...")
                        print(f"设备: {sd.query_devices(self.blackhole_in)['name']}")
                        print(f"采样率: {self.sample_rate} Hz")
                        print(f"通道数: {self.channels}")
                        print(f"块大小: {self.blocksize}")
                        print("\n按Ctrl+C停止...")

                    print(f"[音频] ✅ 音频流已启动")
                    retry_count = 0  # 重置重试计数

                    try:
                        while self.is_running:
                            time.sleep(1)

                            # 检查音频流是否还活跃
                            if time.time() - self.last_audio_time > 15:  # 15秒无音频
                                print(f"[音频] ⚠️ 检测到15秒无音频输入，可能音频流中断")
                                break

                            # 检查流状态
                            if not stream.active:
                                print(f"[音频] ❌ 音频流已停止")
                                break

                    except KeyboardInterrupt:
                        print("\n[音频] 用户中断，停止音频捕获")
                        self.is_running = False
                        break

                if self.is_running:
                    print(f"[音频] 音频流中断，准备重启...")
                    retry_count += 1
                    time.sleep(2)  # 等待2秒再重试
                else:
                    break

            except Exception as e:
                print(f"[音频] 音频捕获错误: {e}")
                retry_count += 1
                if retry_count < max_retries:
                    print(f"[音频] 等待3秒后重试...")
                    time.sleep(3)
                else:
                    print(f"[音频] ❌ 音频流启动失败，已达到最大重试次数")
                    self.is_running = False
    
    def send_to_output(self, audio_data):
        """发送音频到BlackHole输出"""
        if self.blackhole_out is None:
            return
        
        try:
            # 确保音频数据格式正确
            if isinstance(audio_data, np.ndarray):
                # 如果是单声道，转为立体声
                if len(audio_data.shape) == 1:
                    audio_data = np.column_stack((audio_data, audio_data))
                
                # 非阻塞播放到BlackHole
                sd.play(audio_data, self.sample_rate, device=self.blackhole_out)
                print(f"[音频路由] 输出到BlackHole: {audio_data.shape}")
            else:
                print(f"[音频路由] 音频数据格式错误: {type(audio_data)}")
                
        except Exception as e:
            print(f"[音频路由] 输出错误: {e}")

    def restart_audio_stream(self):
        """重启音频流"""
        print("[音频] 手动重启音频流...")
        self.last_audio_time = time.time() - 20  # 强制触发重启

    def stop_capture(self):
        """停止音频捕获"""
        self.is_running = False

class OBSController:
    """OBS控制器"""
    
    def __init__(self, host='127.0.0.1', port=4455, password='PD7PcgZnCGrP75K'):
        self.client = None
        self.host = host
        self.port = port
        self.password = password
        
    def connect(self):
        """连接到OBS"""
        try:
            self.client = obs.ReqClient(
                host=self.host,
                port=self.port,
                password=self.password
            )
            version = self.client.get_version()
            print(f"已连接到OBS {version.obs_version}")
            return True
        except Exception as e:
            print(f"OBS连接失败: {e}")
            return False
    
    def update_text_source(self, source_name, text):
        """更新文字源（字幕）"""
        try:
            self.client.set_input_settings(
                inputName=source_name,
                inputSettings={'text': text}
            )
        except Exception as e:
            print(f"更新文字失败: {e}")

class AITranslatorSystem:
    """AI同声传译系统"""
    
    def __init__(self, obs_password=None, source_lang='zh', target_lang='en'):
        self.audio_router = AudioRouter()
        # 如果没有提供密码，使用默认配置
        if obs_password is None:
            self.obs_controller = OBSController()  # 使用默认参数
        else:
            self.obs_controller = OBSController(password=obs_password)
        # 传递音频路由器给翻译器
        self.translator = RealtimeTranslator(
            source_lang=source_lang, 
            target_lang=target_lang,
            audio_router=self.audio_router
        )
        
    def start(self, test_mode=False):
        """启动系统"""
        print("=== AI同声传译系统启动 ===\n")
        
        # 连接OBS
        if not test_mode:
            if not self.obs_controller.connect():
                print("❌ 无法连接OBS")
                print("💡 提示:")
                print("1. 确保OBS Studio已启动")
                print("2. 启用WebSocket服务器: 工具 → WebSocket服务器设置")
                print("3. 检查密码设置")
                print("4. 或使用测试模式: python3 obs_ai_translator_main.py --test")
                return
        else:
            print("🧪 测试模式：跳过OBS连接")
        
        # 音频处理回调
        def audio_callback(audio_data, volume):
            # 显示音量条
            bar_length = int(volume * 50)
            bar = '█' * bar_length + '░' * (50 - bar_length)
            print(f'\r音量: [{bar}] {volume:.3f}', end='', flush=True)
            
            # 实时AI处理
            if volume > 0.005:  # 只处理有声音的帧
                # 重采样到16kHz（Whisper需要）
                if audio_data.shape[0] > 0:
                    # 转为单声道
                    if len(audio_data.shape) > 1:
                        mono_audio = np.mean(audio_data, axis=1)
                    else:
                        mono_audio = audio_data
                    
                    # 发送到实时翻译器处理
                    self.translator.process_audio(mono_audio)
        
        # 开始音频捕获
        print("💡 提示: 按 'r' + Enter 重置翻译系统")
        
        # 在新线程中启动键盘监听
        import threading
        def keyboard_monitor():
            try:
                while True:
                    user_input = input().strip().lower()
                    if user_input == 'r':
                        self.translator.reset_system()
                    elif user_input == 'q':
                        print("退出系统...")
                        break
            except (EOFError, KeyboardInterrupt):
                pass
        
        threading.Thread(target=keyboard_monitor, daemon=True).start()
        
        # 开始音频捕获
        self.audio_router.start_capture(callback=audio_callback)

def main():
    """主函数"""
    import sys
    
    try:
        # 检查环境变量
        check_environment()
        print("✓ 环境变量检查通过")
        
        # 检查是否为测试模式
        test_mode = '--test' in sys.argv or '-t' in sys.argv
        
        # 从命令行获取OBS密码
        if len(sys.argv) > 1 and not sys.argv[1].startswith('--'):
            obs_password = sys.argv[1]
        else:
            # 尝试从环境变量获取
            obs_password = os.getenv('OBS_WEBSOCKET_PASSWORD', '')
            if not obs_password and not test_mode:
                try:
                    obs_password = input("请输入OBS WebSocket密码(可留空): ") or ""
                except (EOFError, KeyboardInterrupt):
                    obs_password = ""
                    print("\n使用空密码连接OBS")
        
        # 创建并启动系统
        system = AITranslatorSystem(obs_password)
        system.start(test_mode=test_mode)
        
    except ValueError as e:
        print(f"❌ 配置错误: {e}")
        print("\n请设置以下环境变量:")
        print("export OPENAI_API_KEY='your-openai-api-key'")
        sys.exit(1)
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
