#!/usr/bin/env python3
"""
Reliability and resilience utilities for the AI translator system.
Provides timeout handling, circuit breakers, resource management, and health monitoring.
"""

import time
import threading
import functools
import logging
from typing import Any, Callable, Optional, Dict, List
from queue import Queue, Empty, Full
import psutil
import os
from enum import Enum
from dataclasses import dataclass
from concurrent.futures import ThreadPoolExecutor, TimeoutError as FutureTimeoutError
import signal

class CircuitState(Enum):
    CLOSED = "closed"
    OPEN = "open"
    HALF_OPEN = "half_open"

@dataclass
class CircuitBreakerConfig:
    failure_threshold: int = 5
    recovery_timeout: int = 60
    expected_exception: type = Exception

class CircuitBreaker:
    """Circuit breaker pattern implementation for API calls"""
    
    def __init__(self, config: CircuitBreakerConfig):
        self.config = config
        self.state = CircuitState.CLOSED
        self.failure_count = 0
        self.last_failure_time = None
        self.lock = threading.RLock()
        
    def call(self, func: Callable, *args, **kwargs) -> Any:
        """Execute function with circuit breaker protection"""
        with self.lock:
            if self.state == CircuitState.OPEN:
                if self._should_attempt_reset():
                    self.state = CircuitState.HALF_OPEN
                    self.failure_count = 0
                else:
                    raise Exception("Circuit breaker is OPEN - service unavailable")
            
            try:
                result = func(*args, **kwargs)
                self._on_success()
                return result
            except self.config.expected_exception as e:
                self._on_failure()
                raise e
    
    def _on_success(self):
        """Handle successful call"""
        self.failure_count = 0
        self.state = CircuitState.CLOSED
        
    def _on_failure(self):
        """Handle failed call"""
        self.failure_count += 1
        self.last_failure_time = time.time()
        
        if self.failure_count >= self.config.failure_threshold:
            self.state = CircuitState.OPEN
            
    def _should_attempt_reset(self) -> bool:
        """Check if we should try to reset the circuit"""
        return (self.last_failure_time and 
                time.time() - self.last_failure_time >= self.config.recovery_timeout)

def timeout_handler(timeout_seconds: int):
    """Decorator to add timeout to any function call"""
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            with ThreadPoolExecutor(max_workers=1) as executor:
                future = executor.submit(func, *args, **kwargs)
                try:
                    return future.result(timeout=timeout_seconds)
                except FutureTimeoutError:
                    raise TimeoutError(f"Function {func.__name__} timed out after {timeout_seconds} seconds")
        return wrapper
    return decorator

class BoundedBuffer:
    """Thread-safe bounded buffer with intelligent overflow handling"""
    
    def __init__(self, max_size: int, overflow_strategy: str = "drop_oldest"):
        self.max_size = max_size
        self.overflow_strategy = overflow_strategy
        self.buffer = []
        self.lock = threading.RLock()
        self.total_items_added = 0
        self.total_items_dropped = 0
        
    def add(self, item: Any) -> bool:
        """Add item to buffer, handling overflow intelligently"""
        with self.lock:
            if len(self.buffer) >= self.max_size:
                if self.overflow_strategy == "drop_oldest":
                    self.buffer.pop(0)
                    self.total_items_dropped += 1
                elif self.overflow_strategy == "drop_newest":
                    # Don't add the new item
                    self.total_items_dropped += 1
                    return False
                elif self.overflow_strategy == "compress":
                    # Keep only every other item to make room
                    self.buffer = self.buffer[::2]
                    self.total_items_dropped += len(self.buffer) // 2
            
            self.buffer.append(item)
            self.total_items_added += 1
            return True
    
    def get_all_and_clear(self) -> List[Any]:
        """Get all items and clear the buffer"""
        with self.lock:
            items = self.buffer.copy()
            self.buffer.clear()
            return items
    
    def size(self) -> int:
        """Get current buffer size"""
        with self.lock:
            return len(self.buffer)
    
    def get_stats(self) -> Dict[str, int]:
        """Get buffer statistics"""
        with self.lock:
            return {
                'current_size': len(self.buffer),
                'max_size': self.max_size,
                'total_added': self.total_items_added,
                'total_dropped': self.total_items_dropped,
                'utilization': len(self.buffer) / self.max_size * 100
            }

class ResourceMonitor:
    """Monitor system resource usage"""
    
    def __init__(self, memory_threshold_mb: int = 1000, cpu_threshold: float = 90.0):
        self.memory_threshold_mb = memory_threshold_mb
        self.cpu_threshold = cpu_threshold
        self.process = psutil.Process(os.getpid())
        
    def get_memory_usage_mb(self) -> float:
        """Get current memory usage in MB"""
        return self.process.memory_info().rss / 1024 / 1024
        
    def get_cpu_usage_percent(self) -> float:
        """Get current CPU usage percentage"""
        return self.process.cpu_percent(interval=0.1)
        
    def is_memory_critical(self) -> bool:
        """Check if memory usage is critical"""
        return self.get_memory_usage_mb() > self.memory_threshold_mb
        
    def is_cpu_critical(self) -> bool:
        """Check if CPU usage is critical"""
        return self.get_cpu_usage_percent() > self.cpu_threshold
        
    def get_resource_status(self) -> Dict[str, Any]:
        """Get comprehensive resource status"""
        return {
            'memory_mb': self.get_memory_usage_mb(),
            'cpu_percent': self.get_cpu_usage_percent(),
            'memory_critical': self.is_memory_critical(),
            'cpu_critical': self.is_cpu_critical(),
            'timestamp': time.time()
        }

class HealthMonitor:
    """System health monitoring and automatic recovery"""
    
    def __init__(self, check_interval: int = 30):
        self.check_interval = check_interval
        self.is_monitoring = False
        self.monitor_thread = None
        self.health_callbacks = []
        self.resource_monitor = ResourceMonitor()
        self.last_health_check = time.time()
        
    def add_health_callback(self, callback: Callable[[Dict[str, Any]], None]):
        """Add callback to be executed on health check"""
        self.health_callbacks.append(callback)
        
    def start_monitoring(self):
        """Start health monitoring in background"""
        if self.is_monitoring:
            return
            
        self.is_monitoring = True
        self.monitor_thread = threading.Thread(target=self._monitor_loop, daemon=False)
        self.monitor_thread.start()
        
    def stop_monitoring(self):
        """Stop health monitoring"""
        self.is_monitoring = False
        if self.monitor_thread and self.monitor_thread.is_alive():
            self.monitor_thread.join(timeout=5)
            
    def _monitor_loop(self):
        """Main monitoring loop"""
        while self.is_monitoring:
            try:
                health_status = self._check_system_health()
                
                # Execute callbacks
                for callback in self.health_callbacks:
                    try:
                        callback(health_status)
                    except Exception as e:
                        logging.error(f"Health callback error: {e}")
                
                self.last_health_check = time.time()
                time.sleep(self.check_interval)
                
            except Exception as e:
                logging.error(f"Health monitor error: {e}")
                time.sleep(self.check_interval)
                
    def _check_system_health(self) -> Dict[str, Any]:
        """Perform comprehensive system health check"""
        resource_status = self.resource_monitor.get_resource_status()
        
        health_status = {
            'timestamp': time.time(),
            'resources': resource_status,
            'threads_alive': len([t for t in threading.enumerate() if t.is_alive()]),
            'threads_daemon': len([t for t in threading.enumerate() if t.is_alive() and t.daemon]),
            'uptime_seconds': time.time() - self.last_health_check
        }
        
        return health_status

class ManagedWorkerThread:
    """Properly managed worker thread with graceful shutdown"""
    
    def __init__(self, name: str, target_func: Callable, work_queue: Queue):
        self.name = name
        self.target_func = target_func
        self.work_queue = work_queue
        self.thread = None
        self.is_running = False
        self.should_stop = threading.Event()
        self.last_activity = time.time()
        
    def start(self):
        """Start the worker thread"""
        if self.is_running:
            return
            
        self.is_running = True
        self.should_stop.clear()
        self.thread = threading.Thread(target=self._worker_loop, name=self.name)
        self.thread.start()
        
    def stop(self, timeout: int = 10):
        """Stop the worker thread gracefully"""
        if not self.is_running:
            return
            
        self.should_stop.set()
        
        if self.thread and self.thread.is_alive():
            self.thread.join(timeout=timeout)
            
        self.is_running = False
        
    def _worker_loop(self):
        """Main worker loop with enhanced health monitoring"""
        failure_count = 0
        max_consecutive_failures = 3
        
        while not self.should_stop.is_set():
            try:
                # Use timeout to allow periodic checking of stop signal
                try:
                    work_item = self.work_queue.get(timeout=1.0)
                except Empty:
                    continue
                    
                self.last_activity = time.time()
                
                # Execute the work
                self.target_func(work_item)
                
                # Mark task as done
                self.work_queue.task_done()
                
                # Reset failure count on success
                if failure_count > 0:
                    failure_count = 0
                    logging.info(f"Worker {self.name} recovered from failures")
                
            except Exception as e:
                failure_count += 1
                logging.error(f"Worker {self.name} error ({failure_count}/{max_consecutive_failures}): {e}")
                
                # Check for AUHAL error
                if "-10863" in str(e):
                    logging.critical(f"Worker {self.name} encountered AUHAL Error -10863")
                    # Wait longer for AUHAL recovery
                    time.sleep(2.0)
                
                # If too many consecutive failures, pause and try to recover
                if failure_count >= max_consecutive_failures:
                    logging.warning(f"Worker {self.name} pausing for recovery after {failure_count} failures")
                    time.sleep(5.0)
                    failure_count = 0  # Reset after recovery pause
                    
                # Continue processing other items
                
    def is_healthy(self) -> bool:
        """Check if worker is healthy"""
        return (self.is_running and 
                self.thread and 
                self.thread.is_alive() and
                time.time() - self.last_activity < 60)  # 60 second timeout

class SystemRecoveryManager:
    """Manages system recovery from failures"""
    
    def __init__(self):
        self.recovery_strategies = {}
        self.recovery_history = []
        
    def register_recovery_strategy(self, error_type: str, strategy: Callable[[], bool]):
        """Register a recovery strategy for a specific error type"""
        self.recovery_strategies[error_type] = strategy
        
    def attempt_recovery(self, error_type: str, error_details: str = None) -> bool:
        """Attempt recovery using registered strategy"""
        if error_type not in self.recovery_strategies:
            return False
            
        try:
            recovery_func = self.recovery_strategies[error_type]
            success = recovery_func()
            
            self.recovery_history.append({
                'timestamp': time.time(),
                'error_type': error_type,
                'error_details': error_details,
                'success': success
            })
            
            return success
            
        except Exception as e:
            logging.error(f"Recovery attempt failed: {e}")
            return False
            
    def get_recovery_stats(self) -> Dict[str, Any]:
        """Get recovery statistics"""
        if not self.recovery_history:
            return {'total_attempts': 0, 'success_rate': 0}
            
        total = len(self.recovery_history)
        successful = sum(1 for r in self.recovery_history if r['success'])
        
        return {
            'total_attempts': total,
            'successful': successful,
            'success_rate': successful / total * 100,
            'recent_attempts': self.recovery_history[-5:]  # Last 5 attempts
        }

# Utility functions
def retry_with_exponential_backoff(max_retries: int = 3, base_delay: float = 1.0, max_delay: float = 60.0):
    """Decorator for retry with exponential backoff"""
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            last_exception = None
            
            for attempt in range(max_retries + 1):
                try:
                    return func(*args, **kwargs)
                except Exception as e:
                    last_exception = e
                    
                    if attempt == max_retries:
                        break
                        
                    delay = min(base_delay * (2 ** attempt), max_delay)
                    logging.warning(f"Attempt {attempt + 1} failed, retrying in {delay}s: {e}")
                    time.sleep(delay)
            
            raise last_exception
        return wrapper
    return decorator

def safe_execute(func: Callable, fallback_value: Any = None, log_errors: bool = True) -> Any:
    """Safely execute function with fallback value on error"""
    try:
        return func()
    except Exception as e:
        if log_errors:
            logging.error(f"Safe execution failed: {e}")
        return fallback_value