#!/usr/bin/env python3
"""
AI翻译系统启动工具 - 智能设备选择和配置
"""
import subprocess
import sys
import os

def check_audio_output():
    """检查当前音频输出设备"""
    try:
        result = subprocess.run([
            'system_profiler', 'SPAudioDataType'
        ], capture_output=True, text=True)
        
        lines = result.stdout.split('\n')
        current_output = None
        
        for i, line in enumerate(lines):
            if 'Default Output Device: Yes' in line:
                # 往前找设备名称
                for j in range(i-10, i):
                    if j >= 0 and lines[j].strip().endswith(':') and not lines[j].strip().startswith(' '):
                        current_output = lines[j].strip().rstrip(':')
                        break
                break
        
        return current_output
    except:
        return None

def warn_about_audio_setup():
    """警告用户关于音频设置"""
    current_output = check_audio_output()
    
    print("🎵 音频设备检查")
    print("="*50)
    
    if current_output:
        print(f"当前输出设备: {current_output}")
        
        if "AI同传" in current_output or "多输出" in current_output or "BlackHole" in current_output:
            print("⚠️  警告: 当前使用虚拟音频设备作为输出")
            print("   这会导致所有系统音频被翻译系统捕获")
            print("   包括视频、音乐、通知音等")
            print()
            print("🔧 建议:")
            print("1. 平时将输出切换为正常扬声器/耳机")
            print("2. 只在需要翻译视频内容时使用虚拟设备")
            print("3. 或者使用麦克风版本进行语音翻译")
            print()
            
            choice = input("是否继续启动? (y/n): ").strip().lower()
            if choice not in ['y', 'yes']:
                print("已取消启动")
                return False
        else:
            print("✅ 音频输出设备正常")
    
    return True

def choose_mode():
    """选择运行模式"""
    print()
    print("🚀 AI翻译系统启动")
    print("="*50)
    print("请选择运行模式:")
    print()
    print("1. 麦克风直接输入 (推荐) - 避免系统音频干扰")
    print("2. BlackHole音频路由 - 用于翻译视频/音频内容")
    print("3. 测试模式 (不连接OBS)")
    print("4. 退出")
    print()
    
    while True:
        try:
            choice = input("请选择 (1-4): ").strip()
            
            if choice == "1":
                return "microphone"
            elif choice == "2":
                return "blackhole"
            elif choice == "3":
                return "test"
            elif choice == "4":
                return "exit"
            else:
                print("请输入有效选择 (1-4)")
                
        except KeyboardInterrupt:
            print("\n已取消")
            return "exit"

def get_obs_password():
    """获取OBS密码"""
    password = os.getenv('OBS_WEBSOCKET_PASSWORD', '')
    
    if not password:
        print()
        print("🔗 OBS连接配置")
        print("-" * 30)
        try:
            password = input("请输入OBS WebSocket密码 (可留空): ").strip()
        except KeyboardInterrupt:
            print("\n使用空密码")
            password = ""
    
    return password

def main():
    """主启动函数"""
    print("🎯 AI同声传译系统")
    print("🔧 增强版 - 包含智能过滤和质量优化")
    print()
    
    # 检查音频设备配置
    if not warn_about_audio_setup():
        return
    
    # 选择运行模式
    mode = choose_mode()
    
    if mode == "exit":
        print("再见! 👋")
        return
    
    # 构建启动命令
    if mode == "microphone":
        script = "obs_ai_translator_mic.py"
        args = ["python3", script]
        
        if mode == "test":
            args.append("--test")
        else:
            password = get_obs_password()
            if password:
                args.append(password)
    
    elif mode == "blackhole":
        script = "obs_ai_translator_main.py"
        args = ["python3", script]
        
        password = get_obs_password()
        if password:
            args.append(password)
    
    elif mode == "test":
        script = "obs_ai_translator_mic.py"
        args = ["python3", script, "--test"]
    
    # 激活虚拟环境并启动
    print(f"\n🚀 启动 {script}...")
    print("💡 提示: 按 Ctrl+C 停止系统")
    print("💡 提示: 运行时按 'r' + Enter 重置翻译系统")
    print()
    
    try:
        # 在虚拟环境中运行
        env = os.environ.copy()
        env['PATH'] = f"./venv/bin:{env['PATH']}"
        
        subprocess.run(args, env=env, cwd=os.path.dirname(__file__))
        
    except KeyboardInterrupt:
        print("\n\n👋 AI翻译系统已停止")
    except Exception as e:
        print(f"\n❌ 启动失败: {e}")

if __name__ == "__main__":
    main()