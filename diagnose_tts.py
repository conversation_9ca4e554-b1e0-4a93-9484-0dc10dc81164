#!/usr/bin/env python3
"""
诊断TTS问题
"""

import time
import numpy as np
from obs_ai_translator_mic import MicrophoneTranslatorSystem

def test_direct_tts():
    """直接测试TTS功能"""
    print("=== 直接测试TTS功能 ===\n")
    
    system = MicrophoneTranslatorSystem(
        enable_tts=True,  # 确保TTS启用
        source_lang='zh',
        target_lang='en'
    )
    
    print(f"TTS启用状态: {system.translator.enable_tts}")
    
    # 直接调用TTS
    print("\n1. 直接调用_synthesize_and_play...")
    try:
        system.translator._synthesize_and_play("Hello, this is a test of TTS.")
        print("TTS调用完成，等待播放...")
        time.sleep(3)
    except Exception as e:
        print(f"TTS错误: {e}")
    
    system.translator.shutdown()

def test_full_pipeline():
    """测试完整流程中的TTS"""
    print("\n=== 测试完整流程TTS ===\n")
    
    system = MicrophoneTranslatorSystem(
        enable_tts=True,
        source_lang='zh',
        target_lang='en'
    )
    
    print(f"系统配置:")
    print(f"  enable_tts: {system.translator.enable_tts}")
    print(f"  audio_capture: {system.translator.audio_capture}")
    print(f"  audio_router: {system.translator.audio_router is not None}")
    
    # 手动触发一个完整流程
    print("\n模拟语音输入...")
    
    # 生成测试音频
    sample_rate = 48000
    duration = 1.5
    t = np.linspace(0, duration, int(sample_rate * duration), False)
    audio = 0.012 * np.sin(2 * np.pi * 300 * t).astype(np.float32)
    
    # 直接调用处理流程
    print("直接调用_process_speech...")
    system.translator._process_speech(audio)
    
    print("等待TTS处理...")
    time.sleep(10)
    
    system.translator.shutdown()

def check_audio_devices():
    """检查音频设备"""
    print("\n=== 检查音频设备 ===\n")
    
    import sounddevice as sd
    
    print("可用音频设备:")
    devices = sd.query_devices()
    for i, device in enumerate(devices):
        print(f"{i}: {device['name']} - {device['channels']} channels")
    
    print(f"\n默认输出设备: {sd.default.device}")

def install_audio_libs():
    """安装缺失的音频库"""
    print("\n=== 安装音频库 ===")
    import subprocess
    
    libs = [
        ("simpleaudio", "pip install simpleaudio"),
        ("pyaudio", "pip install pyaudio")
    ]
    
    for lib_name, install_cmd in libs:
        try:
            __import__(lib_name)
            print(f"✅ {lib_name} 已安装")
        except ImportError:
            print(f"❌ {lib_name} 未安装")
            response = input(f"是否安装 {lib_name}? (y/n): ")
            if response.lower() == 'y':
                subprocess.run(install_cmd.split(), check=True)
                print(f"✅ {lib_name} 安装完成")

def main():
    print("=== TTS问题诊断 ===\n")
    
    # 1. 检查音频设备
    check_audio_devices()
    
    # 2. 安装缺失的库
    install_audio_libs()
    
    # 3. 测试直接TTS
    test_direct_tts()
    
    # 4. 测试完整流程
    test_full_pipeline()

if __name__ == "__main__":
    main()