#!/usr/bin/env python3
"""
Comprehensive Regression Test Suite for AI Translation System Freezing Bug
测试目标：重现并分析"输入一两句对话之后，整个应用就卡住不work了"的问题
"""

import sys
import time
import threading
import queue
import signal
import logging
import psutil
import os
import numpy as np
from datetime import datetime
from typing import Dict, List, Any, Optional
import json
import traceback
from pathlib import Path

# Configure detailed logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s [%(levelname)s] %(name)s: %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('freeze_test.log')
    ]
)

class SystemMonitor:
    """系统资源监控器"""
    
    def __init__(self):
        self.process = psutil.Process()
        self.monitoring = False
        self.data = []
        self.lock = threading.Lock()
        
    def start_monitoring(self):
        """开始监控"""
        self.monitoring = True
        self.monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
        self.monitor_thread.start()
        logging.info("系统监控已启动")
        
    def stop_monitoring(self):
        """停止监控"""
        self.monitoring = False
        
    def _monitor_loop(self):
        """监控循环"""
        while self.monitoring:
            try:
                # 获取系统资源信息
                cpu_percent = self.process.cpu_percent()
                memory_info = self.process.memory_info()
                memory_percent = self.process.memory_percent()
                
                # 获取线程信息
                threads = self.process.threads()
                thread_count = len(threads)
                
                # 获取文件描述符信息
                try:
                    num_fds = self.process.num_fds()
                except:
                    num_fds = 0
                
                timestamp = datetime.now()
                
                data_point = {
                    'timestamp': timestamp,
                    'cpu_percent': cpu_percent,
                    'memory_rss': memory_info.rss,
                    'memory_vms': memory_info.vms,
                    'memory_percent': memory_percent,
                    'thread_count': thread_count,
                    'num_fds': num_fds,
                    'threads': [(t.id, t.user_time, t.system_time) for t in threads]
                }
                
                with self.lock:
                    self.data.append(data_point)
                    
                # 检测异常情况
                if cpu_percent > 90:
                    logging.warning(f"高CPU使用率: {cpu_percent}%")
                if memory_percent > 80:
                    logging.warning(f"高内存使用率: {memory_percent}%")
                if thread_count > 20:
                    logging.warning(f"线程数量异常: {thread_count}")
                    
            except Exception as e:
                logging.error(f"监控数据收集错误: {e}")
                
            time.sleep(0.5)  # 每0.5秒监控一次
    
    def get_report(self) -> Dict[str, Any]:
        """获取监控报告"""
        with self.lock:
            if not self.data:
                return {"error": "无监控数据"}
                
            # 计算统计信息
            cpu_values = [d['cpu_percent'] for d in self.data]
            memory_values = [d['memory_percent'] for d in self.data]
            thread_values = [d['thread_count'] for d in self.data]
            
            return {
                "duration_seconds": len(self.data) * 0.5,
                "data_points": len(self.data),
                "cpu": {
                    "min": min(cpu_values),
                    "max": max(cpu_values), 
                    "avg": sum(cpu_values) / len(cpu_values)
                },
                "memory": {
                    "min": min(memory_values),
                    "max": max(memory_values),
                    "avg": sum(memory_values) / len(memory_values)
                },
                "threads": {
                    "min": min(thread_values),
                    "max": max(thread_values),
                    "avg": sum(thread_values) / len(thread_values)
                },
                "raw_data": self.data[-10:]  # 最后10个数据点
            }

class QueueMonitor:
    """队列状态监控器"""
    
    def __init__(self, queues: Dict[str, queue.Queue]):
        self.queues = queues
        self.monitoring = False
        self.data = []
        self.lock = threading.Lock()
        
    def start_monitoring(self):
        """开始监控队列"""
        self.monitoring = True
        self.monitor_thread = threading.Thread(target=self._monitor_queues, daemon=True)
        self.monitor_thread.start()
        logging.info("队列监控已启动")
        
    def stop_monitoring(self):
        """停止监控"""
        self.monitoring = False
        
    def _monitor_queues(self):
        """监控队列状态"""
        while self.monitoring:
            try:
                timestamp = datetime.now()
                queue_states = {}
                
                for name, q in self.queues.items():
                    queue_states[name] = {
                        'qsize': q.qsize(),
                        'maxsize': q.maxsize,
                        'full': q.full(),
                        'empty': q.empty()
                    }
                
                data_point = {
                    'timestamp': timestamp,
                    'queues': queue_states
                }
                
                with self.lock:
                    self.data.append(data_point)
                    
                # 检测队列堵塞
                for name, state in queue_states.items():
                    if state['full'] and state['qsize'] >= state['maxsize']:
                        logging.warning(f"队列 {name} 已满: {state['qsize']}/{state['maxsize']}")
                        
            except Exception as e:
                logging.error(f"队列监控错误: {e}")
                
            time.sleep(1.0)  # 每秒监控一次队列
    
    def get_report(self) -> Dict[str, Any]:
        """获取队列监控报告"""
        with self.lock:
            if not self.data:
                return {"error": "无队列监控数据"}
                
            # 分析队列趋势
            report = {
                "duration_seconds": len(self.data),
                "data_points": len(self.data),
                "queue_analysis": {},
                "recent_data": self.data[-5:]  # 最近5个数据点
            }
            
            # 分析每个队列的状态
            for queue_name in self.queues.keys():
                sizes = []
                full_count = 0
                empty_count = 0
                
                for data_point in self.data:
                    if queue_name in data_point['queues']:
                        q_state = data_point['queues'][queue_name]
                        sizes.append(q_state['qsize'])
                        if q_state['full']:
                            full_count += 1
                        if q_state['empty']:
                            empty_count += 1
                
                if sizes:
                    report["queue_analysis"][queue_name] = {
                        "avg_size": sum(sizes) / len(sizes),
                        "max_size": max(sizes),
                        "min_size": min(sizes),
                        "full_percentage": (full_count / len(self.data)) * 100,
                        "empty_percentage": (empty_count / len(self.data)) * 100
                    }
            
            return report

class ThreadMonitor:
    """线程状态监控器"""
    
    def __init__(self):
        self.monitoring = False
        self.data = []
        self.lock = threading.Lock()
        
    def start_monitoring(self):
        """开始监控线程"""
        self.monitoring = True
        self.monitor_thread = threading.Thread(target=self._monitor_threads, daemon=True)
        self.monitor_thread.start()
        logging.info("线程监控已启动")
        
    def stop_monitoring(self):
        """停止监控"""
        self.monitoring = False
        
    def _monitor_threads(self):
        """监控线程状态"""
        while self.monitoring:
            try:
                timestamp = datetime.now()
                
                # 获取所有活动线程
                active_threads = threading.active_count()
                current_thread = threading.current_thread()
                
                # 获取线程详细信息
                thread_details = []
                for thread in threading.enumerate():
                    thread_info = {
                        'name': thread.name,
                        'ident': thread.ident,
                        'daemon': thread.daemon,
                        'is_alive': thread.is_alive()
                    }
                    thread_details.append(thread_info)
                
                data_point = {
                    'timestamp': timestamp,
                    'active_count': active_threads,
                    'threads': thread_details
                }
                
                with self.lock:
                    self.data.append(data_point)
                    
                # 检测线程异常
                if active_threads > 15:
                    logging.warning(f"线程数量过多: {active_threads}")
                    
                # 检测僵死线程
                for thread in thread_details:
                    if not thread['is_alive'] and not thread['daemon']:
                        logging.warning(f"检测到死线程: {thread['name']}")
                        
            except Exception as e:
                logging.error(f"线程监控错误: {e}")
                
            time.sleep(2.0)  # 每2秒监控一次线程
    
    def get_report(self) -> Dict[str, Any]:
        """获取线程监控报告"""
        with self.lock:
            if not self.data:
                return {"error": "无线程监控数据"}
                
            thread_counts = [d['active_count'] for d in self.data]
            
            # 分析线程变化趋势
            thread_names = set()
            for data_point in self.data:
                for thread in data_point['threads']:
                    thread_names.add(thread['name'])
            
            return {
                "duration_seconds": len(self.data) * 2,
                "data_points": len(self.data),
                "thread_count": {
                    "min": min(thread_counts),
                    "max": max(thread_counts),
                    "avg": sum(thread_counts) / len(thread_counts)
                },
                "unique_thread_names": list(thread_names),
                "recent_threads": self.data[-1]['threads'] if self.data else []
            }

class AudioTestGenerator:
    """音频测试数据生成器"""
    
    @staticmethod
    def generate_speech_pattern(duration_seconds: float, sample_rate: int = 48000) -> np.ndarray:
        """生成模拟语音模式的音频数据"""
        samples = int(duration_seconds * sample_rate)
        
        # 生成基础正弦波（模拟语音基频）
        t = np.linspace(0, duration_seconds, samples)
        frequency = 200 + 100 * np.sin(2 * np.pi * 2 * t)  # 变化的基频
        signal = 0.3 * np.sin(2 * np.pi * frequency * t)
        
        # 添加随机噪声（模拟语音变化）
        noise = 0.1 * np.random.normal(0, 1, samples)
        signal += noise
        
        # 添加音量包络（模拟说话的自然节奏）
        envelope = 0.5 * (1 + np.sin(2 * np.pi * 0.5 * t))
        signal *= envelope
        
        # 添加间歇性静音（模拟句子之间的停顿）
        silence_mask = np.sin(2 * np.pi * 0.3 * t) > -0.3
        signal *= silence_mask
        
        return signal.astype(np.float32)
    
    @staticmethod
    def generate_silence(duration_seconds: float, sample_rate: int = 48000) -> np.ndarray:
        """生成静音数据"""
        samples = int(duration_seconds * sample_rate)
        return np.zeros(samples, dtype=np.float32)
    
    @staticmethod
    def generate_noise(duration_seconds: float, sample_rate: int = 48000, level: float = 0.01) -> np.ndarray:
        """生成背景噪声"""
        samples = int(duration_seconds * sample_rate)
        return level * np.random.normal(0, 1, samples).astype(np.float32)

class FreezeDetector:
    """系统冻结检测器"""
    
    def __init__(self, timeout_seconds: int = 30):
        self.timeout_seconds = timeout_seconds
        self.last_activity = time.time()
        self.monitoring = False
        self.freeze_detected = False
        
    def update_activity(self):
        """更新最后活动时间"""
        self.last_activity = time.time()
        
    def start_monitoring(self):
        """开始监控系统冻结"""
        self.monitoring = True
        self.monitor_thread = threading.Thread(target=self._monitor_freeze, daemon=True)
        self.monitor_thread.start()
        logging.info(f"冻结检测器启动，超时阈值: {self.timeout_seconds}秒")
        
    def stop_monitoring(self):
        """停止监控"""
        self.monitoring = False
        
    def _monitor_freeze(self):
        """监控系统冻结"""
        while self.monitoring:
            try:
                current_time = time.time()
                inactive_duration = current_time - self.last_activity
                
                if inactive_duration > self.timeout_seconds:
                    if not self.freeze_detected:
                        self.freeze_detected = True
                        logging.error(f"检测到系统可能冻结！无活动时间: {inactive_duration:.1f}秒")
                        
                        # 记录当前线程状态
                        self._log_freeze_state()
                
                time.sleep(1.0)
                
            except Exception as e:
                logging.error(f"冻结检测错误: {e}")
                
    def _log_freeze_state(self):
        """记录冻结时的系统状态"""
        try:
            logging.error("=== 系统冻结状态分析 ===")
            
            # 线程信息
            active_threads = threading.active_count()
            logging.error(f"活动线程数: {active_threads}")
            
            for thread in threading.enumerate():
                logging.error(f"线程: {thread.name}, 存活: {thread.is_alive()}, 守护: {thread.daemon}")
                
            # 进程信息
            process = psutil.Process()
            logging.error(f"CPU使用率: {process.cpu_percent()}%")
            logging.error(f"内存使用率: {process.memory_percent()}%")
            
            # 获取堆栈跟踪
            import faulthandler
            logging.error("=== 线程堆栈跟踪 ===")
            faulthandler.dump_traceback(file=sys.stderr)
            
        except Exception as e:
            logging.error(f"记录冻结状态错误: {e}")

class ComprehensiveFreezeTest:
    """综合冻结测试套件"""
    
    def __init__(self):
        self.system_monitor = SystemMonitor()
        self.thread_monitor = ThreadMonitor()
        self.freeze_detector = FreezeDetector(timeout_seconds=45)
        self.queue_monitor = None
        self.translator_system = None
        self.test_results = []
        
    def setup_test_environment(self):
        """设置测试环境"""
        logging.info("=== 设置测试环境 ===")
        
        # 检查环境变量
        if not os.getenv('OPENAI_API_KEY'):
            logging.error("缺少OPENAI_API_KEY环境变量")
            return False
            
        # 启动监控
        self.system_monitor.start_monitoring()
        self.thread_monitor.start_monitoring()
        self.freeze_detector.start_monitoring()
        
        return True
    
    def test_individual_components(self):
        """测试各个组件的独立功能"""
        logging.info("=== 测试1: 组件隔离测试 ===")
        
        test_result = {
            "test_name": "component_isolation",
            "start_time": datetime.now(),
            "status": "running",
            "details": {}
        }
        
        try:
            # 测试1: VAD组件
            logging.info("测试VAD组件...")
            self._test_vad_component()
            test_result["details"]["vad"] = "passed"
            
            # 测试2: ASR组件
            logging.info("测试ASR组件...")
            self._test_asr_component()
            test_result["details"]["asr"] = "passed"
            
            # 测试3: 队列系统
            logging.info("测试队列系统...")
            self._test_queue_system()
            test_result["details"]["queue"] = "passed"
            
            test_result["status"] = "passed"
            
        except Exception as e:
            logging.error(f"组件测试失败: {e}")
            test_result["status"] = "failed"
            test_result["error"] = str(e)
            test_result["traceback"] = traceback.format_exc()
        
        test_result["end_time"] = datetime.now()
        test_result["duration"] = (test_result["end_time"] - test_result["start_time"]).total_seconds()
        self.test_results.append(test_result)
        
    def _test_vad_component(self):
        """测试VAD组件"""
        from realtime_translator import RealtimeTranslator
        
        # 创建简化的翻译器实例
        translator = RealtimeTranslator(enable_tts=False)
        
        # 生成测试音频
        speech_audio = AudioTestGenerator.generate_speech_pattern(2.0)
        silence_audio = AudioTestGenerator.generate_silence(1.0)
        noise_audio = AudioTestGenerator.generate_noise(1.0)
        
        # 测试语音检测
        logging.info("测试语音信号处理...")
        for i in range(10):
            chunk = speech_audio[i*1024:(i+1)*1024]
            if len(chunk) > 0:
                translator.process_audio(chunk)
                self.freeze_detector.update_activity()
        
        # 测试静音处理
        logging.info("测试静音信号处理...")
        for i in range(5):
            chunk = silence_audio[i*1024:(i+1)*1024]
            if len(chunk) > 0:
                translator.process_audio(chunk)
                self.freeze_detector.update_activity()
        
        time.sleep(2)  # 等待处理完成
        
    def _test_asr_component(self):
        """测试ASR组件单独处理"""
        import whisper
        
        # 加载轻量级模型进行测试
        model = whisper.load_model("tiny")
        
        # 生成测试音频
        test_audio = AudioTestGenerator.generate_speech_pattern(3.0, sample_rate=16000)
        
        # 测试识别
        logging.info("测试Whisper ASR...")
        result = model.transcribe(test_audio, language='zh', fp16=False)
        logging.info(f"ASR测试结果: {result.get('text', 'N/A')}")
        
        self.freeze_detector.update_activity()
        
    def _test_queue_system(self):
        """测试队列系统"""
        from queue import Queue
        import threading
        
        # 创建测试队列
        test_queues = {
            'recognition': Queue(maxsize=5),
            'translation': Queue(maxsize=5),
            'tts': Queue(maxsize=5)
        }
        
        # 启动队列监控
        queue_monitor = QueueMonitor(test_queues)
        queue_monitor.start_monitoring()
        
        def producer(q, data_list, delay=0.1):
            """生产者线程"""
            for item in data_list:
                try:
                    q.put(item, timeout=1.0)
                    time.sleep(delay)
                    self.freeze_detector.update_activity()
                except:
                    logging.warning("队列生产超时")
                    break
        
        def consumer(q, name, delay=0.2):
            """消费者线程"""
            processed = 0
            while processed < 10:
                try:
                    item = q.get(timeout=2.0)
                    logging.debug(f"消费者 {name} 处理: {item}")
                    time.sleep(delay)  # 模拟处理时间
                    q.task_done()
                    processed += 1
                    self.freeze_detector.update_activity()
                except:
                    logging.warning(f"消费者 {name} 获取数据超时")
                    break
        
        # 启动生产者和消费者
        test_data = [f"item_{i}" for i in range(10)]
        
        threads = []
        threads.append(threading.Thread(target=producer, args=(test_queues['recognition'], test_data, 0.1)))
        threads.append(threading.Thread(target=consumer, args=(test_queues['recognition'], "ASR", 0.3)))
        
        for thread in threads:
            thread.start()
        
        for thread in threads:
            thread.join(timeout=10)
        
        queue_monitor.stop_monitoring()
        
    def test_consecutive_inputs(self):
        """测试连续多次输入（重现冻结bug的核心测试）"""
        logging.info("=== 测试2: 连续输入测试 ===")
        
        test_result = {
            "test_name": "consecutive_inputs",
            "start_time": datetime.now(),
            "status": "running",
            "input_count": 0,
            "successful_processes": 0,
            "failed_processes": 0,
            "freeze_detected": False
        }
        
        try:
            # 导入系统
            from obs_ai_translator_mic import MicrophoneTranslatorSystem
            
            # 创建系统实例（测试模式，禁用TTS）
            system = MicrophoneTranslatorSystem(
                obs_password="",
                enable_tts=False  # 禁用TTS减少复杂性
            )
            
            # 获取队列引用进行监控
            translator_queues = {
                'recognition': system.translator.recognition_queue,
                'translation': system.translator.translation_queue,
                'tts': system.translator.tts_queue
            }
            
            # 启动队列监控
            self.queue_monitor = QueueMonitor(translator_queues)
            self.queue_monitor.start_monitoring()
            
            # 模拟连续语音输入
            logging.info("开始模拟连续语音输入...")
            
            for input_round in range(1, 6):  # 测试5轮输入
                logging.info(f"--- 第 {input_round} 轮输入 ---")
                test_result["input_count"] = input_round
                
                try:
                    # 生成不同长度的语音数据
                    if input_round % 2 == 1:
                        # 奇数轮：短语音
                        speech_data = AudioTestGenerator.generate_speech_pattern(2.0)
                        logging.info(f"生成短语音数据: 2秒")
                    else:
                        # 偶数轮：长语音
                        speech_data = AudioTestGenerator.generate_speech_pattern(4.0)
                        logging.info(f"生成长语音数据: 4秒")
                    
                    # 分块输入音频数据
                    chunk_size = 1024
                    chunks = [speech_data[i:i+chunk_size] for i in range(0, len(speech_data), chunk_size)]
                    
                    logging.info(f"开始输入 {len(chunks)} 个音频块...")
                    
                    for i, chunk in enumerate(chunks):
                        if len(chunk) > 0:
                            system.translator.process_audio(chunk)
                            self.freeze_detector.update_activity()
                            
                            # 检查是否冻结
                            if self.freeze_detector.freeze_detected:
                                test_result["freeze_detected"] = True
                                test_result["freeze_at_round"] = input_round
                                test_result["freeze_at_chunk"] = i
                                logging.error(f"在第 {input_round} 轮第 {i} 块检测到冻结！")
                                break
                        
                        time.sleep(0.01)  # 模拟实时音频流
                    
                    if test_result.get("freeze_detected"):
                        break
                    
                    # 添加句子间的停顿
                    logging.info("添加句子间停顿...")
                    silence_data = AudioTestGenerator.generate_silence(1.0)
                    silence_chunks = [silence_data[i:i+chunk_size] for i in range(0, len(silence_data), chunk_size)]
                    
                    for chunk in silence_chunks:
                        if len(chunk) > 0:
                            system.translator.process_audio(chunk)
                            self.freeze_detector.update_activity()
                        time.sleep(0.01)
                    
                    # 等待处理完成
                    logging.info("等待当前轮次处理完成...")
                    time.sleep(3)  # 给系统时间处理
                    
                    test_result["successful_processes"] = input_round
                    logging.info(f"第 {input_round} 轮输入完成")
                    
                except Exception as e:
                    logging.error(f"第 {input_round} 轮输入失败: {e}")
                    test_result["failed_processes"] += 1
                    # 不中断测试，继续下一轮
                    continue
            
            # 测试完成
            if not test_result.get("freeze_detected"):
                test_result["status"] = "passed"
                logging.info("连续输入测试通过，未检测到冻结")
            else:
                test_result["status"] = "failed_freeze"
                logging.error("连续输入测试失败：检测到系统冻结")
                
        except Exception as e:
            logging.error(f"连续输入测试异常: {e}")
            test_result["status"] = "failed_exception"
            test_result["error"] = str(e)
            test_result["traceback"] = traceback.format_exc()
        
        test_result["end_time"] = datetime.now()
        test_result["duration"] = (test_result["end_time"] - test_result["start_time"]).total_seconds()
        self.test_results.append(test_result)
        
    def test_stress_scenarios(self):
        """测试压力场景"""
        logging.info("=== 测试3: 压力场景测试 ===")
        
        test_result = {
            "test_name": "stress_scenarios",
            "start_time": datetime.now(),
            "status": "running",
            "scenarios": {}
        }
        
        try:
            from obs_ai_translator_mic import MicrophoneTranslatorSystem
            
            system = MicrophoneTranslatorSystem(
                obs_password="",
                enable_tts=False
            )
            
            # 场景1: 快速连续输入
            logging.info("场景1: 快速连续输入")
            try:
                for i in range(10):
                    chunk = AudioTestGenerator.generate_speech_pattern(0.5)
                    system.translator.process_audio(chunk[:1024])
                    self.freeze_detector.update_activity()
                    time.sleep(0.05)  # 很短的间隔
                
                test_result["scenarios"]["rapid_input"] = "passed"
            except Exception as e:
                test_result["scenarios"]["rapid_input"] = f"failed: {e}"
            
            time.sleep(2)  # 恢复间隔
            
            # 场景2: 长时间连续语音
            logging.info("场景2: 长时间连续语音")
            try:
                long_speech = AudioTestGenerator.generate_speech_pattern(10.0)
                chunk_size = 2048
                chunks = [long_speech[i:i+chunk_size] for i in range(0, len(long_speech), chunk_size)]
                
                for chunk in chunks:
                    if len(chunk) > 0:
                        system.translator.process_audio(chunk)
                        self.freeze_detector.update_activity()
                        time.sleep(0.02)
                
                test_result["scenarios"]["long_speech"] = "passed"
            except Exception as e:
                test_result["scenarios"]["long_speech"] = f"failed: {e}"
            
            time.sleep(3)  # 恢复间隔
            
            # 场景3: 噪声干扰测试
            logging.info("场景3: 噪声干扰测试")
            try:
                for i in range(5):
                    # 交替语音和噪声
                    if i % 2 == 0:
                        chunk = AudioTestGenerator.generate_speech_pattern(1.0)
                    else:
                        chunk = AudioTestGenerator.generate_noise(1.0, level=0.1)
                    
                    system.translator.process_audio(chunk[:2048])
                    self.freeze_detector.update_activity()
                    time.sleep(0.1)
                
                test_result["scenarios"]["noise_interference"] = "passed"
            except Exception as e:
                test_result["scenarios"]["noise_interference"] = f"failed: {e}"
            
            test_result["status"] = "passed"
            
        except Exception as e:
            logging.error(f"压力测试失败: {e}")
            test_result["status"] = "failed"
            test_result["error"] = str(e)
            test_result["traceback"] = traceback.format_exc()
        
        test_result["end_time"] = datetime.now()
        test_result["duration"] = (test_result["end_time"] - test_result["start_time"]).total_seconds()
        self.test_results.append(test_result)
    
    def test_reset_functionality(self):
        """测试重置功能"""
        logging.info("=== 测试4: 重置功能测试 ===")
        
        test_result = {
            "test_name": "reset_functionality",
            "start_time": datetime.now(),
            "status": "running",
            "reset_attempts": 0
        }
        
        try:
            from obs_ai_translator_mic import MicrophoneTranslatorSystem
            
            system = MicrophoneTranslatorSystem(
                obs_password="",
                enable_tts=False
            )
            
            # 首先产生一些处理负载
            logging.info("产生处理负载...")
            speech_data = AudioTestGenerator.generate_speech_pattern(3.0)
            chunks = [speech_data[i:i+1024] for i in range(0, len(speech_data), 1024)]
            
            for chunk in chunks[:10]:  # 只处理前10块
                if len(chunk) > 0:
                    system.translator.process_audio(chunk)
                    self.freeze_detector.update_activity()
                time.sleep(0.02)
            
            # 等待队列有数据
            time.sleep(2)
            
            # 测试重置功能
            for reset_round in range(3):
                test_result["reset_attempts"] = reset_round + 1
                logging.info(f"执行第 {reset_round + 1} 次重置...")
                
                # 记录重置前状态
                before_reset = {
                    'recognition_queue': system.translator.recognition_queue.qsize(),
                    'translation_queue': system.translator.translation_queue.qsize(),
                    'tts_queue': system.translator.tts_queue.qsize()
                }
                logging.info(f"重置前队列状态: {before_reset}")
                
                # 执行重置
                system.translator.reset_system()
                self.freeze_detector.update_activity()
                
                # 等待重置完成
                time.sleep(1)
                
                # 记录重置后状态
                after_reset = {
                    'recognition_queue': system.translator.recognition_queue.qsize(),
                    'translation_queue': system.translator.translation_queue.qsize(),
                    'tts_queue': system.translator.tts_queue.qsize()
                }
                logging.info(f"重置后队列状态: {after_reset}")
                
                # 验证重置效果
                if all(size == 0 for size in after_reset.values()):
                    logging.info(f"第 {reset_round + 1} 次重置成功")
                else:
                    logging.warning(f"第 {reset_round + 1} 次重置可能不完全")
                
                # 重置后再次输入数据测试
                test_chunk = AudioTestGenerator.generate_speech_pattern(1.0)[:1024]
                system.translator.process_audio(test_chunk)
                self.freeze_detector.update_activity()
                
                time.sleep(1)
            
            test_result["status"] = "passed"
            
        except Exception as e:
            logging.error(f"重置功能测试失败: {e}")
            test_result["status"] = "failed"
            test_result["error"] = str(e)
            test_result["traceback"] = traceback.format_exc()
        
        test_result["end_time"] = datetime.now()
        test_result["duration"] = (test_result["end_time"] - test_result["start_time"]).total_seconds()
        self.test_results.append(test_result)
    
    def run_comprehensive_test(self):
        """运行综合测试"""
        logging.info("======= 开始AI翻译系统冻结回归测试 =======")
        
        if not self.setup_test_environment():
            logging.error("测试环境设置失败")
            return False
        
        try:
            # 测试1: 组件隔离测试
            self.test_individual_components()
            
            # 测试2: 连续输入测试（重现bug的关键测试）
            self.test_consecutive_inputs()
            
            # 测试3: 压力场景测试
            self.test_stress_scenarios()
            
            # 测试4: 重置功能测试
            self.test_reset_functionality()
            
        except Exception as e:
            logging.error(f"综合测试异常: {e}")
            traceback.print_exc()
        
        finally:
            # 停止监控
            self.system_monitor.stop_monitoring()
            self.thread_monitor.stop_monitoring()
            self.freeze_detector.stop_monitoring()
            if self.queue_monitor:
                self.queue_monitor.stop_monitoring()
        
        # 生成测试报告
        self.generate_test_report()
        
        return True
    
    def generate_test_report(self):
        """生成综合测试报告"""
        logging.info("=== 生成测试报告 ===")
        
        report = {
            "test_session": {
                "start_time": min(tr["start_time"] for tr in self.test_results) if self.test_results else datetime.now(),
                "end_time": max(tr.get("end_time", datetime.now()) for tr in self.test_results) if self.test_results else datetime.now(),
                "total_duration_seconds": sum(tr.get("duration", 0) for tr in self.test_results),
                "python_version": sys.version,
                "platform": sys.platform
            },
            "test_results": self.test_results,
            "system_monitoring": self.system_monitor.get_report(),
            "thread_monitoring": self.thread_monitor.get_report(),
            "queue_monitoring": self.queue_monitor.get_report() if self.queue_monitor else {"error": "队列监控未启动"},
            "freeze_detection": {
                "freeze_detected": self.freeze_detector.freeze_detected,
                "timeout_threshold": self.freeze_detector.timeout_seconds
            },
            "summary": self._generate_summary()
        }
        
        # 保存报告到文件
        report_file = f"freeze_test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        try:
            # 序列化datetime对象
            def json_serializer(obj):
                if isinstance(obj, datetime):
                    return obj.isoformat()
                raise TypeError(f"Object of type {type(obj)} is not JSON serializable")
            
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(report, f, indent=2, ensure_ascii=False, default=json_serializer)
            
            logging.info(f"测试报告已保存到: {report_file}")
            
        except Exception as e:
            logging.error(f"保存测试报告失败: {e}")
        
        # 打印摘要到控制台
        self._print_report_summary(report)
        
        return report
    
    def _generate_summary(self):
        """生成测试摘要"""
        total_tests = len(self.test_results)
        passed_tests = sum(1 for tr in self.test_results if tr.get("status") == "passed")
        failed_tests = total_tests - passed_tests
        
        freeze_detected = any(
            tr.get("freeze_detected") or tr.get("status") == "failed_freeze" 
            for tr in self.test_results
        )
        
        return {
            "total_tests": total_tests,
            "passed_tests": passed_tests,
            "failed_tests": failed_tests,
            "pass_rate": (passed_tests / total_tests * 100) if total_tests > 0 else 0,
            "freeze_bug_reproduced": freeze_detected,
            "critical_findings": self._extract_critical_findings()
        }
    
    def _extract_critical_findings(self):
        """提取关键发现"""
        findings = []
        
        # 检查冻结相关发现
        for test in self.test_results:
            if test.get("freeze_detected"):
                findings.append(f"冻结检测: {test['test_name']} 在第 {test.get('freeze_at_round', 'N/A')} 轮检测到系统冻结")
            
            if test.get("status") == "failed_freeze":
                findings.append(f"测试失败: {test['test_name']} 因系统冻结而失败")
            
            if test.get("status") == "failed_exception":
                findings.append(f"异常失败: {test['test_name']} - {test.get('error', 'N/A')}")
        
        # 检查系统资源异常
        system_report = self.system_monitor.get_report()
        if isinstance(system_report, dict) and "cpu" in system_report:
            if system_report["cpu"]["max"] > 90:
                findings.append(f"高CPU使用: 最高达到 {system_report['cpu']['max']:.1f}%")
            if system_report["memory"]["max"] > 80:
                findings.append(f"高内存使用: 最高达到 {system_report['memory']['max']:.1f}%")
        
        # 检查线程异常
        thread_report = self.thread_monitor.get_report()
        if isinstance(thread_report, dict) and "thread_count" in thread_report:
            if thread_report["thread_count"]["max"] > 15:
                findings.append(f"线程数量异常: 最多 {thread_report['thread_count']['max']} 个线程")
        
        return findings
    
    def _print_report_summary(self, report):
        """打印报告摘要到控制台"""
        print("\n" + "="*60)
        print("AI翻译系统冻结回归测试 - 测试报告摘要")
        print("="*60)
        
        summary = report["summary"]
        print(f"测试总数: {summary['total_tests']}")
        print(f"通过测试: {summary['passed_tests']}")
        print(f"失败测试: {summary['failed_tests']}")
        print(f"通过率: {summary['pass_rate']:.1f}%")
        print(f"冻结Bug重现: {'是' if summary['freeze_bug_reproduced'] else '否'}")
        
        if summary['critical_findings']:
            print("\n关键发现:")
            for finding in summary['critical_findings']:
                print(f"  • {finding}")
        
        print(f"\n详细报告已保存到文件中")
        print("="*60)

def main():
    """主函数"""
    
    # 设置信号处理器，确保清理资源
    def signal_handler(sig, frame):
        logging.info("接收到中断信号，正在清理...")
        sys.exit(0)
    
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    print("AI翻译系统冻结回归测试")
    print("目标：重现并分析'输入一两句对话之后，整个应用就卡住不work了'的问题")
    print("开始测试...")
    
    # 创建并运行测试套件
    test_suite = ComprehensiveFreezeTest()
    
    try:
        success = test_suite.run_comprehensive_test()
        
        if success:
            print("\n测试套件执行完成！")
            
            # 检查是否重现了冻结bug
            freeze_reproduced = any(
                tr.get("freeze_detected") or tr.get("status") == "failed_freeze"
                for tr in test_suite.test_results
            )
            
            if freeze_reproduced:
                print("⚠️  成功重现了系统冻结问题！")
                print("请查看详细日志和测试报告进行根因分析。")
            else:
                print("✅ 在当前测试条件下未能重现冻结问题。")
                print("可能需要调整测试参数或测试更多场景。")
        else:
            print("❌ 测试套件执行失败！")
            return 1
            
    except KeyboardInterrupt:
        print("\n测试被用户中断")
        return 1
    except Exception as e:
        print(f"测试执行异常: {e}")
        traceback.print_exc()
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())