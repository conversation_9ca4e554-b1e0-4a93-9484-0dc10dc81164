{"vad": {"status": "passed", "results": [{"type": "silence", "is_speech": false, "detection_time": 4.76837158203125e-06}, {"type": "noise", "is_speech": true, "detection_time": 4.0531158447265625e-06}, {"type": "speech", "is_speech": true, "detection_time": 2.86102294921875e-06}], "config": {"sample_rate": 16000, "frame_duration": 30, "frame_size": 480}}, "whisper_asr": {"status": "passed", "load_time": 0.31072115898132324, "transcribe_time": 0.21471214294433594, "result": "", "no_speech_prob": 0}, "openai_translation": {"status": "passed", "test_count": 3, "average_time": 1.3474091688791912, "results": [{"original": "你好，这是一个测试句子。", "translated": "Hello, this is a test sentence.", "time": 1.7482459545135498}, {"original": "今天天气很好。", "translated": "The weather is very nice today.", "time": 1.1670358180999756}, {"original": "我正在测试翻译功能。", "translated": "I am testing the translation function.", "time": 1.1269457340240479}]}, "edge_tts": {"status": "passed", "test_count": 3, "average_time": 1.3337311744689941, "total_audio_bytes": 49104, "results": [{"text": "Hello, this is a test.", "audio_size": 16416, "time": 1.4319179058074951}, {"text": "Today is a beautiful day.", "audio_size": 14256, "time": 1.2271687984466553}, {"text": "Testing text-to-speech functionality.", "audio_size": 18432, "time": 1.342106819152832}]}, "threading": {"status": "passed", "produced": 10, "consumed": 10, "errors": [], "execution_time": 2.038386106491089, "queue_empty": true}, "memory": {"status": "failed", "error": "No module named 'psutil'"}}