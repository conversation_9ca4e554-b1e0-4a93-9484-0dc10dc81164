#!/usr/bin/env python3
"""
运行修复后的AI翻译系统
"""
import sys
import os
from obs_ai_translator_main import AITranslatorSystem

def main():
    print("=== 修复版 AI同声传译系统 ===")
    print("主要修复:")
    print("1. ✅ 简化事件循环处理，避免冲突")
    print("2. ✅ 改进音频播放，避免AUHAL错误")
    print("3. ✅ 增强工作线程错误处理")
    print("4. ✅ 添加超时保护机制")
    print()
    
    # 检查环境变量
    if not os.getenv('OPENAI_API_KEY'):
        print("❌ 请先设置 OPENAI_API_KEY 环境变量")
        print("export OPENAI_API_KEY='your-api-key'")
        sys.exit(1)
    
    try:
        # 检查是否为测试模式
        test_mode = '--test' in sys.argv or '-t' in sys.argv
        
        # 获取OBS密码
        obs_password = ""
        if not test_mode:
            if len(sys.argv) > 1 and not sys.argv[1].startswith('--'):
                obs_password = sys.argv[1]
            else:
                obs_password = os.getenv('OBS_WEBSOCKET_PASSWORD', '')
                if not obs_password:
                    try:
                        obs_password = input("请输入OBS WebSocket密码(可留空): ") or ""
                    except (EOFError, KeyboardInterrupt):
                        obs_password = ""
                        print("\n使用空密码连接OBS")
        
        # 创建系统实例
        system = AITranslatorSystem(obs_password)
        
        print("\n💡 使用提示:")
        print("- 按 'r' + Enter: 重置系统")
        print("- 按 'q' + Enter: 退出系统")
        print("- 如果遇到卡死，尝试重置系统")
        print()
        
        # 启动系统
        system.start(test_mode=test_mode)
        
    except KeyboardInterrupt:
        print("\n用户中断，正在退出...")
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
