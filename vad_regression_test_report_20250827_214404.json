{"test_execution_summary": {"timestamp": "2025-08-27T21:44:04.936589", "total_duration_seconds": 97.565801, "total_tests": 14, "passed_tests": 9, "failed_tests": 5, "overall_pass_rate": 64.28571428571429}, "test_results_by_category": {"basic_functionality": {"total_tests": 3, "passed_tests": 1, "failed_tests": 2, "pass_rate": 33.33333333333333, "test_details": {"vad_initialization": {"passed": true, "timestamp": "2025-08-27T21:42:27.927486", "details": {"has_audio_buffer": true, "has_speech_threshold": true, "has_silence_timeout": true, "has_worker_thread": true, "worker_thread_alive": true, "speech_threshold_valid": true, "silence_timeout_valid": true}}, "speech_detection_start": {"passed": false, "timestamp": "2025-08-27T21:42:29.595909", "details": {"speech_detected": false, "is_speaking_state": false, "audio_buffer_size": 48000, "speech_segments_size": 0}}, "audio_processing_pipeline": {"passed": false, "timestamp": "2025-08-27T21:42:32.011132", "details": {"error": "'Mock' object is not subscriptable"}}}}, "edge_cases": {"total_tests": 5, "passed_tests": 3, "failed_tests": 2, "pass_rate": 60.0, "test_details": {"edge_noise_handling": {"passed": false, "timestamp": "2025-08-27T21:42:32.995169", "details": {"below_threshold_no_trigger": true, "above_threshold_trigger": false, "threshold_value": 0.015, "noise_stability": true}}, "long_speech_segments": {"passed": true, "timestamp": "2025-08-27T21:42:34.046359", "details": {"speech_ended": true, "max_duration_enforced": true, "expected_max_duration": 15.0, "actual_duration": 0.00015997886657714844, "queue_has_work": false}}, "rapid_speech_silence_transitions": {"passed": false, "timestamp": "2025-08-27T21:42:36.739769", "details": {"transitions_detected": 0, "final_speaking_state": false, "queue_items": 0, "stability_maintained": false}}, "invalid_audio_input_handling": {"passed": true, "timestamp": "2025-08-27T21:42:38.074356", "details": {"cases_handled_gracefully": 5, "total_cases": 5, "grace_rate": 1.0, "test_results": {"empty_array": true, "nan_values": true, "inf_values": true, "wrong_dtype": true, "multi_dimensional": true}}}, "memory_usage_under_stress": {"passed": true, "timestamp": "2025-08-27T21:42:41.726380", "details": {"initial_memory_mb": 0.44722843170166016, "final_memory_mb": 1.9823999404907227, "max_memory_mb": 2.019059181213379, "memory_growth_mb": 1.5351715087890625, "memory_growth_acceptable": true, "stress_iterations": 30}}}}, "integration": {"total_tests": 3, "passed_tests": 2, "failed_tests": 1, "pass_rate": 66.66666666666666, "test_details": {"full_pipeline_integration": {"passed": false, "timestamp": "2025-08-27T21:42:43.582840", "details": {"pipeline_execution_time": 0.34899115562438965, "speech_detected": false, "queue_processed": true, "worker_thread_alive": true, "final_queue_size": 0}}, "multithreading_behavior": {"passed": true, "timestamp": "2025-08-27T21:42:47.985273", "details": {"threads_launched": 3, "threads_completed": 3, "threads_with_errors": 0, "worker_thread_alive": true, "final_queue_size": 0, "thread_results": {"thread_2": "completed", "thread_1": "completed", "thread_0": "completed"}}}, "resource_cleanup_and_shutdown": {"passed": true, "timestamp": "2025-08-27T21:42:51.838264", "details": {"pre_shutdown": {"worker_thread_alive": true, "queue_size": 0, "audio_buffer_size": 48000, "is_running": true}, "post_shutdown": {"worker_thread_alive": false, "is_running": false, "shutdown_duration": 0.8787178993225098, "shutdown_timeout_acceptable": true}, "resource_reuse_ok": true, "clean_shutdown": true}}}}, "performance": {"total_tests": 2, "passed_tests": 2, "failed_tests": 0, "pass_rate": 100.0, "test_details": {"continuous_operation": {"passed": true, "timestamp": "2025-08-27T21:43:52.376090", "details": {"test_duration": 60.017882108688354, "operations_completed": 566, "operations_per_second": 9.430522706133015, "errors_encountered": 0, "error_rate": 0.0, "speech_cycles": 29, "worker_thread_alive": true, "final_queue_size": 0}}, "memory_leak_detection": {"passed": true, "timestamp": "2025-08-27T21:44:02.836679", "details": {"baseline_memory_mb": 0.44608306884765625, "final_memory_mb": 2.1669130325317383, "total_growth_mb": 1.720829963684082, "trend_growth_mb": 0.43217039108276367, "processing_cycles": 30, "memory_samples_count": 8, "no_significant_leak": true}}}}, "stability": {"total_tests": 1, "passed_tests": 1, "failed_tests": 0, "pass_rate": 100.0, "test_details": {"thread_safety_validation": {"passed": true, "timestamp": "2025-08-27T21:44:04.096016", "details": {"threads_used": 5, "operations_per_thread": 20, "total_operations": 100, "successful_operations": 100, "failed_operations": 0, "success_rate": 1.0, "execution_time": 0.18476319313049316, "final_state_healthy": true, "thread_results": {"2": {"success": 20, "failures": 0}, "4": {"success": 20, "failures": 0}, "1": {"success": 20, "failures": 0}, "3": {"success": 20, "failures": 0}, "0": {"success": 20, "failures": 0}}}}}}}, "performance_metrics": {"memory_usage": [2.019059181213379, 2.1669130325317383], "response_times": [0.34899115562438965, 60.017882108688354], "thread_counts": [5], "error_counts": 0}, "issues_discovered": [{"category": "basic_functionality", "test_name": "speech_detection_start", "issue_type": "test_failure", "description": "Test failed", "severity": "high", "timestamp": "2025-08-27T21:42:29.595909"}, {"category": "basic_functionality", "test_name": "audio_processing_pipeline", "issue_type": "test_failure", "description": "'Mock' object is not subscriptable", "severity": "high", "timestamp": "2025-08-27T21:42:32.011132"}, {"category": "edge_cases", "test_name": "edge_noise_handling", "issue_type": "test_failure", "description": "Test failed", "severity": "low", "timestamp": "2025-08-27T21:42:32.995169"}, {"category": "edge_cases", "test_name": "rapid_speech_silence_transitions", "issue_type": "test_failure", "description": "Test failed", "severity": "low", "timestamp": "2025-08-27T21:42:36.739769"}, {"category": "integration", "test_name": "full_pipeline_integration", "issue_type": "test_failure", "description": "Test failed", "severity": "low", "timestamp": "2025-08-27T21:42:43.582840"}], "recommendations": [{"category": "general", "priority": "critical", "title": "Low Overall Test Pass Rate", "description": "Overall pass rate is 64.3%, which is below acceptable threshold of 80%", "action": "Address failing tests before production deployment"}, {"category": "basic_functionality", "priority": "critical", "title": "Basic Functionality Issues", "description": "Core VAD functionality tests are failing", "action": "Fix basic functionality issues immediately - system is not ready for production"}, {"category": "performance", "priority": "medium", "title": "Long Response Times", "description": "Peak response time is 60.0s", "action": "Optimize response times for better user experience"}], "production_readiness": {"readiness_level": "not_ready", "status_message": "System is NOT ready for production deployment", "critical_issues_count": 2, "high_priority_issues_count": 0, "blocking_issues": [{"category": "general", "priority": "critical", "title": "Low Overall Test Pass Rate", "description": "Overall pass rate is 64.3%, which is below acceptable threshold of 80%", "action": "Address failing tests before production deployment"}, {"category": "basic_functionality", "priority": "critical", "title": "Basic Functionality Issues", "description": "Core VAD functionality tests are failing", "action": "Fix basic functionality issues immediately - system is not ready for production"}]}}