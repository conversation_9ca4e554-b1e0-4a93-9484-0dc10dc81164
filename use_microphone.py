#!/usr/bin/env python3
"""
使用麦克风而不是BlackHole的翻译系统
"""
import os
import sys
import sounddevice as sd

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from obs_ai_translator_main import AITranslatorSystem

def find_microphone_device():
    """查找麦克风设备"""
    devices = sd.query_devices()
    
    print("=== 可用音频输入设备 ===")
    microphone_devices = []
    
    for i, device in enumerate(devices):
        if device['max_input_channels'] > 0:
            print(f"{i:2d}: {device['name']}")
            
            # 优先选择内置麦克风或外接麦克风
            name_lower = device['name'].lower()
            if any(keyword in name_lower for keyword in ['microphone', 'mic', 'built-in', 'internal']):
                microphone_devices.insert(0, i)  # 优先级高的放前面
            elif 'blackhole' not in name_lower:
                microphone_devices.append(i)
    
    return microphone_devices

class MicrophoneTranslatorSystem(AITranslatorSystem):
    """使用麦克风的翻译系统"""
    
    def __init__(self, obs_password="", microphone_device_id=None):
        # 不调用父类的__init__，而是重新初始化
        self.obs_password = obs_password
        self.microphone_device_id = microphone_device_id
        
        # 初始化组件
        self._init_components()
    
    def _init_components(self):
        """初始化系统组件"""
        from obs_ai_translator_main import OBSController
        import queue
        import threading
        import time

        # 初始化OBS控制器
        self.obs_controller = OBSController(self.obs_password)

        # 初始化简化的翻译器
        self.translator = SimpleMicrophoneTranslator()

        # 初始化音频路由器（使用麦克风）
        self.audio_router = MicrophoneAudioRouter(
            microphone_device_id=self.microphone_device_id,
            callback=self._audio_callback
        )

        # 任务队列
        self.task_queue = queue.Queue()
        self.is_running = False
    
    def _audio_callback(self, audio_data, volume):
        """音频回调"""
        # 这里可以添加音频处理逻辑
        pass
    
    def start(self):
        """启动系统"""
        print("=== AI同声传译系统 (麦克风模式) ===")
        print(f"使用麦克风设备: {sd.query_devices(self.microphone_device_id)['name']}")
        
        try:
            # 连接OBS
            if self.obs_controller.connect():
                print("✅ 已连接到OBS")
            else:
                print("⚠️ 未连接到OBS，将跳过字幕显示")
            
            # 启动翻译器
            self.translator.start_worker()
            
            # 启动音频捕获
            self.audio_router.start_capture()
            
        except KeyboardInterrupt:
            print("\n正在停止系统...")
            self.stop()
        except Exception as e:
            print(f"系统错误: {e}")
            self.stop()
    
    def stop(self):
        """停止系统"""
        self.is_running = False
        if hasattr(self, 'audio_router'):
            self.audio_router.stop_capture()
        if hasattr(self, 'translator'):
            self.translator.stop_worker()

class SimpleMicrophoneTranslator:
    """简化的麦克风翻译器"""

    def __init__(self):
        self.is_running = False
        self.worker_thread = None

    def start_worker(self):
        """启动工作线程"""
        self.is_running = True
        print("✅ 翻译器已启动")

    def stop_worker(self):
        """停止工作线程"""
        self.is_running = False
        print("翻译器已停止")

class MicrophoneAudioRouter:
    """麦克风音频路由器"""
    
    def __init__(self, microphone_device_id, callback=None):
        self.microphone_device_id = microphone_device_id
        self.callback = callback
        self.is_running = False
        
        # 音频参数
        self.sample_rate = 44100
        self.channels = 1
        self.blocksize = 1024
    
    def start_capture(self):
        """开始音频捕获"""
        import numpy as np
        import time
        
        print(f"开始从麦克风捕获音频...")
        print(f"设备: {sd.query_devices(self.microphone_device_id)['name']}")
        print(f"采样率: {self.sample_rate} Hz")
        print(f"通道数: {self.channels}")
        print()
        print("💡 提示: 现在只会捕获您的语音，不会捕获系统播放的翻译")
        print("按Ctrl+C停止...")
        
        def audio_callback(indata, frames, time, status):
            if status:
                print(f"音频状态: {status}")
            
            # 计算音量
            volume = np.sqrt(np.mean(indata**2))
            
            # 显示音量条
            if volume > 0.001:
                bars = int(volume * 50)
                print(f"\r音量: {'█' * bars:<20} {volume:.3f}", end='', flush=True)
            
            # 调用外部回调
            if self.callback:
                self.callback(indata, volume)
        
        self.is_running = True
        
        try:
            with sd.InputStream(
                device=self.microphone_device_id,
                channels=self.channels,
                samplerate=self.sample_rate,
                blocksize=self.blocksize,
                dtype='float32',
                callback=audio_callback
            ):
                while self.is_running:
                    time.sleep(0.1)
        except Exception as e:
            print(f"音频捕获错误: {e}")
            self.is_running = False
    
    def stop_capture(self):
        """停止音频捕获"""
        self.is_running = False

def main():
    """主函数"""
    print("AI翻译系统 - 麦克风模式")
    print("=" * 40)
    
    # 查找麦克风设备
    microphone_devices = find_microphone_device()
    
    if not microphone_devices:
        print("❌ 没有找到合适的麦克风设备")
        return
    
    # 选择设备
    if len(microphone_devices) == 1:
        device_id = microphone_devices[0]
        print(f"自动选择设备: {sd.query_devices(device_id)['name']}")
    else:
        print(f"\n找到 {len(microphone_devices)} 个麦克风设备:")
        for i, device_id in enumerate(microphone_devices):
            print(f"{i}: {sd.query_devices(device_id)['name']}")
        
        while True:
            try:
                choice = int(input(f"请选择设备 (0-{len(microphone_devices)-1}): "))
                if 0 <= choice < len(microphone_devices):
                    device_id = microphone_devices[choice]
                    break
                else:
                    print("无效选择")
            except ValueError:
                print("请输入数字")
    
    # 获取OBS密码
    obs_password = input("请输入OBS WebSocket密码(可留空): ").strip()
    
    # 创建并启动系统
    system = MicrophoneTranslatorSystem(obs_password, device_id)
    system.start()

if __name__ == "__main__":
    main()
