#!/usr/bin/env python3
"""
音频设备选择工具
帮助用户选择正确的音频输入设备
"""
import sounddevice as sd
import numpy as np
import time

def list_audio_devices():
    """列出所有音频设备"""
    print("=== 可用音频设备 ===")
    devices = sd.query_devices()
    
    input_devices = []
    
    for i, device in enumerate(devices):
        device_type = []
        if device['max_input_channels'] > 0:
            device_type.append("输入")
            input_devices.append(i)
        if device['max_output_channels'] > 0:
            device_type.append("输出")
        
        type_str = "/".join(device_type) if device_type else "无"
        
        print(f"{i:2d}: {device['name']:<40} ({type_str}) - {device['max_input_channels']}入/{device['max_output_channels']}出")
    
    return input_devices

def test_audio_device(device_id, duration=5):
    """测试音频设备"""
    print(f"\n测试设备 {device_id}: {sd.query_devices(device_id)['name']}")
    print(f"请对着麦克风说话，测试 {duration} 秒...")
    print("音量条: ", end="", flush=True)
    
    max_volume = 0
    
    def audio_callback(indata, frames, time, status):
        nonlocal max_volume
        if status:
            print(f"状态: {status}")
        
        volume = np.sqrt(np.mean(indata**2))
        max_volume = max(max_volume, volume)
        
        # 显示音量条
        bars = int(volume * 50)
        print(f"\r音量条: {'█' * bars:<20} {volume:.4f}", end="", flush=True)
    
    try:
        with sd.InputStream(
            device=device_id,
            channels=1,
            samplerate=44100,
            blocksize=1024,
            dtype='float32',
            callback=audio_callback
        ):
            time.sleep(duration)
        
        print(f"\n测试完成！最大音量: {max_volume:.4f}")
        
        if max_volume > 0.01:
            print("✅ 设备工作正常，检测到音频输入")
            return True
        else:
            print("⚠️ 设备可能没有音频输入或音量太低")
            return False
            
    except Exception as e:
        print(f"\n❌ 设备测试失败: {e}")
        return False

def main():
    """主函数"""
    print("AI翻译系统 - 音频设备选择工具")
    print("=" * 50)
    
    # 列出设备
    input_devices = list_audio_devices()
    
    if not input_devices:
        print("❌ 没有找到可用的音频输入设备")
        return
    
    print(f"\n找到 {len(input_devices)} 个音频输入设备")
    print("\n推荐设备类型:")
    print("🎤 内置麦克风 - 用于捕获您的语音")
    print("🎧 外接麦克风 - 更好的音质")
    print("⚠️ BlackHole - 会捕获系统所有音频(包括翻译语音)")
    
    while True:
        print(f"\n请选择要测试的设备 (0-{len(sd.query_devices())-1}), 或输入 'q' 退出:")
        
        try:
            choice = input("设备ID: ").strip()
            
            if choice.lower() == 'q':
                break
            
            device_id = int(choice)
            
            if device_id < 0 or device_id >= len(sd.query_devices()):
                print("❌ 无效的设备ID")
                continue
            
            device = sd.query_devices(device_id)
            if device['max_input_channels'] == 0:
                print("❌ 该设备不支持音频输入")
                continue
            
            # 测试设备
            if test_audio_device(device_id):
                print(f"\n✅ 推荐使用设备 {device_id}: {device['name']}")
                print(f"在翻译系统中使用此设备ID: {device_id}")
            
            print("\n" + "="*50)
            
        except ValueError:
            print("❌ 请输入有效的数字")
        except KeyboardInterrupt:
            print("\n\n用户中断测试")
            break
        except Exception as e:
            print(f"❌ 错误: {e}")
    
    print("\n使用说明:")
    print("1. 选择麦克风设备(不是BlackHole)")
    print("2. 在翻译系统中指定该设备ID")
    print("3. 这样系统只会捕获您的语音，不会捕获翻译输出")

if __name__ == "__main__":
    main()
