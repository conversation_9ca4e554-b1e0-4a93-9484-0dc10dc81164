#!/usr/bin/env python3
"""
Simplified Freeze Reproduction Test
专门重现 "输入一两句对话之后，整个应用就卡住不work了" 的问题
"""

import sys
import time
import threading
import numpy as np
import logging
from datetime import datetime

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('simple_freeze_test.log')
    ]
)

class SimpleAudioGenerator:
    """简单音频数据生成器"""
    
    @staticmethod
    def generate_speech(duration_seconds: float, sample_rate: int = 48000) -> np.ndarray:
        """生成模拟语音数据"""
        samples = int(duration_seconds * sample_rate)
        t = np.linspace(0, duration_seconds, samples)
        
        # 生成基础正弦波（模拟语音）
        frequency = 200 + 50 * np.sin(2 * np.pi * 2 * t)  # 变化频率
        signal = 0.3 * np.sin(2 * np.pi * frequency * t)
        
        # 添加随机噪声
        noise = 0.05 * np.random.normal(0, 1, samples)
        signal += noise
        
        # 音量包络
        envelope = 0.5 * (1 + np.sin(2 * np.pi * 0.5 * t))
        signal *= envelope
        
        return signal.astype(np.float32)

class FreezeMonitor:
    """冻结监控器"""
    
    def __init__(self, timeout_seconds: int = 30):
        self.timeout_seconds = timeout_seconds
        self.last_activity = time.time()
        self.monitoring = False
        self.freeze_detected = False
        
    def update_activity(self):
        """更新最后活动时间"""
        self.last_activity = time.time()
        
    def start_monitoring(self):
        """开始监控"""
        self.monitoring = True
        self.monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
        self.monitor_thread.start()
        
    def _monitor_loop(self):
        """监控循环"""
        while self.monitoring:
            current_time = time.time()
            inactive_duration = current_time - self.last_activity
            
            if inactive_duration > self.timeout_seconds:
                if not self.freeze_detected:
                    self.freeze_detected = True
                    logging.error(f"检测到可能的系统冻结！无活动时间: {inactive_duration:.1f}秒")
                    self._log_freeze_info()
            
            time.sleep(1)
    
    def _log_freeze_info(self):
        """记录冻结信息"""
        try:
            active_threads = threading.active_count()
            logging.error(f"冻结时活动线程数: {active_threads}")
            
            for thread in threading.enumerate():
                logging.error(f"线程: {thread.name}, 存活: {thread.is_alive()}, 守护: {thread.daemon}")
                
        except Exception as e:
            logging.error(f"记录冻结信息失败: {e}")

def test_consecutive_inputs():
    """测试连续输入，重现冻结问题"""
    logging.info("=== 简化冻结重现测试 ===")
    
    # 启动冻结监控
    freeze_monitor = FreezeMonitor(timeout_seconds=20)
    freeze_monitor.start_monitoring()
    
    try:
        # 导入并创建翻译系统
        from obs_ai_translator_mic import MicrophoneTranslatorSystem
        
        logging.info("创建翻译系统实例...")
        system = MicrophoneTranslatorSystem(
            obs_password="",
            enable_tts=False  # 禁用TTS简化测试
        )
        
        freeze_monitor.update_activity()
        
        # 模拟用户报告的使用模式：输入一两句对话
        logging.info("开始模拟连续对话输入...")
        
        for round_num in range(1, 4):  # 测试3轮对话
            logging.info(f"--- 第 {round_num} 句对话 ---")
            
            # 生成不同长度的语音
            if round_num == 1:
                speech_duration = 2.0  # 2秒短句
                logging.info("模拟短句: 你好")
            elif round_num == 2:
                speech_duration = 3.5  # 3.5秒中等长度句子
                logging.info("模拟中等句子: 今天天气怎么样")
            else:
                speech_duration = 4.0  # 4秒长句
                logging.info("模拟长句: 我想了解一下这个翻译系统的工作原理")
            
            # 生成并输入音频数据
            audio_data = SimpleAudioGenerator.generate_speech(speech_duration)
            
            # 分块输入音频（模拟实时音频流）
            chunk_size = 1024
            total_chunks = len(audio_data) // chunk_size + 1
            
            logging.info(f"开始输入音频数据，共 {total_chunks} 个块...")
            
            for i in range(total_chunks):
                start_idx = i * chunk_size
                end_idx = min(start_idx + chunk_size, len(audio_data))
                chunk = audio_data[start_idx:end_idx]
                
                if len(chunk) > 0:
                    # 输入音频块
                    system.translator.process_audio(chunk)
                    freeze_monitor.update_activity()
                    
                    # 检查是否冻结
                    if freeze_monitor.freeze_detected:
                        logging.error(f"在第 {round_num} 句话的第 {i} 个音频块时检测到冻结！")
                        return False
                
                # 模拟实时音频流的时间间隔
                time.sleep(0.02)  # 20ms间隔
            
            logging.info(f"第 {round_num} 句话音频输入完成")
            
            # 添加句间停顿（模拟用户说话间隔）
            logging.info("添加句间停顿...")
            time.sleep(1.5)  # 1.5秒停顿
            
            freeze_monitor.update_activity()
            
            # 检查系统状态
            logging.info("检查队列状态...")
            rec_queue_size = system.translator.recognition_queue.qsize()
            trans_queue_size = system.translator.translation_queue.qsize()
            tts_queue_size = system.translator.tts_queue.qsize()
            
            logging.info(f"队列状态 - ASR: {rec_queue_size}, 翻译: {trans_queue_size}, TTS: {tts_queue_size}")
            
            # 如果队列堆积太多，可能是处理跟不上输入
            if rec_queue_size > 5:
                logging.warning(f"ASR队列堆积严重: {rec_queue_size}")
            
            if trans_queue_size > 3:
                logging.warning(f"翻译队列堆积: {trans_queue_size}")
        
        # 等待所有处理完成
        logging.info("等待所有处理完成...")
        time.sleep(10)
        
        freeze_monitor.update_activity()
        
        if not freeze_monitor.freeze_detected:
            logging.info("✅ 测试完成，未检测到冻结")
            return True
        else:
            logging.error("❌ 检测到系统冻结")
            return False
            
    except Exception as e:
        logging.error(f"测试过程中发生异常: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        freeze_monitor.monitoring = False

def test_reset_functionality():
    """测试重置功能是否能解决冻结问题"""
    logging.info("=== 测试重置功能 ===")
    
    try:
        from obs_ai_translator_mic import MicrophoneTranslatorSystem
        
        system = MicrophoneTranslatorSystem(
            obs_password="",
            enable_tts=False
        )
        
        # 先产生一些处理负载
        logging.info("产生处理负载...")
        audio_data = SimpleAudioGenerator.generate_speech(3.0)
        
        # 快速输入大量数据
        chunk_size = 512
        for i in range(0, len(audio_data), chunk_size):
            chunk = audio_data[i:i+chunk_size]
            if len(chunk) > 0:
                system.translator.process_audio(chunk)
            time.sleep(0.01)  # 很短的间隔
        
        # 检查队列状态
        logging.info("负载产生后的队列状态:")
        logging.info(f"  ASR队列: {system.translator.recognition_queue.qsize()}")
        logging.info(f"  翻译队列: {system.translator.translation_queue.qsize()}")
        logging.info(f"  TTS队列: {system.translator.tts_queue.qsize()}")
        
        # 执行重置
        logging.info("执行系统重置...")
        system.translator.reset_system()
        
        time.sleep(2)  # 等待重置完成
        
        # 检查重置后的状态
        logging.info("重置后的队列状态:")
        logging.info(f"  ASR队列: {system.translator.recognition_queue.qsize()}")
        logging.info(f"  翻译队列: {system.translator.translation_queue.qsize()}")
        logging.info(f"  TTS队列: {system.translator.tts_queue.qsize()}")
        
        # 测试重置后是否能正常工作
        logging.info("测试重置后的功能...")
        test_audio = SimpleAudioGenerator.generate_speech(1.0)
        system.translator.process_audio(test_audio[:1024])
        
        time.sleep(2)
        
        logging.info("✅ 重置功能测试完成")
        return True
        
    except Exception as e:
        logging.error(f"重置功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def analyze_queue_behavior():
    """分析队列行为，查找潜在的死锁问题"""
    logging.info("=== 分析队列行为 ===")
    
    try:
        from obs_ai_translator_mic import MicrophoneTranslatorSystem
        from queue import Queue
        import threading
        
        system = MicrophoneTranslatorSystem(
            obs_password="",
            enable_tts=False
        )
        
        # 监控队列状态的函数
        def monitor_queues():
            monitor_duration = 20  # 监控20秒
            start_time = time.time()
            
            while time.time() - start_time < monitor_duration:
                rec_size = system.translator.recognition_queue.qsize()
                trans_size = system.translator.translation_queue.qsize()
                tts_size = system.translator.tts_queue.qsize()
                
                current_time = time.time() - start_time
                logging.info(f"[{current_time:.1f}s] 队列状态: ASR={rec_size}, 翻译={trans_size}, TTS={tts_size}")
                
                # 检查队列是否满了
                if system.translator.recognition_queue.full():
                    logging.warning("ASR队列已满！")
                if system.translator.translation_queue.full():
                    logging.warning("翻译队列已满！")
                if system.translator.tts_queue.full():
                    logging.warning("TTS队列已满！")
                
                time.sleep(1)
        
        # 启动队列监控线程
        monitor_thread = threading.Thread(target=monitor_queues, daemon=True)
        monitor_thread.start()
        
        # 输入音频数据，观察队列行为
        logging.info("开始输入音频数据...")
        for i in range(5):
            audio_data = SimpleAudioGenerator.generate_speech(2.0)
            
            logging.info(f"输入第 {i+1} 段音频...")
            chunk_size = 1024
            for j in range(0, len(audio_data), chunk_size):
                chunk = audio_data[j:j+chunk_size]
                if len(chunk) > 0:
                    system.translator.process_audio(chunk)
                time.sleep(0.02)
            
            # 段与段之间的间隔
            time.sleep(2)
        
        # 等待监控完成
        monitor_thread.join()
        
        logging.info("✅ 队列行为分析完成")
        return True
        
    except Exception as e:
        logging.error(f"队列行为分析失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("AI翻译系统 - 简化冻结重现测试")
    print("目标：重现 '输入一两句对话之后，整个应用就卡住不work了' 的问题")
    print("开始测试...\n")
    
    test_results = []
    
    # 测试1: 连续输入测试
    print("【测试1】连续输入测试")
    try:
        result1 = test_consecutive_inputs()
        test_results.append(("连续输入测试", result1))
        print(f"结果: {'通过' if result1 else '失败'}\n")
    except Exception as e:
        logging.error(f"连续输入测试异常: {e}")
        test_results.append(("连续输入测试", False))
        print("结果: 异常\n")
    
    time.sleep(3)  # 恢复间隔
    
    # 测试2: 重置功能测试
    print("【测试2】重置功能测试")
    try:
        result2 = test_reset_functionality()
        test_results.append(("重置功能测试", result2))
        print(f"结果: {'通过' if result2 else '失败'}\n")
    except Exception as e:
        logging.error(f"重置功能测试异常: {e}")
        test_results.append(("重置功能测试", False))
        print("结果: 异常\n")
    
    time.sleep(3)  # 恢复间隔
    
    # 测试3: 队列行为分析
    print("【测试3】队列行为分析")
    try:
        result3 = analyze_queue_behavior()
        test_results.append(("队列行为分析", result3))
        print(f"结果: {'通过' if result3 else '失败'}\n")
    except Exception as e:
        logging.error(f"队列行为分析异常: {e}")
        test_results.append(("队列行为分析", False))
        print("结果: 异常\n")
    
    # 总结
    print("=" * 50)
    print("测试总结:")
    print("=" * 50)
    
    passed = sum(1 for _, result in test_results if result)
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
    
    print(f"\n总体结果: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("✅ 所有测试通过，在当前条件下未重现冻结问题")
    else:
        print("⚠️  部分测试失败，可能成功重现了冻结问题")
        print("请查看日志文件 simple_freeze_test.log 获取详细信息")
    
    return 0 if passed == total else 1

if __name__ == "__main__":
    sys.exit(main())