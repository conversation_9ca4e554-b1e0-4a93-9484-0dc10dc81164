#!/usr/bin/env python3
"""
直接测试TTS音频播放
"""
import asyncio
import edge_tts
import sounddevice as sd
import numpy as np
from pydub import AudioSegment
import io

def find_best_output_device():
    """查找最佳的音频输出设备"""
    try:
        devices = sd.query_devices()
        
        # 优先级顺序：AirPods > 扬声器 > 其他输出设备
        priority_keywords = [
            ['AirPods', 'airpods'],           # AirPods最优先
            ['扬声器', 'Speaker', 'speakers'], # 内置扬声器
            ['HDR', 'Display'],               # 外部显示器
        ]
        
        for priority_group in priority_keywords:
            for i, device in enumerate(devices):
                if device['max_output_channels'] > 0:
                    device_name = device['name'].lower()
                    for keyword in priority_group:
                        if keyword.lower() in device_name:
                            print(f"[音频] 选择输出设备: {device['name']} (ID: {i})")
                            return i
        
        # 如果没找到特定设备，使用第一个可用的输出设备（非虚拟）
        for i, device in enumerate(devices):
            if (device['max_output_channels'] > 0 and 
                not any(virt in device['name'].lower() 
                       for virt in ['blackhole', 'vb-cable', 'ai同传', '多输出'])):
                print(f"[音频] 使用备选输出设备: {device['name']} (ID: {i})")
                return i
        
        # 最后使用系统默认（None表示默认设备）
        print("[音频] 使用系统默认输出设备")
        return None
        
    except Exception as e:
        print(f"[音频] 查找输出设备错误: {e}")
        return None

async def test_tts_playback():
    """测试TTS音频播放"""
    print("=== TTS音频播放测试 ===")
    
    test_text = "Hello, this is a test of the text-to-speech audio playback system."
    voice = "en-US-JennyNeural"
    
    print(f"合成文本: {test_text}")
    print("正在合成...")
    
    # 使用Edge-TTS合成
    communicate = edge_tts.Communicate(test_text, voice)
    audio_data = b""
    
    async for chunk in communicate.stream():
        if chunk["type"] == "audio":
            audio_data += chunk["data"]
    
    print(f"合成完成，音频数据大小: {len(audio_data)} bytes")
    
    # 转换音频格式
    try:
        # 从MP3转换
        audio_segment = AudioSegment.from_mp3(io.BytesIO(audio_data))
        
        # 转换为48kHz立体声
        audio_segment = audio_segment.set_frame_rate(48000).set_channels(2)
        
        # 转换为numpy数组
        audio_np = np.array(audio_segment.get_array_of_samples(), dtype=np.float32)
        audio_np = audio_np.reshape((-1, 2))  # 立体声
        audio_np = audio_np / 32768.0  # 归一化
        
        print(f"音频转换完成: {audio_np.shape}, 时长: {len(audio_np)/48000:.2f}秒")
        
        # 查找最佳输出设备
        playback_device = find_best_output_device()
        
        # 播放音频
        print(f"播放音频到设备 {playback_device}...")
        sd.play(audio_np, 48000, device=playback_device)
        sd.wait()  # 等待播放完成
        
        print("✓ TTS音频播放完成")
        return True
        
    except Exception as e:
        print(f"❌ 音频处理/播放错误: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    result = asyncio.run(test_tts_playback())
    
    if result:
        print("\n✅ TTS音频播放系统工作正常")
    else:
        print("\n❌ TTS音频播放系统有问题")
        
    print("\n现在你应该听到了英文语音。如果没有听到，可能的问题:")
    print("1. AirPods连接问题")
    print("2. 音量设置问题") 
    print("3. 音频格式转换问题")
    print("4. 设备选择问题")