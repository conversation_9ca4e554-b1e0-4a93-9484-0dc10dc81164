#!/usr/bin/env python3
"""
测试TTS音频输出
"""
import asyncio
import edge_tts
import sounddevice as sd
import numpy as np
from pydub import AudioSegment
import io

async def test_tts_output():
    """测试TTS音频输出"""
    print("=== 测试TTS音频输出 ===")
    
    # 测试文本
    test_text = "Hello, this is a test of the AI translation system."
    
    print(f"合成语音: {test_text}")
    
    # 使用Edge-TTS合成
    voice = "en-US-JennyNeural"
    communicate = edge_tts.Communicate(test_text, voice)
    
    audio_data = b""
    async for chunk in communicate.stream():
        if chunk["type"] == "audio":
            audio_data += chunk["data"]
    
    print(f"TTS音频数据大小: {len(audio_data)} bytes")
    
    # 转换音频格式
    try:
        # 从MP3转换
        audio_segment = AudioSegment.from_mp3(io.BytesIO(audio_data))
        print(f"原始音频: {audio_segment.frame_rate}Hz, {audio_segment.channels}ch, 时长{len(audio_segment)}ms")
        
        # 转换为48kHz立体声
        audio_segment = audio_segment.set_frame_rate(48000).set_channels(2)
        
        # 转换为numpy数组
        audio_np = np.array(audio_segment.get_array_of_samples(), dtype=np.float32)
        audio_np = audio_np.reshape((-1, 2))  # 立体声
        audio_np = audio_np / 32768.0  # 归一化
        
        print(f"转换后: 48000Hz, 2ch, shape={audio_np.shape}")
        
        # 播放到默认扬声器
        print("播放到扬声器...")
        sd.play(audio_np, 48000)
        sd.wait()  # 等待播放完成
        
        print("✓ 音频播放完成")
        
        return audio_np
        
    except Exception as e:
        print(f"❌ 音频处理错误: {e}")
        return None

def test_audio_devices():
    """测试可用的音频设备"""
    print("\n=== 可用音频设备 ===")
    devices = sd.query_devices()
    
    for i, device in enumerate(devices):
        device_type = []
        if device['max_input_channels'] > 0:
            device_type.append('输入')
        if device['max_output_channels'] > 0:
            device_type.append('输出')
        
        print(f"{i}: {device['name']} ({'/'.join(device_type)})")
        print(f"   采样率: {device['default_samplerate']} Hz")
        
    print(f"\n默认输入设备: {sd.default.device[0]}")
    print(f"默认输出设备: {sd.default.device[1]}")

if __name__ == "__main__":
    # 测试设备
    test_audio_devices()
    
    # 测试TTS输出
    asyncio.run(test_tts_output())