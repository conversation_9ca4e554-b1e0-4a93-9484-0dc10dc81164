#!/usr/bin/env python3
"""
实时监控翻译系统状态，帮助诊断卡死问题
"""
import time
import threading
import psutil
import os
import signal
from datetime import datetime

class TranslatorMonitor:
    def __init__(self, check_interval=2):
        self.check_interval = check_interval
        self.is_monitoring = True
        self.translator_process = None
        self.last_log_time = time.time()
        self.log_file = "translator_monitor.log"
        
    def log(self, message):
        """记录日志"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_msg = f"[{timestamp}] {message}"
        print(log_msg)
        
        # 写入日志文件
        try:
            with open(self.log_file, "a", encoding="utf-8") as f:
                f.write(log_msg + "\n")
        except:
            pass
    
    def find_translator_process(self):
        """查找翻译器进程"""
        for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
            try:
                cmdline = proc.info['cmdline']
                if cmdline and any('run_fixed_translator.py' in cmd or 'obs_ai_translator_main.py' in cmd for cmd in cmdline):
                    return proc
            except:
                continue
        return None
    
    def check_system_resources(self):
        """检查系统资源使用情况"""
        try:
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            
            self.log(f"系统资源 - CPU: {cpu_percent:.1f}%, 内存: {memory.percent:.1f}%")
            
            if cpu_percent > 80:
                self.log("⚠️  CPU使用率过高!")
            if memory.percent > 80:
                self.log("⚠️  内存使用率过高!")
                
        except Exception as e:
            self.log(f"资源检查失败: {e}")
    
    def check_translator_process(self):
        """检查翻译器进程状态"""
        proc = self.find_translator_process()
        
        if not proc:
            self.log("❌ 未找到翻译器进程")
            return False
        
        try:
            # 检查进程状态
            status = proc.status()
            cpu_percent = proc.cpu_percent()
            memory_info = proc.memory_info()
            
            self.log(f"翻译器进程 PID:{proc.pid} 状态:{status} CPU:{cpu_percent:.1f}% 内存:{memory_info.rss/1024/1024:.1f}MB")
            
            # 检查是否卡死
            if status == 'sleeping' and cpu_percent < 0.1:
                self.log("🔍 进程可能处于等待状态")
            elif status == 'running' and cpu_percent > 50:
                self.log("⚠️  进程CPU使用率异常高")
            
            # 检查线程
            threads = proc.threads()
            self.log(f"线程数: {len(threads)}")
            
            return True
            
        except psutil.NoSuchProcess:
            self.log("❌ 翻译器进程已退出")
            return False
        except Exception as e:
            self.log(f"进程检查失败: {e}")
            return False
    
    def check_audio_devices(self):
        """检查音频设备状态"""
        try:
            import sounddevice as sd
            devices = sd.query_devices()
            
            blackhole_found = False
            for i, device in enumerate(devices):
                if 'BlackHole' in device['name']:
                    blackhole_found = True
                    break
            
            if blackhole_found:
                self.log("✅ BlackHole设备正常")
            else:
                self.log("❌ BlackHole设备未找到")
                
        except Exception as e:
            self.log(f"音频设备检查失败: {e}")
    
    def check_network_connectivity(self):
        """检查网络连接（OpenAI API）"""
        try:
            import urllib.request
            import socket
            
            # 设置短超时
            socket.setdefaulttimeout(5)
            
            # 测试OpenAI API连接
            try:
                response = urllib.request.urlopen('https://api.openai.com', timeout=5)
                self.log("✅ OpenAI API连接正常")
            except:
                self.log("❌ OpenAI API连接失败")
            
            # 测试一般网络连接
            try:
                response = urllib.request.urlopen('https://www.google.com', timeout=5)
                self.log("✅ 网络连接正常")
            except:
                self.log("❌ 网络连接失败")
                
        except Exception as e:
            self.log(f"网络检查失败: {e}")
    
    def monitor_loop(self):
        """主监控循环"""
        self.log("=== 翻译器监控启动 ===")
        self.log(f"监控间隔: {self.check_interval}秒")
        self.log(f"日志文件: {self.log_file}")
        
        consecutive_failures = 0
        
        while self.is_monitoring:
            try:
                self.log("\n--- 系统检查 ---")
                
                # 检查翻译器进程
                if self.check_translator_process():
                    consecutive_failures = 0
                else:
                    consecutive_failures += 1
                    if consecutive_failures >= 3:
                        self.log("❌ 翻译器进程连续3次检查失败，可能已崩溃")
                
                # 检查系统资源
                self.check_system_resources()
                
                # 每10次检查一次音频和网络（减少频率）
                if int(time.time()) % (self.check_interval * 5) == 0:
                    self.check_audio_devices()
                    self.check_network_connectivity()
                
                time.sleep(self.check_interval)
                
            except KeyboardInterrupt:
                self.log("用户中断监控")
                break
            except Exception as e:
                self.log(f"监控循环错误: {e}")
                time.sleep(self.check_interval)
        
        self.log("=== 监控结束 ===")
    
    def start_monitoring(self):
        """启动监控"""
        monitor_thread = threading.Thread(target=self.monitor_loop, daemon=True)
        monitor_thread.start()
        return monitor_thread

def main():
    print("=== 翻译器实时监控工具 ===")
    print("此工具将监控翻译器进程状态，帮助诊断卡死问题")
    print()
    print("使用方法:")
    print("1. 在一个终端运行此监控工具")
    print("2. 在另一个终端运行翻译器")
    print("3. 观察监控输出，找出卡死的时间点和状态")
    print()
    
    monitor = TranslatorMonitor(check_interval=3)
    
    try:
        monitor.monitor_loop()
    except KeyboardInterrupt:
        print("\n监控已停止")

if __name__ == "__main__":
    main()
